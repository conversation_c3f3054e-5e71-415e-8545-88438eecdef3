<x-app-layout>
    <x-slot name="header">
        <div class="d-flex justify-content-between align-items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('المصروفات') }}
            </h2>
            <div>
                <a href="{{ route('expenses.export') }}" class="btn btn-success me-2">
                    <i class="fas fa-file-export"></i> {{ __('تصدير') }}
                </a>
                <a href="{{ route('expenses.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> {{ __('إضافة مصروف جديد') }}
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="container-fluid">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    <!-- Summary Cards -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <h5 class="card-title">{{ __('إجمالي المصروفات') }}</h5>
                                    <h3 class="mb-0">{{ number_format($totalExpenses, 2) }}</h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <h5 class="card-title">{{ __('مصروفات اليوم') }}</h5>
                                    <h3 class="mb-0">{{ number_format($todayExpenses, 2) }}</h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <h5 class="card-title">{{ __('مصروفات الشهر') }}</h5>
                                    <h3 class="mb-0">{{ number_format($monthExpenses, 2) }}</h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <h5 class="card-title">{{ __('عدد المصروفات') }}</h5>
                                    <h3 class="mb-0">{{ $expensesCount }}</h3>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Create Button -->
                    <div class="mb-4">
                        <a href="{{ route('expenses.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> {{ __('إضافة مصروف جديد') }}
                        </a>
                    </div>

                    <!-- Filters -->
                    <form action="{{ route('expenses.index') }}" method="GET" class="mb-4">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="search">{{ __('بحث') }}</label>
                                    <input type="text" name="search" id="search" class="form-control" value="{{ request('search') }}" placeholder="{{ __('الوصف') }}">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="category">{{ __('التصنيف') }}</label>
                                    <select name="category" id="category" class="form-select">
                                        <option value="">{{ __('الكل') }}</option>
                                        @foreach($categories as $category)
                                            <option value="{{ $category->id }}" {{ request('category') == $category->id ? 'selected' : '' }}>
                                                {{ $category->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="branch">{{ __('الفرع') }}</label>
                                    <select name="branch" id="branch" class="form-select">
                                        <option value="">{{ __('الكل') }}</option>
                                        @foreach($branches as $branch)
                                            <option value="{{ $branch->id }}" {{ request('branch') == $branch->id ? 'selected' : '' }}>
                                                {{ $branch->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="date_range">{{ __('الفترة') }}</label>
                                    <input type="text" name="date_range" id="date_range" class="form-control" value="{{ request('date_range') }}" placeholder="{{ __('اختر الفترة') }}">
                                </div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12 text-start">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i> {{ __('بحث') }}
                                </button>
                                <a href="{{ route('expenses.index') }}" class="btn btn-secondary">
                                    <i class="fas fa-redo"></i> {{ __('إعادة تعيين') }}
                                </a>
                            </div>
                        </div>
                    </form>

                    <!-- Expenses Table -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead>
                                <tr>
                                    <th>{{ __('التاريخ') }}</th>
                                    <th>{{ __('المرجع') }}</th>
                                    <th>{{ __('التصنيف') }}</th>
                                    <th>{{ __('الفرع') }}</th>
                                    <th>{{ __('الوصف') }}</th>
                                    <th>{{ __('المبلغ') }}</th>
                                    <th>{{ __('طريقة الدفع') }}</th>
                                    <th>{{ __('المستخدم') }}</th>
                                    <th>{{ __('الحالة') }}</th>
                                    <th>{{ __('الإجراءات') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($expenses as $expense)
                                    <tr>
                                        <td>{{ $expense->expense_date->format('Y-m-d') }}</td>
                                        <td>{{ $expense->reference }}</td>
                                        <td>
                                            <span class="badge" style="background-color: {{ $expense->category->color ?: '#6c757d' }}">
                                                {{ $expense->category->name }}
                                            </span>
                                        </td>
                                        <td>{{ $expense->branch->name }}</td>
                                        <td>{{ $expense->description }}</td>
                                        <td>{{ number_format($expense->amount, 2) }}</td>
                                        <td>{{ $expense->payment_method }}</td>
                                        <td>{{ $expense->user->name }}</td>
                                        <td>
                                            @if($expense->status == 'pending')
                                                <span class="badge bg-warning">{{ __('قيد الانتظار') }}</span>
                                            @elseif($expense->status == 'approved')
                                                <span class="badge bg-success">{{ __('تمت الموافقة') }}</span>
                                            @elseif($expense->status == 'rejected')
                                                <span class="badge bg-danger">{{ __('مرفوض') }}</span>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="btn-group">
                                                <a href="{{ route('expenses.show', $expense) }}" class="btn btn-info btn-sm" title="{{ __('عرض') }}">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{{ route('expenses.edit', $expense) }}" class="btn btn-primary btn-sm" title="{{ __('تعديل') }}">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <form action="{{ route('expenses.destroy', $expense) }}" method="POST" class="d-inline">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('{{ __('هل أنت متأكد من حذف هذا المصروف؟') }}')" title="{{ __('حذف') }}">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="10" class="text-center">{{ __('لا توجد مصروفات') }}</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="mt-4">
                        {{ $expenses->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>

    @push('styles')
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css" />
    <style>
        .rtl {
            direction: rtl;
            text-align: right;
        }
        .rtl .form-select {
            text-align: right;
        }
        .rtl .table th,
        .rtl .table td {
            text-align: right;
        }
        .rtl .btn-group {
            flex-direction: row-reverse;
        }
        .rtl .btn-group .btn {
            margin-right: 0;
            margin-left: 0.25rem;
        }
        .rtl .text-start {
            text-align: right !important;
        }
        .rtl .text-end {
            text-align: left !important;
        }
        .rtl .form-control {
            text-align: right;
        }
        .rtl .form-control::placeholder {
            text-align: right;
        }
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .card-body {
            padding: 1.5rem;
        }
        .card-title {
            font-size: 1rem;
            margin-bottom: 0.5rem;
        }
        .badge {
            padding: 0.5em 0.75em;
        }
    </style>
    @endpush

    @push('scripts')
    <script type="text/javascript" src="https://cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
    <script>
        $(document).ready(function() {
            $('#date_range').daterangepicker({
                locale: {
                    format: 'YYYY-MM-DD',
                    applyLabel: 'تطبيق',
                    cancelLabel: 'إلغاء',
                    fromLabel: 'من',
                    toLabel: 'إلى',
                    customRangeLabel: 'مخصص',
                    daysOfWeek: ['أحد', 'اثن', 'ثلا', 'أرب', 'خمي', 'جمع', 'سبت'],
                    monthNames: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر']
                }
            });
        });
    </script>
    @endpush
</x-app-layout>
