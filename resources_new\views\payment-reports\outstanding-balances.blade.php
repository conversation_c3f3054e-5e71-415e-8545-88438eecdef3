@extends('layouts.app')

@section('title', 'تقرير الأرصدة المعلقة')

@section('content')
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-0 text-gray-800">تقرير الأرصدة المعلقة</h1>
                <p class="text-muted mb-0">عرض تفصيلي للمشتريات ذات الأرصدة المعلقة</p>
            </div>
            <div>
                <a href="{{ user_route('payment-reports.index') }}" class="btn btn-outline-secondary me-2">
                    <i class="fas fa-arrow-right me-2"></i>العودة للتقارير
                </a>
                <a href="{{ user_route('payment-reports.export', ['type' => 'outstanding']) }}" class="btn btn-success">
                    <i class="fas fa-file-excel me-2"></i>تصدير
                </a>
            </div>
        </div>

        <!-- Filters and Summary -->
        <div class="row mb-4">
            <div class="col-lg-8">
                <div class="card shadow">
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-6">
                                <label class="form-label">المورد</label>
                                <select name="supplier_id" class="form-select">
                                    <option value="">جميع الموردين</option>
                                    @foreach ($suppliers as $supplier)
                                        <option value="{{ $supplier->id }}"
                                            {{ $supplierId == $supplier->id ? 'selected' : '' }}>
                                            {{ $supplier->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">ترتيب حسب</label>
                                <select name="sort_by" class="form-select">
                                    <option value="remaining_amount" {{ $sortBy == 'remaining_amount' ? 'selected' : '' }}>
                                        المبلغ المتبقي</option>
                                    <option value="purchase_date" {{ $sortBy == 'purchase_date' ? 'selected' : '' }}>تاريخ
                                        الشراء</option>
                                    <option value="total_amount" {{ $sortBy == 'total_amount' ? 'selected' : '' }}>إجمالي
                                        المبلغ</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">الترتيب</label>
                                <select name="sort_order" class="form-select">
                                    <option value="desc" {{ $sortOrder == 'desc' ? 'selected' : '' }}>تنازلي</option>
                                    <option value="asc" {{ $sortOrder == 'asc' ? 'selected' : '' }}>تصاعدي</option>
                                </select>
                            </div>
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-2"></i>تطبيق الفلاتر
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="card border-left-danger shadow h-100">
                    <div class="card-body">
                        <div class="text-center">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                إجمالي الأرصدة المعلقة
                            </div>
                            <div class="h4 mb-0 font-weight-bold text-gray-800">
                                {{ format_currency($totalOutstanding) }}
                            </div>
                            <div class="text-muted mt-2">
                                {{ $purchases->total() }} عملية شراء
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Outstanding Balances Table -->
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">الأرصدة المعلقة</h6>
            </div>
            <div class="card-body">
                @if ($purchases->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th class="border-0 fw-bold">رقم العملية</th>
                                    <th class="border-0 fw-bold">المورد</th>
                                    <th class="border-0 fw-bold">الفرع</th>
                                    <th class="border-0 fw-bold">تاريخ الشراء</th>
                                    <th class="border-0 fw-bold">إجمالي المبلغ</th>
                                    <th class="border-0 fw-bold">المبلغ المدفوع</th>
                                    <th class="border-0 fw-bold">المبلغ المتبقي</th>
                                    <th class="border-0 fw-bold text-center">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($purchases as $purchase)
                                    <tr class="border-bottom">
                                        <td>
                                            <span class="fw-bold text-primary">#{{ $purchase->id }}</span>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-circle bg-primary text-white me-2">
                                                    {{ substr($purchase->supplier->name, 0, 1) }}
                                                </div>
                                                <div>
                                                    <div class="fw-bold">{{ $purchase->supplier->name }}</div>
                                                    @if ($purchase->supplier->phone)
                                                        <small class="text-muted">{{ $purchase->supplier->phone }}</small>
                                                    @endif
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">{{ $purchase->branch->name ?? 'غير محدد' }}</span>
                                        </td>
                                        <td>
                                            <div class="fw-bold">{{ $purchase->purchase_date->format('Y-m-d') }}</div>
                                            <small
                                                class="text-muted">{{ $purchase->purchase_date->diffForHumans() }}</small>
                                        </td>
                                        <td>
                                            <span
                                                class="fw-bold text-info">{{ format_currency($purchase->total_amount) }}</span>
                                        </td>
                                        <td>
                                            <span
                                                class="fw-bold text-success">{{ format_currency($purchase->paid_amount) }}</span>
                                        </td>
                                        <td>
                                            <span
                                                class="fw-bold text-danger">{{ format_currency($purchase->actual_remaining_amount) }}</span>
                                            <div class="progress mt-1" style="height: 4px;">
                                                <div class="progress-bar bg-success"
                                                    style="width: {{ ($purchase->paid_amount / $purchase->total_amount) * 100 }}%">
                                                </div>
                                            </div>
                                        </td>
                                        <td class="text-center">
                                            <div class="btn-group" role="group">
                                                <a href="{{ user_route('purchases.show', $purchase) }}"
                                                    class="btn btn-outline-info btn-sm" title="عرض التفاصيل">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{{ user_route('supplier-payments.create', $purchase) }}"
                                                    class="btn btn-outline-warning btn-sm" title="دفع مبلغ">
                                                    <i class="fas fa-money-bill-wave"></i>
                                                </a>
                                                <a href="{{ user_route('supplier-payments.show', $purchase) }}"
                                                    class="btn btn-outline-secondary btn-sm" title="تاريخ المدفوعات">
                                                    <i class="fas fa-history"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                            <tfoot class="table-light">
                                <tr>
                                    <th colspan="6" class="text-end fw-bold text-danger">إجمالي المبالغ المتبقية:</th>
                                    <th class="fw-bold text-danger">{{ format_currency($totalOutstanding) }}</th>
                                    <th></th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-center mt-4">
                        {{ $purchases->appends(request()->query())->links() }}
                    </div>
                @else
                    <div class="text-center py-5">
                        <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                        <h5 class="text-success">لا توجد أرصدة معلقة</h5>
                        <p class="text-muted">جميع المشتريات مدفوعة بالكامل</p>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <style>
        .avatar-circle {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 12px;
        }

        .border-left-danger {
            border-left: 0.25rem solid #e74a3b !important;
        }

        .btn-group .btn {
            margin-left: 2px;
        }

        .btn-group .btn:first-child {
            margin-left: 0;
        }

        .table-hover tbody tr:hover {
            background-color: rgba(0, 0, 0, 0.02);
        }
    </style>
@endsection
