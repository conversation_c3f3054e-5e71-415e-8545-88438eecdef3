<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Account extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'code',
        'type',
        'opening_balance',
        'current_balance',
        'is_active',
    ];

    protected $casts = [
        'opening_balance' => 'decimal:2',
        'current_balance' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    public function accountable(): MorphTo
    {
        return $this->morphTo();
    }

    public function transactions(): HasMany
    {
        return $this->hasMany(AccountTransaction::class);
    }

    public function fromTransactions(): HasMany
    {
        return $this->hasMany(AccountTransaction::class, 'from_account_id');
    }

    public function toTransactions(): HasMany
    {
        return $this->hasMany(AccountTransaction::class, 'to_account_id');
    }

    public function deposit(float $amount, string $description = null, Model $transactionable = null)
    {
        $transaction = $this->transactions()->create([
            'reference_number' => 'DEP-' . uniqid(),
            'type' => 'deposit',
            'amount' => $amount,
            'balance_before' => $this->current_balance,
            'balance_after' => $this->current_balance + $amount,
            'description' => $description,
            'user_id' => auth()->id(),
        ] + ($transactionable ? [
            'transactionable_type' => get_class($transactionable),
            'transactionable_id' => $transactionable->id,
        ] : []));

        $this->update(['current_balance' => $this->current_balance + $amount]);

        return $transaction;
    }

    public function withdraw(float $amount, string $description = null, Model $transactionable = null)
    {
        if ($this->current_balance < $amount) {
            throw new \Exception('Insufficient balance');
        }

        $transaction = $this->transactions()->create([
            'reference_number' => 'WTH-' . uniqid(),
            'type' => 'withdrawal',
            'amount' => $amount,
            'balance_before' => $this->current_balance,
            'balance_after' => $this->current_balance - $amount,
            'description' => $description,
            'user_id' => auth()->id(),
        ] + ($transactionable ? [
            'transactionable_type' => get_class($transactionable),
            'transactionable_id' => $transactionable->id,
        ] : []));

        $this->update(['current_balance' => $this->current_balance - $amount]);

        return $transaction;
    }

    public function credit(float $amount, string $description = null, Model $transactionable = null)
    {
        $transaction = $this->transactions()->create([
            'reference_number' => 'CRD-' . uniqid(),
            'type' => 'credit',
            'amount' => $amount,
            'balance_before' => $this->current_balance,
            'balance_after' => $this->current_balance + $amount,
            'description' => $description,
            'user_id' => auth()->id(),
        ] + ($transactionable ? [
            'transactionable_type' => get_class($transactionable),
            'transactionable_id' => $transactionable->id,
        ] : []));

        $this->update(['current_balance' => $this->current_balance + $amount]);

        return $transaction;
    }

    public function getBalance(): float
    {
        return (float) $this->current_balance;
    }

    public function getRemainingBalance(): float
    {
        return $this->getBalance();
    }

    public function isCustomerAccount(): bool
    {
        return $this->type === 'customer';
    }

    public function isSupplierAccount(): bool
    {
        return $this->type === 'supplier';
    }

    public function getOwedAmount(): float
    {
        if ($this->isCustomerAccount()) {
            // For customers, negative balance means they owe money
            return $this->current_balance < 0 ? abs($this->current_balance) : 0;
        }

        if ($this->isSupplierAccount()) {
            // For suppliers, positive balance means we owe them money
            return $this->current_balance > 0 ? $this->current_balance : 0;
        }

        return 0;
    }

    public function getCreditAmount(): float
    {
        if ($this->isCustomerAccount()) {
            // For customers, positive balance means they have credit
            return $this->current_balance > 0 ? $this->current_balance : 0;
        }

        if ($this->isSupplierAccount()) {
            // For suppliers, negative balance means they owe us money
            return $this->current_balance < 0 ? abs($this->current_balance) : 0;
        }

        return 0;
    }



    public function transfer(float $amount, Account $toAccount, string $description = null, Model $transactionable = null)
    {
        if ($this->current_balance < $amount) {
            throw new \Exception('Insufficient balance');
        }

        $this->withdraw($amount, "Transfer to {$toAccount->name}: {$description}", $transactionable);
        $toAccount->deposit($amount, "Transfer from {$this->name}: {$description}", $transactionable);
    }
}
