@extends('layouts.app')

@section('title', 'تاريخ المدفوعات')

@section('content')
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-0 text-gray-800">تاريخ المدفوعات</h1>
                <p class="text-muted mb-0">سجل تفصيلي لجميع المدفوعات المسجلة</p>
            </div>
            <div>
                <a href="{{ user_route('payment-reports.index') }}" class="btn btn-outline-secondary me-2">
                    <i class="fas fa-arrow-right me-2"></i>العودة للتقارير
                </a>
                <a href="{{ user_route('payment-reports.export', array_merge(['type' => 'payments'], request()->query())) }}"
                    class="btn btn-success">
                    <i class="fas fa-file-csv me-2"></i>تصدير
                </a>
            </div>
        </div>

        <!-- Filters and Summary -->
        <div class="row mb-4">
            <div class="col-lg-8">
                <div class="card shadow">
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-3">
                                <label class="form-label">من تاريخ</label>
                                <input type="date" name="date_from" class="form-control" value="{{ $dateFrom }}">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">إلى تاريخ</label>
                                <input type="date" name="date_to" class="form-control" value="{{ $dateTo }}">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">المورد</label>
                                <select name="supplier_id" class="form-select">
                                    <option value="">جميع الموردين</option>
                                    @foreach ($suppliers as $supplier)
                                        <option value="{{ $supplier->id }}"
                                            {{ $supplierId == $supplier->id ? 'selected' : '' }}>
                                            {{ $supplier->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <button type="submit" class="btn btn-primary d-block w-100">
                                    <i class="fas fa-search me-2"></i>بحث
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="card border-left-success shadow h-100">
                    <div class="card-body">
                        <div class="text-center">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                إجمالي المدفوعات
                            </div>
                            <div class="h4 mb-0 font-weight-bold text-gray-800">
                                {{ format_currency($totalAmount) }}
                            </div>
                            <div class="text-muted mt-2">
                                {{ $payments->total() }} عملية دفع
                            </div>
                            <div class="text-muted small">
                                {{ $dateFrom }} إلى {{ $dateTo }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Payment History Table -->
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">سجل المدفوعات</h6>
            </div>
            <div class="card-body">
                @if ($payments->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th class="border-0 fw-bold">رقم المرجع</th>
                                    <th class="border-0 fw-bold">رقم العملية</th>
                                    <th class="border-0 fw-bold">المورد</th>
                                    <th class="border-0 fw-bold">المبلغ</th>
                                    <th class="border-0 fw-bold">طريقة الدفع</th>
                                    <th class="border-0 fw-bold">تاريخ الدفع</th>
                                    <th class="border-0 fw-bold">المستخدم</th>
                                    <th class="border-0 fw-bold text-center">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($payments as $payment)
                                    <tr class="border-bottom">
                                        <td>
                                            <span class="badge bg-secondary">{{ $payment->reference_number }}</span>
                                        </td>
                                        <td>
                                            <a href="{{ user_route('purchases.show', $payment->transactionable) }}"
                                                class="fw-bold text-primary text-decoration-none">
                                                {{ $payment->transactionable->invoice_number }}
                                            </a>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-circle bg-primary text-white me-2">
                                                    {{ substr($payment->transactionable->supplier->name, 0, 1) }}
                                                </div>
                                                <div>
                                                    <div class="fw-bold">{{ $payment->transactionable->supplier->name }}
                                                    </div>
                                                    @if ($payment->transactionable->supplier->phone)
                                                        <small
                                                            class="text-muted">{{ $payment->transactionable->supplier->phone }}</small>
                                                    @endif
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span
                                                class="fw-bold text-success fs-6">{{ format_currency($payment->amount) }}</span>
                                        </td>
                                        <td>
                                            @php
                                                $methodColors = [
                                                    'cash' => 'success',
                                                    'bank_transfer' => 'info',
                                                    'check' => 'warning',
                                                    'credit_card' => 'primary',
                                                    'other' => 'secondary',
                                                ];
                                                $methodLabels = [
                                                    'cash' => 'نقدي',
                                                    'bank_transfer' => 'تحويل بنكي',
                                                    'check' => 'شيك',
                                                    'credit_card' => 'بطاقة ائتمان',
                                                    'other' => 'أخرى',
                                                ];
                                            @endphp
                                            <span
                                                class="badge bg-{{ $methodColors[$payment->payment_method] ?? 'secondary' }}">
                                                {{ $methodLabels[$payment->payment_method] ?? $payment->payment_method }}
                                            </span>
                                            @if ($payment->payment_reference)
                                                <div class="small text-muted mt-1">{{ $payment->payment_reference }}</div>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="fw-bold">{{ $payment->created_at->format('Y-m-d') }}</div>
                                            <small class="text-muted">{{ $payment->created_at->format('H:i') }}</small>
                                            <div class="small text-muted">{{ $payment->created_at->diffForHumans() }}</div>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-circle bg-info text-white me-2">
                                                    {{ substr($payment->user->name ?? 'غير محدد', 0, 1) }}
                                                </div>
                                                <div>
                                                    <div class="fw-bold small">{{ $payment->user->name ?? 'غير محدد' }}
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="text-center">
                                            <div class="btn-group" role="group">
                                                <a href="{{ user_route('purchases.show', $payment->transactionable) }}"
                                                    class="btn btn-outline-info btn-sm" title="عرض العملية">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{{ user_route('supplier-payments.show', $payment->transactionable) }}"
                                                    class="btn btn-outline-secondary btn-sm" title="تاريخ المدفوعات">
                                                    <i class="fas fa-history"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                            <tfoot class="table-light">
                                <tr>
                                    <th colspan="3" class="text-end fw-bold text-success">إجمالي المدفوعات:</th>
                                    <th class="fw-bold text-success">{{ format_currency($totalAmount) }}</th>
                                    <th colspan="4"></th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-center mt-4">
                        {{ $payments->appends(request()->query())->links() }}
                    </div>
                @else
                    <div class="text-center py-5">
                        <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد مدفوعات</h5>
                        <p class="text-muted">لم يتم العثور على مدفوعات في الفترة المحددة</p>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <style>
        .avatar-circle {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 12px;
        }

        .border-left-success {
            border-left: 0.25rem solid #1cc88a !important;
        }

        .btn-group .btn {
            margin-left: 2px;
        }

        .btn-group .btn:first-child {
            margin-left: 0;
        }

        .table-hover tbody tr:hover {
            background-color: rgba(0, 0, 0, 0.02);
        }
    </style>
@endsection
