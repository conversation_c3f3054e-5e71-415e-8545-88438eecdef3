<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SaleReturn extends Model
{
    use HasFactory;

    protected $fillable = [
        'return_number',
        'sale_id',
        'customer_id',
        'branch_id',
        'store_id',
        'user_id',
        'total_amount',
        'refund_amount',
        'status',
        'return_type',
        'reason',
        'notes',
        'return_date',
        'approved_at',
        'approved_by'
    ];

    protected $casts = [
        'total_amount' => 'decimal:2',
        'refund_amount' => 'decimal:2',
        'return_date' => 'date',
        'approved_at' => 'datetime'
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->return_number)) {
                $today = date('Y-m-d');
                $dailyCount = static::whereDate('created_at', $today)->count() + 1;
                $model->return_number = 'RET-' . date('Ymd') . '-' . str_pad($dailyCount, 3, '0', STR_PAD_LEFT);
            }
        });
    }

    // Relationships
    public function sale()
    {
        return $this->belongsTo(Sale::class);
    }

    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }

    public function store()
    {
        return $this->belongsTo(Store::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function approvedBy()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    public function items()
    {
        return $this->hasMany(SaleReturnItem::class);
    }

    public function transactions()
    {
        return $this->morphMany(AccountTransaction::class, 'transactionable');
    }

    // Business Logic Methods
    public function canBeEdited(): bool
    {
        // Since returns are now auto-completed, they generally cannot be edited
        // Only allow editing if somehow a return is still pending (edge case)
        return $this->status === 'pending';
    }

    public function canBeApproved(): bool
    {
        return $this->status === 'pending';
    }

    public function canBeCompleted(): bool
    {
        return in_array($this->status, ['pending', 'approved']) && $this->items->count() > 0;
    }

    public function canBeCancelled(): bool
    {
        return in_array($this->status, ['pending', 'approved']);
    }

    public function approve(): bool
    {
        if (!$this->canBeApproved()) {
            return false;
        }

        $this->update([
            'status' => 'approved',
            'approved_at' => now(),
            'approved_by' => auth()->id()
        ]);

        return true;
    }

    public function complete(): bool
    {
        // Allow completion even if status is already 'completed' for direct creation
        if (!in_array($this->status, ['pending', 'approved', 'completed'])) {
            return false;
        }

        // Process inventory adjustments
        foreach ($this->items as $item) {
            $this->adjustInventoryForItem($item);
        }

        // Process customer account refund if applicable
        if ($this->refund_amount > 0 && $this->customer) {
            $this->processCustomerRefund();
        }

        // Update status only if not already completed
        if ($this->status !== 'completed') {
            $this->update(['status' => 'completed']);
        }

        return true;
    }

    public function cancel(?string $reason = null): bool
    {
        if (!$this->canBeCancelled()) {
            return false;
        }

        $this->update([
            'status' => 'cancelled',
            'notes' => $this->notes . ($reason ? "\nCancellation reason: " . $reason : '')
        ]);

        return true;
    }

    protected function adjustInventoryForItem(SaleReturnItem $item): void
    {
        if ($item->inventory_adjusted) {
            return; // Already adjusted
        }

        // Return items to inventory based on the original sale location
        $sale = $this->sale;

        if ($sale->branch_id) {
            // Return to branch inventory
            $inventory = BranchInventory::where('branch_id', $sale->branch_id)
                ->where('product_id', $item->product_id)
                ->first();

            if ($inventory) {
                $inventory->increment('quantity', $item->quantity_returned);
            } else {
                // Create inventory record if it doesn't exist
                BranchInventory::create([
                    'branch_id' => $sale->branch_id,
                    'product_id' => $item->product_id,
                    'quantity' => $item->quantity_returned,
                    'min_quantity' => 0,
                    'max_quantity' => null,
                ]);
            }
        } elseif ($sale->store_id) {
            // Return to store inventory
            $inventory = StoreInventory::where('store_id', $sale->store_id)
                ->where('product_id', $item->product_id)
                ->first();

            if ($inventory) {
                $inventory->increment('quantity', $item->quantity_returned);
            } else {
                // Create inventory record if it doesn't exist
                StoreInventory::create([
                    'store_id' => $sale->store_id,
                    'product_id' => $item->product_id,
                    'quantity' => $item->quantity_returned,
                    'min_quantity' => 0,
                    'max_quantity' => null,
                ]);
            }
        }

        $item->update(['inventory_adjusted' => true]);
    }

    protected function processCustomerRefund(): void
    {
        if (!$this->customer || !$this->customer->account) {
            return;
        }

        // Credit customer account with refund amount
        $this->customer->account->credit(
            $this->refund_amount,
            "مرتجع مبيعات رقم {$this->return_number}",
            $this
        );
    }

    // Status helpers
    public function getStatusLabelAttribute(): string
    {
        return match ($this->status) {
            'pending' => 'قيد الانتظار',
            'approved' => 'موافق عليه',
            'completed' => 'مكتمل',
            'cancelled' => 'ملغي',
            default => 'غير محدد'
        };
    }

    public function getStatusColorAttribute(): string
    {
        return match ($this->status) {
            'pending' => 'warning',
            'approved' => 'info',
            'completed' => 'success',
            'cancelled' => 'danger',
            default => 'secondary'
        };
    }

    public function getReturnTypeLabelAttribute(): string
    {
        return match ($this->return_type) {
            'full' => 'مرتجع كامل',
            'partial' => 'مرتجع جزئي',
            default => 'غير محدد'
        };
    }

    public function getReasonLabelAttribute(): string
    {
        return match ($this->reason) {
            'damaged' => 'منتج تالف',
            'wrong_item' => 'منتج خاطئ',
            'expired' => 'منتهي الصلاحية',
            'customer_request' => 'طلب العميل',
            'quality_issue' => 'مشكلة في الجودة',
            'defective' => 'عيب في التصنيع',
            'other' => 'أخرى',
            default => $this->reason ?? 'غير محدد'
        };
    }

    // Additional helper methods
    public function getTotalQuantityReturnedAttribute(): float
    {
        return $this->items->sum('quantity_returned');
    }

    public function getReturnPercentageAttribute(): float
    {
        if ($this->sale->total_amount <= 0) {
            return 0;
        }
        return ($this->total_amount / $this->sale->total_amount) * 100;
    }

    public function isFullReturn(): bool
    {
        return $this->return_type === 'full';
    }

    public function isPartialReturn(): bool
    {
        return $this->return_type === 'partial';
    }

    public function hasRefund(): bool
    {
        return $this->refund_amount > 0;
    }

    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    public function isApproved(): bool
    {
        return $this->status === 'approved';
    }

    public function isCancelled(): bool
    {
        return $this->status === 'cancelled';
    }

    public function getFormattedTotalAmountAttribute(): string
    {
        return number_format($this->total_amount, 2) . ' ج.م';
    }

    public function getFormattedRefundAmountAttribute(): string
    {
        return number_format($this->refund_amount, 2) . ' ج.م';
    }

    public function getDaysFromReturnAttribute(): int
    {
        return $this->return_date->diffInDays(now());
    }

    public function getLocationNameAttribute(): string
    {
        if ($this->branch) {
            return $this->branch->name;
        } elseif ($this->store) {
            return $this->store->name;
        }
        return 'غير محدد';
    }

    public function validateReturnQuantities(): array
    {
        $errors = [];

        foreach ($this->items as $item) {
            $saleItem = $item->saleItem;
            if (!$saleItem) {
                $errors[] = "عنصر المبيعات غير موجود للمنتج {$item->product->name}";
                continue;
            }

            // Check if return quantity exceeds original quantity
            if ($item->quantity_returned > $saleItem->quantity) {
                $errors[] = "كمية المرتجع للمنتج {$item->product->name} تتجاوز الكمية الأصلية";
            }

            // Check if total returns for this item exceed original quantity
            $totalReturned = $saleItem->returnItems()
                ->whereHas('saleReturn', function ($q) {
                    $q->where('status', '!=', 'cancelled')
                        ->where('id', '!=', $this->id);
                })
                ->sum('quantity_returned');

            if (($totalReturned + $item->quantity_returned) > $saleItem->quantity) {
                $errors[] = "إجمالي المرتجعات للمنتج {$item->product->name} يتجاوز الكمية الأصلية";
            }
        }

        return $errors;
    }

    public function canBeCompletedWithValidation(): array
    {
        $errors = [];

        if (!$this->canBeCompleted()) {
            $errors[] = 'لا يمكن إكمال هذا المرتجع في حالته الحالية';
        }

        $quantityErrors = $this->validateReturnQuantities();
        $errors = array_merge($errors, $quantityErrors);

        return $errors;
    }
}
