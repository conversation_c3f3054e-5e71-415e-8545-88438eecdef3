<x-app-layout>
    <x-slot name="header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                    <i class="fas fa-plus-circle text-primary"></i> {{ __('إضافة منتجات متعددة') }}
                </h2>
                <p class="text-muted small mb-0">إضافة عدة منتجات دفعة واحدة لنفس الفئة</p>
            </div>
            <div class="d-flex gap-2">
                <a href="{{ route('admin.products.index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right"></i> العودة للقائمة
                </a>
            </div>
        </div>
    </x-slot>

    <div class="container-fluid py-4">
        <div class="row">
            <!-- Instructions Section -->
            <div class="col-xl-4 col-lg-5 mb-4">
                <div class="card shadow border-0 h-100">
                    <div class="card-header bg-gradient-info text-white py-3">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-info-circle fa-lg me-3"></i>
                            <h5 class="mb-0 fw-bold">تعليمات الاستخدام</h5>
                        </div>
                    </div>
                    <div class="card-body p-4">
                        <div class="alert alert-info border-0 mb-4">
                            <h6 class="alert-heading fw-bold">
                                <i class="fas fa-lightbulb me-2"></i>نصائح مهمة
                            </h6>
                            <ul class="mb-0 small">
                                <li>اختر الفئة أولاً قبل إضافة المنتجات</li>
                                <li>اسم المنتج مطلوب لكل منتج</li>
                                <li>يمكنك إضافة منتجات متعددة بسهولة</li>
                                <li>استخدم زر "+" لإضافة صف جديد</li>
                                <li>استخدم زر "🗑" لحذف الصف</li>
                            </ul>
                        </div>

                        <div class="mb-3">
                            <h6 class="fw-bold text-primary">
                                <i class="fas fa-list me-2"></i>الحقول المتاحة:
                            </h6>
                            <ul class="list-unstyled small">
                                <li><strong>اسم المنتج:</strong> مطلوب</li>
                                <li><strong>الوصف:</strong> اختياري</li>
                                <li><strong>سعر التكلفة:</strong> اختياري</li>
                                <li><strong>سعر البيع:</strong> اختياري</li>
                            </ul>
                        </div>

                        {{-- <div class="alert alert-warning border-0">
                            <small>
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                سيتم تجاهل الصفوف الفارغة تلقائياً
                            </small>
                        </div> --}}
                    </div>
                </div>
            </div>

            <!-- Form Section -->
            <div class="col-xl-8 col-lg-7">
                <div class="card shadow border-0">
                    <div class="card-header bg-gradient-primary text-white py-3">
                        <div class="d-flex align-items-center justify-content-between">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-edit fa-lg me-3"></i>
                                <h5 class="mb-0 fw-bold">بيانات المنتجات</h5>
                            </div>
                            <button type="button" class="btn btn-light btn-sm" onclick="addProductRow()">
                                <i class="fas fa-plus"></i> إضافة صف
                            </button>
                        </div>
                    </div>
                    <div class="card-body p-4">
                        <form action="{{ route('admin.products.bulk.store') }}" method="POST" id="bulk-product-form">
                            @csrf

                            <!-- Category Selection -->
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <label for="category_id" class="form-label fw-bold">
                                        <i class="fas fa-tags text-primary me-2"></i>الفئة
                                        <span class="text-danger">*</span>
                                    </label>
                                    <select name="category_id" id="category_id"
                                        class="form-select @error('category_id') is-invalid @enderror" required>
                                        <option value="">اختر الفئة...</option>
                                        @foreach ($categories as $category)
                                            <option value="{{ $category->id }}"
                                                {{ old('category_id') == $category->id ? 'selected' : '' }}>
                                                {{ $category->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('category_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- Products Table -->
                            <div class="table-responsive">
                                <table class="table table-bordered" id="products-table">
                                    <thead class="table-primary">
                                        <tr>
                                            <th style="width: 25%">اسم المنتج <span class="text-danger">*</span></th>
                                            <th style="width: 30%">الوصف</th>
                                            <th style="width: 15%">سعر التكلفة</th>
                                            <th style="width: 15%">سعر البيع</th>
                                            <th style="width: 15%">إجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="products-tbody">
                                        @for ($i = 0; $i < 1; $i++)
                                            <tr class="product-row">
                                                <td>
                                                    <input type="text" name="products[{{ $i }}][name]"
                                                        class="form-control form-control-sm" placeholder="اسم المنتج">
                                                </td>
                                                <td>
                                                    <input type="text"
                                                        name="products[{{ $i }}][description]"
                                                        class="form-control form-control-sm" placeholder="وصف المنتج">
                                                </td>
                                                <td>
                                                    <input type="number" name="products[{{ $i }}][price]"
                                                        class="form-control form-control-sm" step="0.01"
                                                        min="0" placeholder="0.00">
                                                </td>
                                                <td>
                                                    <input type="number"
                                                        name="products[{{ $i }}][selling_price]"
                                                        class="form-control form-control-sm" step="0.01"
                                                        min="0" placeholder="0.00">
                                                </td>
                                                <td class="text-center">
                                                    <div class="btn-group" role="group">
                                                        <button type="button" class="btn btn-outline-success btn-sm"
                                                            onclick="addProductRow()" title="إضافة صف جديد">
                                                            <i class="fas fa-plus"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-outline-danger btn-sm"
                                                            onclick="removeProductRow(this)" title="حذف الصف">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        @endfor
                                    </tbody>
                                </table>
                            </div>

                            <!-- Form Actions -->
                            <div class="d-flex justify-content-between align-items-center mt-4">
                                <div>
                                    <button type="button" class="btn btn-outline-primary" onclick="addProductRow()">
                                        <i class="fas fa-plus"></i> إضافة صف آخر
                                    </button>

                                    <button type="button" class="btn btn-outline-secondary"
                                        onclick="clearAllRows()">
                                        <i class="fas fa-eraser"></i> مسح الكل
                                    </button>
                                </div>
                                <div>
                                    <button type="button" class="btn btn-outline-secondary me-2"
                                        onclick="window.history.back()">
                                        <i class="fas fa-times"></i> إلغاء
                                    </button>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i> حفظ المنتجات
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
        <script>
            let rowIndex = 5; // Start from 5 since we have 5 initial rows

            function addProductRow() {
                const tbody = document.getElementById('products-tbody');
                const newRow = document.createElement('tr');
                newRow.className = 'product-row';
                newRow.innerHTML = `
                <td>
                    <input type="text" name="products[${rowIndex}][name]"
                           class="form-control form-control-sm"
                           placeholder="اسم المنتج">
                </td>
                <td>
                    <input type="text" name="products[${rowIndex}][description]"
                           class="form-control form-control-sm"
                           placeholder="وصف المنتج">
                </td>
                <td>
                    <input type="number" name="products[${rowIndex}][price]"
                           class="form-control form-control-sm"
                           step="0.01" min="0" placeholder="0.00">
                </td>
                <td>
                    <input type="number" name="products[${rowIndex}][selling_price]"
                           class="form-control form-control-sm"
                           step="0.01" min="0" placeholder="0.00">
                </td>
                <td class="text-center">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-success btn-sm"
                                onclick="addProductRow()" title="إضافة صف جديد">
                            <i class="fas fa-plus"></i>
                        </button>
                        <button type="button" class="btn btn-outline-danger btn-sm"
                                onclick="removeProductRow(this)" title="حذف الصف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            `;
                tbody.appendChild(newRow);
                rowIndex++;
            }

            function removeProductRow(button) {
                const row = button.closest('tr');
                const tbody = document.getElementById('products-tbody');

                // Don't allow removing if it's the last row
                if (tbody.children.length > 1) {
                    row.remove();
                } else {
                    alert('يجب الاحتفاظ بصف واحد على الأقل');
                }
            }

            function clearAllRows() {
                if (confirm('هل أنت متأكد من مسح جميع البيانات؟')) {
                    const tbody = document.getElementById('products-tbody');
                    tbody.innerHTML = '';
                    rowIndex = 0;
                    addProductRow(); // Add one empty row
                }
            }



            // Form validation
            document.getElementById('bulk-product-form').addEventListener('submit', function(e) {
                const categorySelect = document.getElementById('category_id');
                if (!categorySelect.value) {
                    e.preventDefault();
                    alert('يرجى اختيار الفئة أولاً');
                    categorySelect.focus();
                    return;
                }

                // Check if at least one product has a name
                const nameInputs = document.querySelectorAll('input[name*="[name]"]');
                let hasValidProduct = false;

                nameInputs.forEach(input => {
                    if (input.value.trim()) {
                        hasValidProduct = true;
                    }
                });

                if (!hasValidProduct) {
                    e.preventDefault();
                    alert('يرجى إدخال اسم منتج واحد على الأقل');
                    return;
                }
            });
        </script>
    @endpush
</x-app-layout>
