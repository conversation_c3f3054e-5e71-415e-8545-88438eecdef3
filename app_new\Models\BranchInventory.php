<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use App\Models\User;

class BranchInventory extends Model
{
    protected $fillable = [
        'branch_id',
        'product_id',
        'quantity',
        'minimum_stock',
        'cost_price',
        'sale_price_1',
        'sale_price_2',
        'sale_price_3'
    ];
    protected $table = 'branch_inventory';

    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }

    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    // Scopes
    public function scopeAccessibleBy($query, User $user)
    {
        if ($user->canAccessAllBranches()) {
            return $query;
        }

        $accessibleBranchIds = $user->getAccessibleBranchIds();
        if (!empty($accessibleBranchIds)) {
            return $query->whereIn('branch_id', $accessibleBranchIds);
        }

        // Fallback: no accessible branches
        return $query->whereRaw('1 = 0'); // Return empty result
    }
}
