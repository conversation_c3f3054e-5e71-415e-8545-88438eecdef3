<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="h3 mb-0"> المخازن</h4>
                    
                </div>
            </div>
        </div>

        <!-- Stores Table -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <?php if($stores->count() > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>الكود</th>
                                            <th>اسم المخزن</th>
                                            <th>الفرع</th>
                                            <th>المدير</th>
                                            <th>العنوان</th>
                                            <th>الهاتف</th>
                                            <th>مخزون المخزن</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $__currentLoopData = $stores; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $store): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <tr>
                                                <td>
                                                    <span class="badge bg-secondary"><?php echo e($store->code); ?></span>
                                                </td>
                                                <td>
                                                    <div class="fw-bold"><?php echo e($store->name); ?></div>
                                                    <?php if($store->description): ?>
                                                        <small
                                                            class="text-muted"><?php echo e(Str::limit($store->description, 50)); ?></small>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <span class="badge bg-info"><?php echo e($store->branch->name); ?></span>
                                                </td>
                                                <td>
                                                    <?php if($store->manager): ?>
                                                        <div class="d-flex align-items-center">
                                                            <i class="fas fa-user-tie text-primary me-2"></i>
                                                            <?php echo e($store->manager->name); ?>

                                                        </div>
                                                    <?php else: ?>
                                                        <span class="text-muted">غير محدد</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if($store->address): ?>
                                                        <small><?php echo e(Str::limit($store->address, 30)); ?></small>
                                                    <?php else: ?>
                                                        <span class="text-muted">غير محدد</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if($store->phone): ?>
                                                        <a href="tel:<?php echo e($store->phone); ?>" class="text-decoration-none">
                                                            <i class="fas fa-phone text-success"></i>
                                                            <?php echo e($store->phone); ?>

                                                        </a>
                                                    <?php else: ?>
                                                        <span class="text-muted">غير محدد</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if(request()->routeIs('admin.*')): ?>
                                                        <a href="<?php echo e(route('admin.stores.inventory', $store)); ?>"
                                                            class="btn btn-sm btn-outline-primary d-flex align-items-center justify-content-center"
                                                            title="عرض مخزون المخزن">
                                                            <i class="fas fa-warehouse me-1"></i>
                                                            <span
                                                                class="badge bg-primary"><?php echo e($store->storeInventories->count() ?? 0); ?></span>
                                                        </a>
                                                    <?php else: ?>
                                                        <span
                                                            class="badge bg-secondary"><?php echo e($store->storeInventories->count() ?? 0); ?></span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if($store->is_active): ?>
                                                        <span class="badge bg-success">نشط</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-danger">غير نشط</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <a href="<?php echo e(user_route('stores.show', $store)); ?>"
                                                            class="btn btn-sm btn-outline-info" title="عرض">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        <a href="<?php echo e(user_route('stores.edit', $store)); ?>"
                                                            class="btn btn-sm btn-outline-primary" title="تعديل">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                        
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                            </div>

                            <!-- Pagination -->
                            <div class="d-flex justify-content-center mt-4">
                                <?php echo e($stores->withQueryString()->links()); ?>

                            </div>
                        <?php else: ?>
                            <div class="text-center py-5">
                                <i class="fas fa-store fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">لا توجد مخازن</h5>
                                <p class="text-muted">لم يتم العثور على أي مخازن مطابقة للبحث</p>
                                <a href="<?php echo e(user_route('stores.create')); ?>" class="btn btn-primary">
                                    <i class="fas fa-plus"></i> إضافة مخزن جديد
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH D:\pos-app\resources\views/stores/index.blade.php ENDPATH**/ ?>