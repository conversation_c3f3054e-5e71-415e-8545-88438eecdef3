<x-app-layout>
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="h3 mb-0">تفاصيل العميل - {{ $customer->name }}</h2>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb mb-0">
                                <li class="breadcrumb-item"><a href="{{ route('seller.dashboard') }}">لوحة التحكم</a></li>
                                <li class="breadcrumb-item"><a href="{{ route('seller.customers.index') }}">العملاء</a></li>
                                <li class="breadcrumb-item active">{{ $customer->name }}</li>
                            </ol>
                        </nav>
                    </div>
                    <div class="text-muted">
                        <i class="fas fa-store"></i> {{ auth()->user()->branch->name ?? 'غير محدد' }}
                    </div>
                </div>
            </div>
        </div>

        <!-- Customer Info and Actions -->
        <div class="row mb-4">
            <div class="col-lg-8">
                <div class="card border-0 shadow-sm">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-2 text-center">
                                <div class="avatar-lg bg-primary text-white rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3">
                                    <span class="fs-2 fw-bold">{{ strtoupper(substr($customer->name, 0, 1)) }}</span>
                                </div>
                            </div>
                            <div class="col-md-10">
                                <h4 class="mb-3">{{ $customer->name }}</h4>
                                <div class="row">
                                    <div class="col-md-6">
                                        @if($customer->phone)
                                            <div class="mb-2">
                                                <i class="fas fa-phone text-muted me-2"></i>
                                                <span>{{ $customer->phone }}</span>
                                            </div>
                                        @endif
                                        @if($customer->email)
                                            <div class="mb-2">
                                                <i class="fas fa-envelope text-muted me-2"></i>
                                                <span>{{ $customer->email }}</span>
                                            </div>
                                        @endif
                                    </div>
                                    <div class="col-md-6">
                                        @if($customer->address)
                                            <div class="mb-2">
                                                <i class="fas fa-map-marker-alt text-muted me-2"></i>
                                                <span>{{ $customer->address }}</span>
                                            </div>
                                        @endif
                                        <div class="mb-2">
                                            <i class="fas fa-calendar text-muted me-2"></i>
                                            <span>عميل منذ {{ $customer->created_at->format('Y-m-d') }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="card border-0 shadow-sm">
                    <div class="card-body text-center">
                        <h6 class="text-muted mb-3">الإجراءات السريعة</h6>
                        <div class="d-grid gap-2">
                            <a href="{{ route('seller.sales.create', ['customer_id' => $customer->id]) }}" 
                               class="btn btn-primary">
                                <i class="fas fa-plus"></i> بيع جديد
                            </a>
                            @if(($customerSummary['total_remaining'] ?? 0) > 0)
                                <a href="{{ route('seller.customer-payments.create', ['customer_id' => $customer->id]) }}" 
                                   class="btn btn-success">
                                    <i class="fas fa-money-bill"></i> تسجيل دفعة
                                </a>
                            @endif
                            <a href="{{ route('seller.customers.edit', $customer) }}" 
                               class="btn btn-outline-warning">
                                <i class="fas fa-edit"></i> تعديل البيانات
                            </a>
                            <a href="{{ route('seller.customers.index') }}" 
                               class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-right"></i> العودة للعملاء
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card border-0 shadow-sm">
                    <div class="card-body text-center">
                        <div class="bg-primary bg-gradient rounded-3 p-3 mb-3 d-inline-block">
                            <i class="fas fa-shopping-cart text-white fa-lg"></i>
                        </div>
                        <h5 class="mb-1">{{ $customerSummary['sales_count'] ?? 0 }}</h5>
                        <p class="text-muted mb-0">إجمالي الفواتير</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card border-0 shadow-sm">
                    <div class="card-body text-center">
                        <div class="bg-success bg-gradient rounded-3 p-3 mb-3 d-inline-block">
                            <i class="fas fa-chart-line text-white fa-lg"></i>
                        </div>
                        <h5 class="mb-1">{{ number_format($customerSummary['total_sales'] ?? 0, 2) }} ج.م</h5>
                        <p class="text-muted mb-0">إجمالي المشتريات</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card border-0 shadow-sm">
                    <div class="card-body text-center">
                        <div class="bg-info bg-gradient rounded-3 p-3 mb-3 d-inline-block">
                            <i class="fas fa-money-bill-wave text-white fa-lg"></i>
                        </div>
                        <h5 class="mb-1">{{ number_format($customerSummary['total_paid'] ?? 0, 2) }} ج.م</h5>
                        <p class="text-muted mb-0">المبلغ المدفوع</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card border-0 shadow-sm">
                    <div class="card-body text-center">
                        <div class="bg-warning bg-gradient rounded-3 p-3 mb-3 d-inline-block">
                            <i class="fas fa-exclamation-triangle text-white fa-lg"></i>
                        </div>
                        <h5 class="mb-1">{{ number_format($customerSummary['total_remaining'] ?? 0, 2) }} ج.م</h5>
                        <p class="text-muted mb-0">المبلغ المستحق</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Product Search -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white border-bottom">
                        <h5 class="mb-0">
                            <i class="fas fa-search text-primary"></i> البحث في منتجات العميل
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="GET" action="{{ route('seller.customers.show', $customer) }}">
                            <div class="row g-3">
                                <div class="col-md-8">
                                    <input type="text" class="form-control" name="product_search" 
                                           value="{{ $productSearch }}" 
                                           placeholder="البحث عن منتج اشتراه العميل...">
                                </div>
                                <div class="col-md-4">
                                    <div class="d-flex gap-2">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-search"></i> بحث
                                        </button>
                                        @if($productSearch)
                                            <a href="{{ route('seller.customers.show', $customer) }}" 
                                               class="btn btn-outline-secondary">
                                                <i class="fas fa-times"></i> إعادة تعيين
                                            </a>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </form>

                        @if($productSearch && $purchasedProducts->count() > 0)
                            <div class="mt-4">
                                <h6 class="mb-3">نتائج البحث: {{ $purchasedProducts->count() }} منتج</h6>
                                <div class="table-responsive">
                                    <table class="table table-bordered table-hover">
                                        <thead class="table-light">
                                            <tr>
                                                <th>المنتج</th>
                                                <th>إجمالي الكمية</th>
                                                <th>إجمالي المبلغ</th>
                                                <th>متوسط السعر</th>
                                                <th>عدد مرات الشراء</th>
                                                <th>آخر شراء</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($purchasedProducts as $item)
                                                <tr>
                                                    <td>
                                                        <strong>{{ $item->product->name }}</strong>
                                                        @if($item->product->sku)
                                                            <br><small class="text-muted">{{ $item->product->sku }}</small>
                                                        @endif
                                                    </td>
                                                    <td>{{ number_format($item->total_quantity) }}</td>
                                                    <td>{{ number_format($item->total_amount, 2) }} ج.م</td>
                                                    <td>{{ number_format($item->average_price, 2) }} ج.م</td>
                                                    <td>{{ $item->purchase_count }}</td>
                                                    <td>{{ $item->last_purchase_date->format('Y-m-d') }}</td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        @elseif($productSearch)
                            <div class="mt-4">
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle"></i> لم يتم العثور على منتجات تطابق البحث "{{ $productSearch }}"
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- Sales History -->
        @if($customer->sales->count() > 0)
            <div class="row">
                <div class="col-12">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-white border-bottom">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">
                                    <i class="fas fa-history text-primary"></i> سجل المشتريات من فرعك
                                </h5>
                                <span class="badge bg-primary fs-6">{{ $customer->sales->count() }} فاتورة</span>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th class="border-0">رقم الفاتورة</th>
                                            <th class="border-0">التاريخ</th>
                                            <th class="border-0">عدد المنتجات</th>
                                            <th class="border-0">المجموع</th>
                                            <th class="border-0">الخصم</th>
                                            <th class="border-0">المدفوع</th>
                                            <th class="border-0">المتبقي</th>
                                            <th class="border-0 text-center">الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($customer->sales->sortByDesc('created_at') as $sale)
                                            <tr>
                                                <td>
                                                    <span class="fw-semibold">{{ $sale->invoice_number }}</span>
                                                </td>
                                                <td>
                                                    <span>{{ $sale->created_at->format('Y-m-d') }}</span>
                                                    <br><small class="text-muted">{{ $sale->created_at->format('H:i') }}</small>
                                                </td>
                                                <td>
                                                    <span class="badge bg-info">{{ $sale->items->count() }} منتج</span>
                                                </td>
                                                <td>
                                                    <span class="fw-semibold">{{ number_format($sale->total_amount, 2) }} ج.م</span>
                                                </td>
                                                <td>
                                                    @if($sale->discount_amount > 0)
                                                        <span class="text-success">{{ number_format($sale->discount_amount, 2) }} ج.م</span>
                                                    @else
                                                        <span class="text-muted">-</span>
                                                    @endif
                                                </td>
                                                <td>
                                                    <span class="text-success fw-semibold">{{ number_format($sale->paid_amount, 2) }} ج.م</span>
                                                </td>
                                                <td>
                                                    @php
                                                        $remaining = ($sale->total_amount - $sale->discount_amount) - $sale->paid_amount;
                                                    @endphp
                                                    @if($remaining > 0)
                                                        <span class="badge bg-warning">{{ number_format($remaining, 2) }} ج.م</span>
                                                    @else
                                                        <span class="badge bg-success">مسدد</span>
                                                    @endif
                                                </td>
                                                <td class="text-center">
                                                    <div class="btn-group btn-group-sm">
                                                        <a href="{{ route('seller.sales.show', $sale) }}" 
                                                           class="btn btn-outline-primary" title="عرض الفاتورة">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        <a href="{{ route('seller.sales.print', $sale) }}" 
                                                           class="btn btn-outline-secondary" title="طباعة" target="_blank">
                                                            <i class="fas fa-print"></i>
                                                        </a>
                                                        @if($remaining > 0)
                                                            <a href="{{ route('seller.customer-payments.create', ['customer_id' => $customer->id, 'sale_id' => $sale->id]) }}" 
                                                               class="btn btn-outline-success" title="تسجيل دفعة">
                                                                <i class="fas fa-money-bill"></i>
                                                            </a>
                                                        @endif
                                                    </div>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @else
            <div class="row">
                <div class="col-12">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body text-center py-5">
                            <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد مشتريات</h5>
                            <p class="text-muted">لم يقم هذا العميل بأي مشتريات من فرعك بعد</p>
                            <a href="{{ route('seller.sales.create', ['customer_id' => $customer->id]) }}" 
                               class="btn btn-primary">
                                <i class="fas fa-plus"></i> إنشاء فاتورة جديدة
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        @endif
    </div>
</x-app-layout>
