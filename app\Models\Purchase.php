<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Traits\BranchScoped;
use App\Traits\StoreScoped;


class Purchase extends Model
{
    use HasFactory, SoftDeletes, BranchScoped, StoreScoped {
        StoreScoped::scopeAccessibleBy insteadof BranchScoped;
        StoreScoped::scopeForBranch insteadof BranchScoped;
        StoreScoped::scopeForBranches insteadof BranchScoped;
        StoreScoped::canBeAccessedBy insteadof BranchScoped;
        StoreScoped::getBranchId insteadof BranchScoped;
        BranchScoped::scopeAccessibleBy as scopeAccessibleByBranch;
        BranchScoped::scopeForBranch as scopeForBranchDirect;
        BranchScoped::scopeForBranches as scopeForBranchesDirect;
        BranchScoped::canBeAccessedBy as canBeAccessedByBranch;
        BranchScoped::getBranchId as getBranchIdDirect;
    }

    protected $fillable = [
        'invoice_number',
        'supplier_id',
        'branch_id',
        'store_id',
        'supplier_account_id',
        'total_amount',
        'discount_type',
        'discount_amount',
        'discount_value',
        'paid_amount',
        'remaining_amount',
        'notes',
        'status',
        'purchase_date',
        'is_distributed',
        'distributed_at',
        'distributed_by'
    ];

    protected $casts = [
        'total_amount' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'discount_value' => 'decimal:2',
        'paid_amount' => 'decimal:2',
        'remaining_amount' => 'decimal:2'
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->invoice_number)) {
                $today = date('Y-m-d');
                $dailyCount = static::whereDate('created_at', $today)->count() + 1;
                $model->invoice_number = 'PUR-' . date('Ymd') . '-' . str_pad($dailyCount, 3, '0', STR_PAD_LEFT);
            }
        });
    }

    public function supplier()
    {
        return $this->belongsTo(Supplier::class);
    }

    public function supplierAccount()
    {
        return $this->supplier?->account;
    }

    public function items()
    {
        return $this->hasMany(PurchaseItem::class);
    }

    public function transactions()
    {
        return $this->morphMany(AccountTransaction::class, 'transactionable');
    }

    /**
     * Get formatted discount display
     */
    public function getFormattedDiscountAttribute()
    {
        if ($this->discount_type === 'percentage') {
            return $this->discount_value . '%';
        }
        return format_currency($this->discount_value);
    }

    public function returns()
    {
        return $this->hasMany(PurchaseReturn::class);
    }

    public function distributedBy()
    {
        return $this->belongsTo(User::class, 'distributed_by');
    }

    /**
     * Get payment transactions for this purchase.
     */
    public function paymentTransactions()
    {
        return $this->transactions()->where('type', '!=', 'credit');
    }

    /**
     * Get the total amount paid for this purchase from account transactions.
     */
    public function getTotalPaidFromTransactionsAttribute(): float
    {
        return $this->paymentTransactions()->where('amount', '>', 0)->sum('amount');
    }

    /**
     * Get the final amount after discount.
     */
    public function getFinalAmountAttribute(): float
    {
        return $this->total_amount - ($this->discount_amount ?? 0);
    }

    /**
     * Get the actual remaining amount based on paid_amount field.
     */
    public function getActualRemainingAmountAttribute(): float
    {
        return max(0, $this->final_amount - $this->paid_amount);
    }

    /**
     * Check if the purchase is fully paid.
     */
    public function isFullyPaid(): bool
    {
        return $this->actual_remaining_amount <= 0;
    }

    /**
     * Check if the purchase has partial payments.
     */
    public function hasPartialPayments(): bool
    {
        return $this->paid_amount > 0 && !$this->isFullyPaid();
    }

    /**
     * Get payment status label.
     */
    public function getPaymentStatusAttribute(): string
    {
        if ($this->isFullyPaid()) {
            return 'مدفوع بالكامل';
        } elseif ($this->hasPartialPayments()) {
            return 'مدفوع جزئياً';
        } else {
            return 'غير مدفوع';
        }
    }

    /**
     * Get payment status color for UI.
     */
    public function getPaymentStatusColorAttribute(): string
    {
        if ($this->isFullyPaid()) {
            return 'success';
        } elseif ($this->hasPartialPayments()) {
            return 'warning';
        } else {
            return 'danger';
        }
    }

    /**
     * Make a payment for this purchase.
     */
    public function makePayment(float $amount, string $paymentMethod = 'cash', ?string $notes = null): AccountTransaction
    {
        if ($amount <= 0) {
            throw new \InvalidArgumentException('Payment amount must be greater than zero');
        }

        if ($amount > $this->actual_remaining_amount) {
            throw new \InvalidArgumentException('Payment amount cannot exceed remaining amount');
        }

        $supplierAccount = $this->supplier?->account;
        if (!$supplierAccount) {
            throw new \Exception('Supplier account not found');
        }

        // Create payment transaction
        $transaction = AccountTransaction::create([
            'account_id' => $supplierAccount->id,
            'reference_number' => 'PAY-' . $this->id . '-' . uniqid(),
            'type' => 'deposit',
            'amount' => $amount,
            'balance_before' => $supplierAccount->current_balance,
            'balance_after' => $supplierAccount->current_balance + $amount,
            'description' => $notes ?: "دفعة إضافية لعملية شراء رقم {$this->invoice_number} - طريقة الدفع: {$paymentMethod}",
            'transactionable_type' => Purchase::class,
            'transactionable_id' => $this->id,
            'user_id' => auth()->id() ?? 1, // Default to user ID 1 if no authenticated user
        ]);

        // Update supplier account balance
        $supplierAccount->update(['current_balance' => $supplierAccount->current_balance + $amount]);

        // Update purchase paid amount and remaining amount
        $newPaidAmount = $this->paid_amount + $amount;
        $newRemainingAmount = $this->final_amount - $newPaidAmount;

        $this->update([
            'paid_amount' => $newPaidAmount,
            'remaining_amount' => $newRemainingAmount
        ]);

        return $transaction;
    }
    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }

    public function store()
    {
        return $this->belongsTo(Store::class);
    }
}
