<x-app-layout>
    <div class="container-fluid px-4">
        <!-- Header Section -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div class="d-flex align-items-center">
                <div class="avatar-circle bg-primary text-white me-3 d-inline-flex">
                    <i class="fas fa-tags"></i>
                </div>
                <div>
                    <h2 class="font-semibold text-xl text-gray-800 leading-tight mb-0">
                        إدارة الفئات
                    </h2>
                    <p class="text-muted mb-0 small">إدارة فئات المنتجات وتصنيفاتها</p>
                </div>
            </div>
            <div class="d-flex gap-2">
                <button class="btn btn-outline-success" onclick="exportCategories()">
                    <i class="fas fa-file-export"></i> تصدير
                </button>
                <a href="{{ user_route('categories.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> إضافة فئة جديدة
                </a>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-primary shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                    إجمالي الفئات
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $totalCategories }}</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-tags fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-success shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                    فئات بها منتجات
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    {{ $categoriesWithProducts }}
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-box fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-warning shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                    فئات فارغة
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    {{ $emptyCategories }}
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-info shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                    إجمالي المنتجات
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    {{ $totalProducts }}
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-cubes fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content Card -->
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-list"></i> قائمة الفئات
                </h6>
                <div class="dropdown no-arrow">
                    <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink"
                        data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                    </a>
                    <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in"
                        aria-labelledby="dropdownMenuLink">
                        <div class="dropdown-header">خيارات إضافية:</div>
                        <a class="dropdown-item" href="#" onclick="printTable()">
                            <i class="fas fa-print fa-sm fa-fw mr-2 text-gray-400"></i>
                            طباعة
                        </a>
                        <a class="dropdown-item" href="#" onclick="exportCategories()">
                            <i class="fas fa-download fa-sm fa-fw mr-2 text-gray-400"></i>
                            تصدير Excel
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <!-- Enhanced Search and Filter Section -->
                <form action="{{ user_route('categories.index') }}" method="GET" class="mb-4">
                    <div class="row g-3">
                        <div class="col-md-4">
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-search text-muted"></i>
                                </span>
                                <input type="text" name="search" class="form-control"
                                    placeholder="البحث في الفئات..." value="{{ request('search') }}">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <select name="sort" class="form-select">
                                <option value="">ترتيب حسب</option>
                                <option value="name_asc" {{ request('sort') == 'name_asc' ? 'selected' : '' }}>الاسم
                                    (أ-ي)</option>
                                <option value="name_desc" {{ request('sort') == 'name_desc' ? 'selected' : '' }}>الاسم
                                    (ي-أ)</option>
                                <option value="products_count_desc"
                                    {{ request('sort') == 'products_count_desc' ? 'selected' : '' }}>عدد المنتجات
                                    (الأكثر)</option>
                                <option value="created_at_desc"
                                    {{ request('sort') == 'created_at_desc' ? 'selected' : '' }}>الأحدث</option>
                                <option value="created_at_asc"
                                    {{ request('sort') == 'created_at_asc' ? 'selected' : '' }}>الأقدم</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select name="filter" class="form-select">
                                <option value="">جميع الفئات</option>
                                <option value="with_products"
                                    {{ request('filter') == 'with_products' ? 'selected' : '' }}>فئات بها منتجات
                                </option>
                                <option value="empty" {{ request('filter') == 'empty' ? 'selected' : '' }}>فئات فارغة
                                </option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <div class="d-flex gap-1">
                                <button class="btn btn-primary" type="submit">
                                    <i class="fas fa-search"></i> بحث
                                </button>
                                <a href="{{ user_route('categories.index') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </form>

                <!-- Bulk Actions -->
                @if (auth()->user()->isAdmin())
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div class="bulk-actions" style="display: none;">
                            <div class="d-flex align-items-center gap-2">
                                <span class="text-muted small">
                                    <span id="selected-count">0</span> عنصر محدد
                                </span>
                                <button type="button" class="btn btn-sm btn-danger" onclick="bulkDelete()">
                                    <i class="fas fa-trash"></i> حذف المحدد
                                </button>
                            </div>
                        </div>
                        <div class="view-options">
                            <div class="btn-group" role="group">
                                <input type="radio" class="btn-check" name="view-mode" id="table-view" checked>
                                <label class="btn btn-outline-secondary btn-sm" for="table-view">
                                    <i class="fas fa-table"></i>
                                </label>
                                <input type="radio" class="btn-check" name="view-mode" id="grid-view">
                                <label class="btn btn-outline-secondary btn-sm" for="grid-view">
                                    <i class="fas fa-th"></i>
                                </label>
                            </div>
                        </div>
                    </div>
                @endif

                <!-- Table View -->
                <div class="table-responsive" id="table-view-content">
                    <table class="table table-hover align-middle">
                        <thead class="table-light">
                            <tr>
                                @if (auth()->user()->isAdmin())
                                    <th width="40">
                                        <input type="checkbox" id="select-all" class="form-check-input">
                                    </th>
                                @endif
                                <th>
                                    <i class="fas fa-tag text-primary me-2"></i>الفئة
                                </th>
                                <th>
                                    <i class="fas fa-align-left text-info me-2"></i>الوصف
                                </th>
                                <th>
                                    <i class="fas fa-cubes text-success me-2"></i>المنتجات
                                </th>
                                <th>
                                    <i class="fas fa-calendar text-warning me-2"></i>تاريخ الإنشاء
                                </th>
                                <th width="150">
                                    <i class="fas fa-cogs text-secondary me-2"></i>الإجراءات
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($categories as $category)
                                <tr>
                                    @if (auth()->user()->isAdmin())
                                        <td>
                                            <input type="checkbox" name="categories[]" value="{{ $category->id }}"
                                                class="form-check-input category-checkbox">
                                        </td>
                                    @endif
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-circle bg-primary text-white me-3"
                                                style="width: 40px; height: 40px;">
                                                <i class="fas fa-tag"></i>
                                            </div>
                                            <div>
                                                <h6 class="mb-0 fw-bold">{{ $category->name }}</h6>
                                                <small class="text-muted">ID: {{ $category->id }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="text-muted">
                                            {{ Str::limit($category->description ?: 'لا يوجد وصف', 50) }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            @if ($category->products_count > 0)
                                                <span
                                                    class="badge bg-success me-2">{{ $category->products_count }}</span>
                                                <small class="text-success">منتج</small>
                                            @else
                                                <span class="badge bg-warning me-2">0</span>
                                                <small class="text-warning">فارغة</small>
                                            @endif
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <div class="fw-bold">{{ $category->created_at->format('Y-m-d') }}</div>
                                            <small
                                                class="text-muted">{{ $category->created_at->diffForHumans() }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ user_route('categories.show', $category) }}"
                                                class="btn btn-sm btn-outline-info" data-bs-toggle="tooltip"
                                                title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            @if (auth()->user()->isAdmin())
                                                <a href="{{ user_route('categories.edit', $category) }}"
                                                    class="btn btn-sm btn-outline-primary" data-bs-toggle="tooltip"
                                                    title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <button type="button"
                                                    class="btn btn-sm btn-outline-danger delete-category"
                                                    data-category-id="{{ $category->id }}"
                                                    data-category-name="{{ $category->name }}"
                                                    data-products-count="{{ $category->products_count }}"
                                                    data-bs-toggle="tooltip" title="حذف"
                                                    {{ $category->products_count > 0 ? 'disabled' : '' }}>
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="{{ auth()->user()->isAdmin() ? '6' : '5' }}"
                                        class="text-center py-5">
                                        <div class="text-muted">
                                            <i class="fas fa-tags fa-3x mb-3"></i>
                                            <h5>لا توجد فئات</h5>
                                            <p>لم يتم العثور على أي فئات. ابدأ بإضافة فئة جديدة.</p>
                                            <a href="{{ user_route('categories.create') }}" class="btn btn-primary">
                                                <i class="fas fa-plus"></i> إضافة فئة جديدة
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                <!-- Grid View (Hidden by default) -->
                <div class="row" id="grid-view-content" style="display: none;">
                    @forelse($categories as $category)
                        <div class="col-xl-3 col-lg-4 col-md-6 mb-4">
                            <div class="card h-100 shadow-sm border-0">
                                <div class="card-body text-center">
                                    <div class="avatar-circle bg-primary text-white mx-auto mb-3"
                                        style="width: 60px; height: 60px;">
                                        <i class="fas fa-tag fa-2x"></i>
                                    </div>
                                    <h5 class="card-title">{{ $category->name }}</h5>
                                    <p class="card-text text-muted small">
                                        {{ Str::limit($category->description ?: 'لا يوجد وصف', 60) }}
                                    </p>
                                    <div class="d-flex justify-content-center align-items-center mb-3">
                                        @if ($category->products_count > 0)
                                            <span class="badge bg-success me-2">{{ $category->products_count }}</span>
                                            <small class="text-success">منتج</small>
                                        @else
                                            <span class="badge bg-warning me-2">0</span>
                                            <small class="text-warning">فارغة</small>
                                        @endif
                                    </div>
                                </div>
                                <div class="card-footer bg-transparent border-0">
                                    <div class="d-flex justify-content-center gap-1">
                                        <a href="{{ user_route('categories.show', $category) }}"
                                            class="btn btn-sm btn-outline-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        @if (auth()->user()->isAdmin())
                                            <a href="{{ user_route('categories.edit', $category) }}"
                                                class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button"
                                                class="btn btn-sm btn-outline-danger delete-category"
                                                data-category-id="{{ $category->id }}"
                                                data-category-name="{{ $category->name }}"
                                                data-products-count="{{ $category->products_count }}"
                                                {{ $category->products_count > 0 ? 'disabled' : '' }}>
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    @empty
                        <div class="col-12">
                            <div class="text-center py-5">
                                <i class="fas fa-tags fa-3x text-muted mb-3"></i>
                                <h5>لا توجد فئات</h5>
                                <p class="text-muted">لم يتم العثور على أي فئات. ابدأ بإضافة فئة جديدة.</p>
                                <a href="{{ user_route('categories.create') }}" class="btn btn-primary">
                                    <i class="fas fa-plus"></i> إضافة فئة جديدة
                                </a>
                            </div>
                        </div>
                    @endforelse
                </div>

                <div class="mt-4">
                    {{ $categories->links() }}
                </div>
            </div>
        </div>
    </div>
    </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteModalLabel">تأكيد الحذف</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="text-center">
                        <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                        <h5>هل أنت متأكد من حذف هذه الفئة؟</h5>
                        <p class="text-muted">
                            سيتم حذف الفئة "<span id="category-name"></span>" نهائياً ولا يمكن التراجع عن هذا الإجراء.
                        </p>
                        <div id="products-warning" class="alert alert-warning" style="display: none;">
                            <i class="fas fa-exclamation-triangle"></i>
                            هذه الفئة تحتوي على <span id="products-count"></span> منتج. لا يمكن حذفها.
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <form id="delete-form" method="POST" style="display: inline;">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-danger" id="confirm-delete">
                            <i class="fas fa-trash"></i> حذف نهائي
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    @push('styles')
        <style>
            .avatar-circle {
                width: 50px;
                height: 50px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 1.2rem;
            }

            .border-left-primary {
                border-left: 0.25rem solid #4e73df !important;
            }

            .border-left-success {
                border-left: 0.25rem solid #1cc88a !important;
            }

            .border-left-warning {
                border-left: 0.25rem solid #f6c23e !important;
            }

            .border-left-info {
                border-left: 0.25rem solid #36b9cc !important;
            }

            .table th {
                border-top: none;
                font-weight: 600;
                font-size: 0.85rem;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }

            .btn-group .btn {
                margin: 0 1px;
            }

            .card {
                transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
            }

            .card:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
            }

            .animated--fade-in {
                animation: fadeIn 0.3s ease-in-out;
            }

            @keyframes fadeIn {
                from {
                    opacity: 0;
                    transform: translateY(-10px);
                }

                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }

            .bulk-actions {
                transition: all 0.3s ease;
            }

            .table-responsive {
                border-radius: 0.375rem;
            }
        </style>
    @endpush

    @push('scripts')
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                // Initialize tooltips
                var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
                var tooltipList = tooltipTriggerList.map(function(tooltipTriggerEl) {
                    return new bootstrap.Tooltip(tooltipTriggerEl);
                });

                // View mode toggle
                const tableViewBtn = document.getElementById('table-view');
                const gridViewBtn = document.getElementById('grid-view');
                const tableContent = document.getElementById('table-view-content');
                const gridContent = document.getElementById('grid-view-content');

                if (tableViewBtn && gridViewBtn) {
                    tableViewBtn.addEventListener('change', function() {
                        if (this.checked) {
                            tableContent.style.display = 'block';
                            gridContent.style.display = 'none';
                        }
                    });

                    gridViewBtn.addEventListener('change', function() {
                        if (this.checked) {
                            tableContent.style.display = 'none';
                            gridContent.style.display = 'block';
                        }
                    });
                }

                // Select all functionality
                const selectAllCheckbox = document.getElementById('select-all');
                const categoryCheckboxes = document.querySelectorAll('.category-checkbox');
                const bulkActions = document.querySelector('.bulk-actions');
                const selectedCount = document.getElementById('selected-count');

                if (selectAllCheckbox) {
                    selectAllCheckbox.addEventListener('change', function() {
                        categoryCheckboxes.forEach(checkbox => {
                            checkbox.checked = this.checked;
                        });
                        updateBulkActions();
                    });
                }

                categoryCheckboxes.forEach(checkbox => {
                    checkbox.addEventListener('change', updateBulkActions);
                });

                function updateBulkActions() {
                    const checkedBoxes = document.querySelectorAll('.category-checkbox:checked');
                    const count = checkedBoxes.length;

                    if (selectedCount) {
                        selectedCount.textContent = count;
                    }

                    if (bulkActions) {
                        bulkActions.style.display = count > 0 ? 'block' : 'none';
                    }

                    if (selectAllCheckbox) {
                        selectAllCheckbox.indeterminate = count > 0 && count < categoryCheckboxes.length;
                        selectAllCheckbox.checked = count === categoryCheckboxes.length;
                    }
                }

                // Delete category functionality
                const deleteButtons = document.querySelectorAll('.delete-category');
                const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
                const deleteForm = document.getElementById('delete-form');
                const categoryNameSpan = document.getElementById('category-name');
                const productsWarning = document.getElementById('products-warning');
                const productsCountSpan = document.getElementById('products-count');
                const confirmDeleteBtn = document.getElementById('confirm-delete');

                deleteButtons.forEach(button => {
                    button.addEventListener('click', function() {
                        const categoryId = this.dataset.categoryId;
                        const categoryName = this.dataset.categoryName;
                        const productsCount = parseInt(this.dataset.productsCount);

                        categoryNameSpan.textContent = categoryName;

                        if (productsCount > 0) {
                            productsCountSpan.textContent = productsCount;
                            productsWarning.style.display = 'block';
                            confirmDeleteBtn.style.display = 'none';
                        } else {
                            productsWarning.style.display = 'none';
                            confirmDeleteBtn.style.display = 'inline-block';
                            deleteForm.action = `{{ user_route('categories.index') }}/${categoryId}`;
                        }

                        deleteModal.show();
                    });
                });
            });

            // Export functionality
            function exportCategories() {
                // Implement export logic here
                alert('سيتم تنفيذ وظيفة التصدير قريباً');
            }

            // Print functionality
            function printTable() {
                window.print();
            }

            // Bulk delete functionality
            function bulkDelete() {
                const checkedBoxes = document.querySelectorAll('.category-checkbox:checked');
                if (checkedBoxes.length === 0) {
                    alert('يرجى تحديد فئة واحدة على الأقل للحذف');
                    return;
                }

                if (confirm(`هل أنت متأكد من حذف ${checkedBoxes.length} فئة؟`)) {
                    // Implement bulk delete logic here
                    alert('سيتم تنفيذ وظيفة الحذف المجمع قريباً');
                }
            }
        </script>
    @endpush
</x-app-layout>
