<?php

namespace App\Http\Controllers;

use App\Models\InventoryTransfer;
use App\Models\InventoryTransferItem;
use App\Models\Store;
use App\Models\Branch;
use App\Models\Product;
use App\Models\StoreInventory;
use App\Models\BranchInventory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class InventoryTransferController extends Controller
{
    public function index(Request $request)
    {
        $query = InventoryTransfer::with(['requestedBy', 'approvedBy', 'receivedBy', 'items.product'])
            ->forUser(Auth::user());

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('transfer_number', 'like', "%{$search}%")
                    ->orWhere('notes', 'like', "%{$search}%");
            });
        }

        $transfers = $query->orderBy('created_at', 'desc')->paginate(15);

        return view('inventory-transfers.index', compact('transfers'));
    }

    public function show(InventoryTransfer $inventoryTransfer)
    {
        $inventoryTransfer->load(['items.product', 'requestedBy', 'approvedBy', 'receivedBy']);

        return view('inventory-transfers.show', compact('inventoryTransfer'));
    }

    public function create()
    {
        $user = Auth::user();
        $stores = Store::active()->accessibleBy($user)->with('branch')->get();
        $branches = Branch::where('is_active', true)->get();
        $products = Product::where('is_active', true)->with('category')->get();

        return view('inventory-transfers.create', compact('stores', 'branches', 'products'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'type' => 'required|in:store_to_branch,branch_to_store,store_to_store,branch_to_branch',
            'source_type' => 'required|in:store,branch',
            'source_id' => 'required|integer',
            'destination_type' => 'required|in:store,branch',
            'destination_id' => 'required|integer',
            'notes' => 'nullable|string|max:1000',
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'required|exists:products,id',
            'items.*.requested_quantity' => 'required|numeric|min:0.01',
            'items.*.notes' => 'nullable|string|max:500',
        ]);

        // Validate that source and destination are different
        if (
            $validated['source_type'] === $validated['destination_type'] &&
            $validated['source_id'] === $validated['destination_id']
        ) {
            return back()->withErrors(['destination_id' => 'المصدر والوجهة لا يمكن أن يكونا نفس المكان']);
        }

        DB::beginTransaction();
        try {
            // Create the transfer
            $transfer = InventoryTransfer::create([
                'transfer_number' => InventoryTransfer::generateTransferNumber(),
                'type' => $validated['type'],
                'source_type' => $validated['source_type'],
                'source_id' => $validated['source_id'],
                'destination_type' => $validated['destination_type'],
                'destination_id' => $validated['destination_id'],
                'requested_by' => Auth::id(),
                'status' => 'pending',
                'notes' => $validated['notes'],
                'requested_at' => now(),
            ]);

            // Create transfer items
            foreach ($validated['items'] as $item) {
                InventoryTransferItem::create([
                    'inventory_transfer_id' => $transfer->id,
                    'product_id' => $item['product_id'],
                    'requested_quantity' => $item['requested_quantity'],
                    'notes' => $item['notes'] ?? null,
                ]);
            }

            DB::commit();

            return redirect()->route('admin.inventory-transfers.show', $transfer)
                ->with('success', 'تم إنشاء طلب النقل بنجاح');
        } catch (\Exception $e) {
            DB::rollback();
            return back()->withErrors(['error' => 'حدث خطأ أثناء إنشاء طلب النقل: ' . $e->getMessage()]);
        }
    }

    public function approve(InventoryTransfer $inventoryTransfer, Request $request)
    {
        if (!$inventoryTransfer->canBeApproved()) {
            return back()->withErrors(['error' => 'لا يمكن الموافقة على هذا الطلب']);
        }

        $validated = $request->validate([
            'items' => 'required|array',
            'items.*.approved_quantity' => 'required|numeric|min:0',
            'items.*.unit_cost' => 'nullable|numeric|min:0',
        ]);

        DB::beginTransaction();
        try {
            // Update transfer status
            $inventoryTransfer->update([
                'status' => 'approved',
                'approved_by' => Auth::id(),
                'approved_at' => now(),
            ]);

            // Update items with approved quantities
            foreach ($validated['items'] as $itemId => $itemData) {
                $item = $inventoryTransfer->items()->findOrFail($itemId);
                $item->update([
                    'approved_quantity' => $itemData['approved_quantity'],
                    'unit_cost' => $itemData['unit_cost'] ?? null,
                ]);
            }

            DB::commit();

            return back()->with('success', 'تم الموافقة على طلب النقل بنجاح');
        } catch (\Exception $e) {
            DB::rollback();
            return back()->withErrors(['error' => 'حدث خطأ أثناء الموافقة: ' . $e->getMessage()]);
        }
    }

    public function ship(InventoryTransfer $inventoryTransfer)
    {
        if (!$inventoryTransfer->canBeShipped()) {
            return back()->withErrors(['error' => 'لا يمكن شحن هذا الطلب']);
        }

        DB::beginTransaction();
        try {
            // Validate and remove stock from source
            foreach ($inventoryTransfer->items as $item) {
                if ($inventoryTransfer->source_type === 'store') {
                    $storeInventory = StoreInventory::where('store_id', $inventoryTransfer->source_id)
                        ->where('product_id', $item->product_id)
                        ->first();

                    if (!$storeInventory || !$storeInventory->canTransfer($item->approved_quantity)) {
                        throw new \Exception("مخزون غير كافي للمنتج: {$item->product->name}");
                    }
                } else {
                    $branchInventory = BranchInventory::where('branch_id', $inventoryTransfer->source_id)
                        ->where('product_id', $item->product_id)
                        ->first();

                    if (!$branchInventory || $branchInventory->quantity < $item->approved_quantity) {
                        throw new \Exception("مخزون غير كافي للمنتج: {$item->product->name}");
                    }
                }
            }

            // Remove stock from source
            foreach ($inventoryTransfer->items as $item) {
                if ($inventoryTransfer->source_type === 'store') {
                    $storeInventory = StoreInventory::where('store_id', $inventoryTransfer->source_id)
                        ->where('product_id', $item->product_id)
                        ->first();
                    $storeInventory->removeStock($item->approved_quantity);
                } else {
                    $branchInventory = BranchInventory::where('branch_id', $inventoryTransfer->source_id)
                        ->where('product_id', $item->product_id)
                        ->first();
                    $branchInventory->quantity -= $item->approved_quantity;
                    $branchInventory->save();
                }
            }

            // Update transfer status
            $inventoryTransfer->update([
                'status' => 'in_transit',
                'shipped_at' => now(),
            ]);

            DB::commit();

            return back()->with('success', 'تم شحن الطلب بنجاح');
        } catch (\Exception $e) {
            DB::rollback();
            return back()->withErrors(['error' => 'حدث خطأ أثناء الشحن: ' . $e->getMessage()]);
        }
    }

    public function receive(InventoryTransfer $inventoryTransfer, Request $request)
    {
        if (!$inventoryTransfer->canBeReceived()) {
            return back()->withErrors(['error' => 'لا يمكن استلام هذا الطلب']);
        }

        $validated = $request->validate([
            'items' => 'required|array',
            'items.*.received_quantity' => 'required|numeric|min:0',
            'items.*.damage_notes' => 'nullable|string|max:500',
        ]);

        DB::beginTransaction();
        try {
            // Update items with received quantities
            foreach ($validated['items'] as $itemId => $itemData) {
                $item = $inventoryTransfer->items()->findOrFail($itemId);
                $item->update([
                    'received_quantity' => $itemData['received_quantity'],
                    'damage_notes' => $itemData['damage_notes'] ?? null,
                ]);

                // Add stock to destination
                if ($inventoryTransfer->destination_type === 'store') {
                    // Get source pricing information
                    $sourcePricing = $this->getSourcePricing($inventoryTransfer, $item);

                    $storeInventory = StoreInventory::updateOrCreate(
                        [
                            'store_id' => $inventoryTransfer->destination_id,
                            'product_id' => $item->product_id,
                        ],
                        [
                            'quantity' => 0,
                            'minimum_stock' => 0,
                            'cost_price' => $sourcePricing['cost_price'],
                            'sale_price_1' => $sourcePricing['sale_price_1'],
                            'sale_price_2' => $sourcePricing['sale_price_2'],
                            'sale_price_3' => $sourcePricing['sale_price_3'],
                        ]
                    );
                    $storeInventory->addStock($itemData['received_quantity']);
                } else {
                    // Get source pricing information
                    $sourcePricing = $this->getSourcePricing($inventoryTransfer, $item);

                    $branchInventory = BranchInventory::updateOrCreate(
                        [
                            'branch_id' => $inventoryTransfer->destination_id,
                            'product_id' => $item->product_id,
                        ],
                        [
                            'quantity' => 0,
                            'minimum_stock' => 10,
                            'cost_price' => $sourcePricing['cost_price'],
                            'sale_price_1' => $sourcePricing['sale_price_1'],
                            'sale_price_2' => $sourcePricing['sale_price_2'],
                            'sale_price_3' => $sourcePricing['sale_price_3'],
                        ]
                    );
                    $branchInventory->quantity += $itemData['received_quantity'];
                    $branchInventory->save();
                }
            }

            // Update transfer status
            $inventoryTransfer->update([
                'status' => 'completed',
                'received_by' => Auth::id(),
                'received_at' => now(),
            ]);

            DB::commit();

            return back()->with('success', 'تم استلام الطلب بنجاح');
        } catch (\Exception $e) {
            DB::rollback();
            return back()->withErrors(['error' => 'حدث خطأ أثناء الاستلام: ' . $e->getMessage()]);
        }
    }

    public function cancel(InventoryTransfer $inventoryTransfer, Request $request)
    {
        if (!$inventoryTransfer->canBeCancelled()) {
            return back()->withErrors(['error' => 'لا يمكن إلغاء هذا الطلب']);
        }

        $validated = $request->validate([
            'rejection_reason' => 'required|string|max:1000',
        ]);

        $inventoryTransfer->update([
            'status' => 'cancelled',
            'rejection_reason' => $validated['rejection_reason'],
        ]);

        return back()->with('success', 'تم إلغاء الطلب بنجاح');
    }

    public function getAvailableStock(Request $request)
    {
        $validated = $request->validate([
            'location_type' => 'required|in:store,branch',
            'location_id' => 'required|integer',
            'product_id' => 'required|exists:products,id',
        ]);

        $quantity = 0;

        if ($validated['location_type'] === 'store') {
            $inventory = StoreInventory::where('store_id', $validated['location_id'])
                ->where('product_id', $validated['product_id'])
                ->first();
            $quantity = $inventory ? $inventory->quantity : 0;
        } else {
            $inventory = BranchInventory::where('branch_id', $validated['location_id'])
                ->where('product_id', $validated['product_id'])
                ->first();
            $quantity = $inventory ? $inventory->quantity : 0;
        }

        return response()->json(['quantity' => $quantity]);
    }

    /**
     * Get source pricing information for transfer
     */
    private function getSourcePricing($inventoryTransfer, $item)
    {
        if ($inventoryTransfer->source_type === 'branch') {
            $sourceInventory = BranchInventory::where('branch_id', $inventoryTransfer->source_id)
                ->where('product_id', $item->product_id)
                ->first();

            if ($sourceInventory) {
                return [
                    'cost_price' => $sourceInventory->cost_price,
                    'sale_price_1' => $sourceInventory->sale_price_1,
                    'sale_price_2' => $sourceInventory->sale_price_2,
                    'sale_price_3' => $sourceInventory->sale_price_3,
                ];
            }
        } else {
            $sourceInventory = StoreInventory::where('store_id', $inventoryTransfer->source_id)
                ->where('product_id', $item->product_id)
                ->first();

            if ($sourceInventory) {
                return [
                    'cost_price' => $sourceInventory->cost_price,
                    'sale_price_1' => $sourceInventory->sale_price_1,
                    'sale_price_2' => $sourceInventory->sale_price_2,
                    'sale_price_3' => $sourceInventory->sale_price_3,
                ];
            }
        }

        // Fallback to product pricing
        return [
            'cost_price' => $item->product->price ?? 0,
            'sale_price_1' => $item->product->selling_price ?? $item->product->price ?? 0,
            'sale_price_2' => null,
            'sale_price_3' => null,
        ];
    }
}
