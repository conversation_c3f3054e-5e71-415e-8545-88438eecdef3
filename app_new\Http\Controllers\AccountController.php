<?php

namespace App\Http\Controllers;

use App\Models\Account;
use Illuminate\Http\Request;

class Account<PERSON>ontroller extends Controller
{
    public function index(Request $request)
    {
        $query = Account::query();
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }
        if ($request->filled('is_active')) {
            $query->where('is_active', $request->is_active);
        }
        $accounts = $query->latest()->paginate(15);
        return view('accounts.index', compact('accounts'));
    }

    public function show(Account $account)
    {
        $transactions = $account->transactions()
            ->latest()
            ->paginate(10);

        return view('accounts.show', compact('account', 'transactions'));
    }

    public function create()
    {
        $accountTypes = [
            'branch' => 'فرع',
            'supplier' => 'مورد',
            'customer' => 'عميل'
        ];

        return view('accounts.create', compact('accountTypes'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:50|unique:accounts',
            'type' => 'required|in:branch,supplier,customer',
            'is_active' => 'boolean'
        ]);

        $account = Account::create($validated);

        return redirect()
            ->route('accounts.show', $account)
            ->with('success', 'تم إنشاء الحساب بنجاح');
    }
}
