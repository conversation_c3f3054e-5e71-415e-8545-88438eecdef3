<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ExpenseCategory extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'is_active',
        'parent_id',
        'monthly_budget',
        'yearly_budget',
        'color',
        'icon',
        'is_fixed',
        'requires_approval',
        'approval_roles'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_fixed' => 'boolean',
        'requires_approval' => 'boolean',
        'approval_roles' => 'array',
        'monthly_budget' => 'decimal:2',
        'yearly_budget' => 'decimal:2'
    ];

    public function parent(): BelongsTo
    {
        return $this->belongsTo(ExpenseCategory::class, 'parent_id');
    }

    public function children(): HasMany
    {
        return $this->hasMany(ExpenseCategory::class, 'parent_id');
    }

    public function expenses()
    {
        return $this->hasMany(Expense::class, 'expense_category_id');
    }

    public function getMonthlyExpensesAttribute()
    {
        return $this->expenses()
            ->whereMonth('expense_date', now()->month)
            ->whereYear('expense_date', now()->year)
            ->sum('amount');
    }

    public function getYearlyExpensesAttribute()
    {
        return $this->expenses()
            ->whereYear('expense_date', now()->year)
            ->sum('amount');
    }

    public function getMonthlyBudgetUsageAttribute()
    {
        if (!$this->monthly_budget) {
            return 0;
        }
        return ($this->monthly_expenses / $this->monthly_budget) * 100;
    }

    public function getYearlyBudgetUsageAttribute()
    {
        if (!$this->yearly_budget) {
            return 0;
        }
        return ($this->yearly_expenses / $this->yearly_budget) * 100;
    }

    public function getStatusAttribute()
    {
        return $this->is_active ? 'active' : 'inactive';
    }

    public function getFullNameAttribute()
    {
        return $this->parent ? $this->parent->name . ' > ' . $this->name : $this->name;
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeRoot($query)
    {
        return $query->whereNull('parent_id');
    }

    public function scopeFixed($query)
    {
        return $query->where('is_fixed', true);
    }

    public function scopeRequiresApproval($query)
    {
        return $query->where('requires_approval', true);
    }
}
