<x-app-layout>
    <x-slot name="header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                    <i class="fas fa-building text-primary"></i> {{ __('مخزون الفروع') }}
                </h2>
                <p class="text-muted small mb-0">عرض مخزون جميع الفروع</p>
            </div>
            <div>
                <a href="{{ route('admin.inventory.overview') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right"></i> العودة للنظرة العامة
                </a>
            </div>
        </div>
    </x-slot>

    <div class="container-fluid">
        <div class="row">
            @foreach($branches as $branch)
                <div class="col-xl-4 col-md-6 mb-4">
                    <div class="card border-left-primary shadow h-100">
                        <div class="card-header py-3 d-flex justify-content-between align-items-center">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-building"></i> {{ $branch->name }}
                            </h6>
                            <span class="badge bg-primary">{{ $branch->code }}</span>
                        </div>
                        <div class="card-body">
                            <div class="row no-gutters align-items-center mb-3">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                        إجمالي المنتجات
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $branch->total_products }}</div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-boxes fa-2x text-gray-300"></i>
                                </div>
                            </div>

                            <div class="row no-gutters align-items-center mb-3">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        إجمالي القيمة
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($branch->total_value, 2) }} ج.م</div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                                </div>
                            </div>

                            <div class="row no-gutters align-items-center mb-3">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                        مخزون منخفض
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $branch->low_stock_count }}</div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                                </div>
                            </div>

                            <div class="d-grid gap-2">
                                <a href="{{ route('admin.inventory.branch-details', $branch->id) }}" class="btn btn-primary btn-sm">
                                    <i class="fas fa-eye"></i> عرض التفاصيل
                                </a>
                                <a href="{{ route('admin.branches.sales-inventory', $branch->id) }}" class="btn btn-outline-secondary btn-sm">
                                    <i class="fas fa-cog"></i> إدارة المخزون
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>

        @if($branches->isEmpty())
            <div class="card shadow">
                <div class="card-body text-center py-5">
                    <i class="fas fa-building fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد فروع</h5>
                    <p class="text-muted">لم يتم إنشاء أي فروع بعد</p>
                    <a href="{{ route('admin.branches.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> إضافة فرع جديد
                    </a>
                </div>
            </div>
        @endif
    </div>
</x-app-layout>
