<x-app-layout>
    <div class="container-fluid px-4">
        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-0 text-gray-800">
                    <i class="fas fa-shopping-cart text-primary me-2"></i>
                    إدارة المشتريات
                </h1>
                <p class="text-muted mb-0">إدارة وتتبع جميع عمليات الشراء والمخزون</p>
            </div>
            @if (auth()->user()->isAdmin())
                <a href="{{ user_route('purchases.create') }}" class="btn btn-primary shadow-sm">
                    <i class="fas fa-plus me-2"></i>
                    إضافة مشتريات جديدة
                </a>
            @endif
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-primary shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                    إجمالي المشتريات
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $purchases->count() }}</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-success shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                    المشتريات المكتملة
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    {{ $purchases->where('status', 'completed')->count() }}</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-warning shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                    قيد الانتظار
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    {{ $purchases->where('status', 'pending')->count() }}</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-clock fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-primary shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                    في انتظار التوزيع
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    {{ $purchases->where('status', 'pending_distribution')->count() }}</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-boxes fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-info shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                    إجمالي القيمة
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    {{ format_currency($purchases->sum('total_amount')) }}</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content Card -->
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-list me-2"></i>
                    قائمة المشتريات
                </h6>
                <div class="dropdown no-arrow">
                    <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink"
                        data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                    </a>
                    <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in"
                        aria-labelledby="dropdownMenuLink">
                        <div class="dropdown-header">خيارات إضافية:</div>
                        <a class="dropdown-item" href="{{ user_route('purchases.index') }}?export=excel">
                            <i class="fas fa-file-excel fa-sm fa-fw mr-2 text-gray-400"></i>
                            تصدير إلى Excel
                        </a>
                        <a class="dropdown-item" href="{{ user_route('purchases.index') }}?export=pdf">
                            <i class="fas fa-file-pdf fa-sm fa-fw mr-2 text-gray-400"></i>
                            تصدير إلى PDF
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <!-- Advanced Filters -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-secondary">
                            <i class="fas fa-filter me-2"></i>
                            فلاتر البحث المتقدم
                        </h6>
                    </div>
                    <div class="card-body">
                        <form action="{{ user_route('purchases.index') }}" method="GET">
                            <div class="row">
                                <div class="col-md-3 mb-3">
                                    <label for="supplier_id" class="form-label">المورد</label>
                                    <select name="supplier_id" id="supplier_id" class="form-select">
                                        <option value="">جميع الموردين</option>
                                        @foreach ($suppliers as $supplier)
                                            <option value="{{ $supplier->id }}"
                                                {{ request('supplier_id') == $supplier->id ? 'selected' : '' }}>
                                                {{ $supplier->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>

                                <div class="col-md-3 mb-3">
                                    <label for="branch_id" class="form-label">الفرع</label>
                                    <select name="branch_id" id="branch_id" class="form-select">
                                        <option value="">جميع الفروع</option>
                                        @foreach ($branches as $branch)
                                            <option value="{{ $branch->id }}"
                                                {{ request('branch_id') == $branch->id ? 'selected' : '' }}>
                                                {{ $branch->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>

                                <div class="col-md-3 mb-3">
                                    <label for="status" class="form-label">الحالة</label>
                                    <select name="status" id="status" class="form-select">
                                        <option value="">جميع الحالات</option>
                                        <option value="pending"
                                            {{ request('status') == 'pending' ? 'selected' : '' }}>
                                            قيد الانتظار</option>
                                        <option value="pending_distribution"
                                            {{ request('status') == 'pending_distribution' ? 'selected' : '' }}>
                                            في انتظار التوزيع</option>
                                        <option value="completed"
                                            {{ request('status') == 'completed' ? 'selected' : '' }}>مكتمل</option>
                                        <option value="cancelled"
                                            {{ request('status') == 'cancelled' ? 'selected' : '' }}>ملغي</option>
                                    </select>
                                </div>

                                <div class="col-md-3 mb-3">
                                    <label for="search" class="form-label">البحث</label>
                                    <input type="text" name="search" id="search" class="form-control"
                                        placeholder="البحث في المرجع أو المورد" value="{{ request('search') }}">
                                </div>

                                <div class="col-md-3 mb-3">
                                    <label for="date_from" class="form-label">من تاريخ</label>
                                    <input type="date" name="date_from" id="date_from" class="form-control"
                                        value="{{ request('date_from') }}">
                                </div>

                                <div class="col-md-3 mb-3">
                                    <label for="date_to" class="form-label">إلى تاريخ</label>
                                    <input type="date" name="date_to" id="date_to" class="form-control"
                                        value="{{ request('date_to') }}">
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-flex gap-2">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-search me-2"></i>بحث
                                        </button>
                                        <a href="{{ user_route('purchases.index') }}"
                                            class="btn btn-outline-secondary">
                                            <i class="fas fa-undo me-2"></i>إعادة تعيين
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Purchases Table -->
                <div class="table-responsive">
                    <table class="table table-hover align-middle">
                        <thead class="table-light">
                            <tr>
                                <th class="border-0 fw-bold">رقم الفاتورة</th>
                                <th class="border-0 fw-bold">المورد</th>
                                <th class="border-0 fw-bold">الفرع</th>
                                <th class="border-0 fw-bold">التاريخ</th>
                                <th class="border-0 fw-bold">المجموع</th>
                                <th class="border-0 fw-bold">الخصم</th>
                                <th class="border-0 fw-bold">الصافي</th>
                                <th class="border-0 fw-bold">الحالة</th>
                                <th class="border-0 fw-bold">حالة الدفع</th>
                                <th class="border-0 fw-bold text-center">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($purchases as $purchase)
                                <tr class="border-bottom">
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-circle bg-primary text-white me-3">
                                                <i class="fas fa-receipt"></i>
                                            </div>
                                            <div>
                                                <div class="fw-bold text-dark">{{ $purchase->invoice_number }}</div>
                                                <small
                                                    class="text-muted">{{ $purchase->created_at->format('d/m/Y') }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="fw-semibold text-dark">{{ $purchase->supplier->name }}</div>
                                        <small
                                            class="text-muted">{{ $purchase->supplier->phone ?? 'لا يوجد هاتف' }}</small>
                                    </td>
                                    <td>
                                        <span
                                            class="badge bg-light text-dark border">{{ $purchase->branch ? $purchase->branch->name : 'غير محدد' }}</span>
                                    </td>
                                    <td>
                                        <div class="text-dark">
                                            {{ \Carbon\Carbon::parse($purchase->purchase_date)->format('d/m/Y') }}
                                        </div>
                                        <small
                                            class="text-muted">{{ \Carbon\Carbon::parse($purchase->purchase_date)->format('h:i A') }}</small>
                                    </td>
                                    <td>
                                        <div class="fw-bold text-primary">
                                            {{ format_currency($purchase->total_amount ?? 0) }}</div>
                                    </td>
                                    <td>
                                        @if ($purchase->discount_amount > 0)
                                            <div class="fw-bold text-danger">
                                                @if ($purchase->discount_type === 'percentage')
                                                    {{ $purchase->discount_value }}%
                                                @endif
                                                <div>{{ format_currency($purchase->discount_amount) }}</div>
                                            </div>
                                        @else
                                            <span class="text-muted">-</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="fw-bold text-success">
                                            {{ format_currency($purchase->final_amount) }}
                                        </div>
                                    </td>
                                    <td>
                                        @if ($purchase->status === 'completed')
                                            <span class="badge bg-success-soft text-success px-3 py-2">
                                                <i class="fas fa-check-circle me-1"></i>مكتمل
                                            </span>
                                        @elseif($purchase->status === 'cancelled')
                                            <span class="badge bg-danger-soft text-danger px-3 py-2">
                                                <i class="fas fa-times-circle me-1"></i>ملغي
                                            </span>
                                        @elseif($purchase->status === 'pending_distribution')
                                            <span class="badge bg-info-soft text-info px-3 py-2">
                                                <i class="fas fa-boxes me-1"></i>في انتظار التوزيع
                                            </span>
                                        @else
                                            <span class="badge bg-warning-soft text-warning px-3 py-2">
                                                <i class="fas fa-clock me-1"></i>قيد الانتظار
                                            </span>
                                        @endif
                                    </td>
                                    <td>
                                        @if ($purchase->status === 'completed')
                                            <span class="badge bg-{{ $purchase->payment_status_color }} px-3 py-2">
                                                {{ $purchase->payment_status }}
                                            </span>
                                            @if ($purchase->actual_remaining_amount > 0)
                                                <div class="small text-muted mt-1">
                                                    متبقي: {{ format_currency($purchase->actual_remaining_amount) }}
                                                </div>
                                            @endif
                                        @else
                                            <span class="badge bg-secondary px-3 py-2">
                                                غير مكتمل
                                            </span>
                                        @endif
                                    </td>
                                    <td class="text-center">
                                        <div class="btn-group" role="group">
                                            <a href="{{ user_route('purchases.show', $purchase) }}"
                                                class="btn btn-outline-info btn-sm" title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            @if ($purchase->status === 'pending')
                                                <a href="{{ user_route('purchases.edit', $purchase) }}"
                                                    class="btn btn-outline-primary btn-sm" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <button type="button" class="btn btn-outline-success btn-sm"
                                                    title="إكمال" onclick="completePurchase({{ $purchase->id }})">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                                <button type="button" class="btn btn-outline-danger btn-sm"
                                                    title="إلغاء" onclick="cancelPurchase({{ $purchase->id }})">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            @endif
                                            @if ($purchase->status === 'pending_distribution')
                                                <a href="{{ route('admin.purchases.distribute', $purchase) }}"
                                                    class="btn btn-outline-warning btn-sm" title="توزيع المنتجات">
                                                    <i class="fas fa-boxes"></i>
                                                </a>
                                            @endif
                                            @if ($purchase->status === 'completed' && $purchase->actual_remaining_amount > 0)
                                                <a href="{{ user_route('supplier-payments.create', $purchase) }}"
                                                    class="btn btn-outline-warning btn-sm" title="دفع مبلغ">
                                                    <i class="fas fa-money-bill-wave"></i>
                                                </a>
                                            @endif
                                            @if ($purchase->status === 'completed')
                                                <a href="{{ route('admin.purchase-returns.create', ['purchase_id' => $purchase->id]) }}"
                                                    class="btn btn-outline-secondary btn-sm" title="إرجاع منتجات">
                                                    <i class="fas fa-undo"></i>
                                                </a>
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="8" class="text-center py-5">
                                        <div class="empty-state">
                                            <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                                            <h5 class="text-muted">لا توجد مشتريات</h5>
                                            <p class="text-muted">لم يتم العثور على أي مشتريات بناءً على المعايير
                                                المحددة</p>
                                            @if (auth()->user()->isAdmin())
                                                <a href="{{ user_route('purchases.create') }}"
                                                    class="btn btn-primary">
                                                    <i class="fas fa-plus me-2"></i>إضافة مشتريات جديدة
                                                </a>
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-between align-items-center mt-4">
                    <div class="text-muted">
                        عرض {{ $purchases->firstItem() ?? 0 }} إلى {{ $purchases->lastItem() ?? 0 }}
                        من أصل {{ $purchases->total() }} نتيجة
                    </div>
                    <div>
                        {{ $purchases->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
        <script>
            function completePurchase(purchaseId) {
                if (confirm('هل أنت متأكد من إكمال هذه العملية؟')) {
                    fetch(`{{ url('') }}/admin/purchases/${purchaseId}/complete`, {
                            method: 'POST',
                            headers: {
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                                'Content-Type': 'application/json',
                            },
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                location.reload();
                            } else {
                                alert('حدث خطأ أثناء إكمال العملية');
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            alert('حدث خطأ أثناء إكمال العملية');
                        });
                }
            }

            function cancelPurchase(purchaseId) {
                if (confirm('هل أنت متأكد من إلغاء هذه العملية؟')) {
                    fetch(`{{ url('') }}/admin/purchases/${purchaseId}/cancel`, {
                            method: 'POST',
                            headers: {
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                                'Content-Type': 'application/json',
                            },
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                location.reload();
                            } else {
                                alert('حدث خطأ أثناء إلغاء العملية');
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            alert('حدث خطأ أثناء إلغاء العملية');
                        });
                }
            }
        </script>
    @endpush

    @push('styles')
        <style>
            .avatar-circle {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 14px;
            }

            .border-left-primary {
                border-left: 0.25rem solid #4e73df !important;
            }

            .border-left-success {
                border-left: 0.25rem solid #1cc88a !important;
            }

            .border-left-warning {
                border-left: 0.25rem solid #f6c23e !important;
            }

            .border-left-info {
                border-left: 0.25rem solid #36b9cc !important;
            }

            .bg-success-soft {
                background-color: rgba(28, 200, 138, 0.1) !important;
            }

            .bg-danger-soft {
                background-color: rgba(231, 74, 59, 0.1) !important;
            }

            .bg-warning-soft {
                background-color: rgba(246, 194, 62, 0.1) !important;
            }

            .empty-state {
                padding: 3rem 1rem;
            }

            .table-hover tbody tr:hover {
                background-color: rgba(0, 0, 0, 0.02);
            }

            .btn-group .btn {
                margin-left: 2px;
            }

            .btn-group .btn:first-child {
                margin-left: 0;
            }
        </style>
    @endpush
</x-app-layout>
