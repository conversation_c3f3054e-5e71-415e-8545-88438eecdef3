<?php

namespace Tests\Feature;

use App\Models\Branch;
use App\Models\BranchInventory;
use App\Models\Customer;
use App\Models\Product;
use App\Models\Sale;
use App\Models\SaleItem;
use App\Models\SaleReturn;
use App\Models\SaleReturnItem;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SaleReturnTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $branch;
    protected $customer;
    protected $product;
    protected $sale;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test data
        $this->user = User::factory()->create();
        $this->branch = Branch::factory()->create();
        $this->customer = Customer::factory()->create();
        $this->product = Product::factory()->create();

        // Create branch inventory
        BranchInventory::create([
            'branch_id' => $this->branch->id,
            'product_id' => $this->product->id,
            'quantity' => 100,
            'min_quantity' => 10,
        ]);

        // Create a sale
        $this->sale = Sale::create([
            'invoice_number' => 'INV-001',
            'customer_id' => $this->customer->id,
            'branch_id' => $this->branch->id,
            'user_id' => $this->user->id,
            'total_amount' => 500.00,
            'discount_amount' => 0,
            'status' => 'completed',
        ]);

        // Create sale item
        SaleItem::create([
            'sale_id' => $this->sale->id,
            'product_id' => $this->product->id,
            'quantity' => 10,
            'price' => 50.00,
            'subtotal' => 500.00,
        ]);

        // Reduce inventory for the sale
        $inventory = BranchInventory::where('branch_id', $this->branch->id)
            ->where('product_id', $this->product->id)
            ->first();
        $inventory->decrement('quantity', 10);
    }

    /** @test */
    public function it_can_create_a_sale_return()
    {
        $returnData = [
            'sale_id' => $this->sale->id,
            'return_date' => now()->format('Y-m-d'),
            'return_type' => 'partial',
            'reason' => 'customer_request',
            'notes' => 'Customer requested return',
            'refund_amount' => 250.00,
            'items' => [
                [
                    'sale_item_id' => $this->sale->items->first()->id,
                    'quantity_returned' => 5,
                    'condition' => 'good',
                    'item_notes' => 'Good condition',
                ]
            ]
        ];

        $response = $this->actingAs($this->user)
            ->post(route('sale-returns.store'), $returnData);

        $response->assertRedirect();
        $this->assertDatabaseHas('sale_returns', [
            'sale_id' => $this->sale->id,
            'total_amount' => 250.00,
            'refund_amount' => 250.00,
            'status' => 'pending',
        ]);
    }

    /** @test */
    public function it_updates_inventory_when_return_is_completed()
    {
        // Create a return
        $saleReturn = SaleReturn::create([
            'sale_id' => $this->sale->id,
            'customer_id' => $this->customer->id,
            'branch_id' => $this->branch->id,
            'user_id' => $this->user->id,
            'total_amount' => 250.00,
            'refund_amount' => 250.00,
            'status' => 'pending',
            'return_type' => 'partial',
            'reason' => 'customer_request',
            'return_date' => now(),
        ]);

        // Create return item
        SaleReturnItem::create([
            'sale_return_id' => $saleReturn->id,
            'sale_item_id' => $this->sale->items->first()->id,
            'product_id' => $this->product->id,
            'quantity_returned' => 5,
            'original_quantity' => 10,
            'sale_price' => 50.00,
            'total_amount' => 250.00,
            'condition' => 'good',
        ]);

        // Check initial inventory
        $initialInventory = BranchInventory::where('branch_id', $this->branch->id)
            ->where('product_id', $this->product->id)
            ->first();
        $initialQuantity = $initialInventory->quantity;

        // Complete the return
        $saleReturn->complete();

        // Check inventory was updated
        $updatedInventory = BranchInventory::where('branch_id', $this->branch->id)
            ->where('product_id', $this->product->id)
            ->first();

        $this->assertEquals($initialQuantity + 5, $updatedInventory->quantity);
        $this->assertEquals('completed', $saleReturn->fresh()->status);
    }

    /** @test */
    public function it_validates_return_quantities()
    {
        $saleReturn = SaleReturn::create([
            'sale_id' => $this->sale->id,
            'customer_id' => $this->customer->id,
            'branch_id' => $this->branch->id,
            'user_id' => $this->user->id,
            'total_amount' => 600.00, // More than original sale
            'refund_amount' => 600.00,
            'status' => 'pending',
            'return_type' => 'partial',
            'reason' => 'customer_request',
            'return_date' => now(),
        ]);

        // Create return item with quantity exceeding original
        SaleReturnItem::create([
            'sale_return_id' => $saleReturn->id,
            'sale_item_id' => $this->sale->items->first()->id,
            'product_id' => $this->product->id,
            'quantity_returned' => 15, // More than original 10
            'original_quantity' => 10,
            'sale_price' => 50.00,
            'total_amount' => 750.00,
            'condition' => 'good',
        ]);

        $errors = $saleReturn->validateReturnQuantities();
        $this->assertNotEmpty($errors);
        $this->assertStringContainsString('تتجاوز الكمية الأصلية', $errors[0]);
    }

    /** @test */
    public function it_prevents_duplicate_inventory_adjustments()
    {
        $saleReturn = SaleReturn::create([
            'sale_id' => $this->sale->id,
            'customer_id' => $this->customer->id,
            'branch_id' => $this->branch->id,
            'user_id' => $this->user->id,
            'total_amount' => 250.00,
            'refund_amount' => 250.00,
            'status' => 'pending',
            'return_type' => 'partial',
            'reason' => 'customer_request',
            'return_date' => now(),
        ]);

        $returnItem = SaleReturnItem::create([
            'sale_return_id' => $saleReturn->id,
            'sale_item_id' => $this->sale->items->first()->id,
            'product_id' => $this->product->id,
            'quantity_returned' => 5,
            'original_quantity' => 10,
            'sale_price' => 50.00,
            'total_amount' => 250.00,
            'condition' => 'good',
        ]);

        $initialInventory = BranchInventory::where('branch_id', $this->branch->id)
            ->where('product_id', $this->product->id)
            ->first();
        $initialQuantity = $initialInventory->quantity;

        // Complete return first time
        $saleReturn->complete();

        $firstCompletionInventory = BranchInventory::where('branch_id', $this->branch->id)
            ->where('product_id', $this->product->id)
            ->first();

        // Try to complete again (should not adjust inventory again)
        $saleReturn->complete();

        $secondCompletionInventory = BranchInventory::where('branch_id', $this->branch->id)
            ->where('product_id', $this->product->id)
            ->first();

        $this->assertEquals($firstCompletionInventory->quantity, $secondCompletionInventory->quantity);
        $this->assertTrue($returnItem->fresh()->inventory_adjusted);
    }

    /** @test */
    public function it_shows_return_information_in_sale()
    {
        // Create a return
        $saleReturn = SaleReturn::create([
            'sale_id' => $this->sale->id,
            'customer_id' => $this->customer->id,
            'branch_id' => $this->branch->id,
            'user_id' => $this->user->id,
            'total_amount' => 250.00,
            'refund_amount' => 250.00,
            'status' => 'completed',
            'return_type' => 'partial',
            'reason' => 'customer_request',
            'return_date' => now(),
        ]);

        $this->assertTrue($this->sale->hasReturns());
        $this->assertEquals(250.00, $this->sale->total_returns);
    }
}
