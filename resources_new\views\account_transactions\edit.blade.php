<x-app-layout>
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h2 class="h3 mb-0">تعديل المعاملة المالية</h2>
                    <div class="btn-group">
                        <a href="{{ route('account-transactions.show', $accountTransaction) }}" class="btn btn-outline-info">
                            <i class="fas fa-eye"></i> عرض
                        </a>
                        <a href="{{ route('account-transactions.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> العودة للقائمة
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Edit Form -->
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-edit text-primary"></i> تعديل بيانات المعاملة
                        </h5>
                    </div>
                    <div class="card-body">
                        <form action="{{ route('account-transactions.update', $accountTransaction) }}" method="POST">
                            @csrf
                            @method('PUT')
                            
                            <div class="row">
                                <!-- Account -->
                                <div class="col-md-6 mb-3">
                                    <label for="account_id" class="form-label">الحساب <span class="text-danger">*</span></label>
                                    <select class="form-select @error('account_id') is-invalid @enderror" 
                                            id="account_id" name="account_id" required>
                                        <option value="">اختر الحساب</option>
                                        @foreach($accounts as $account)
                                            <option value="{{ $account->id }}" 
                                                {{ old('account_id', $accountTransaction->account_id) == $account->id ? 'selected' : '' }}>
                                                {{ $account->name }} ({{ $account->code }})
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('account_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <!-- Transaction Type -->
                                <div class="col-md-6 mb-3">
                                    <label for="type" class="form-label">نوع المعاملة <span class="text-danger">*</span></label>
                                    <select class="form-select @error('type') is-invalid @enderror" 
                                            id="type" name="type" required>
                                        <option value="">اختر نوع المعاملة</option>
                                        <option value="debit" {{ old('type', $accountTransaction->type) == 'debit' ? 'selected' : '' }}>
                                            مدين (خصم)
                                        </option>
                                        <option value="credit" {{ old('type', $accountTransaction->type) == 'credit' ? 'selected' : '' }}>
                                            دائن (إضافة)
                                        </option>
                                        <option value="transfer" {{ old('type', $accountTransaction->type) == 'transfer' ? 'selected' : '' }}>
                                            تحويل
                                        </option>
                                    </select>
                                    @error('type')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="row">
                                <!-- Amount -->
                                <div class="col-md-6 mb-3">
                                    <label for="amount" class="form-label">المبلغ <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <input type="number" step="0.01" min="0" 
                                               class="form-control @error('amount') is-invalid @enderror" 
                                               id="amount" name="amount" 
                                               value="{{ old('amount', $accountTransaction->amount) }}" required>
                                        <span class="input-group-text">ريال</span>
                                    </div>
                                    @error('amount')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <!-- Reference Number -->
                                <div class="col-md-6 mb-3">
                                    <label for="reference_number" class="form-label">رقم المرجع</label>
                                    <input type="text" class="form-control @error('reference_number') is-invalid @enderror" 
                                           id="reference_number" name="reference_number" 
                                           value="{{ old('reference_number', $accountTransaction->reference_number) }}">
                                    @error('reference_number')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">رقم مرجعي للمعاملة (اختياري)</div>
                                </div>
                            </div>

                            <!-- Transfer To Account (shown only for transfer type) -->
                            <div class="row" id="transfer-section" style="display: {{ old('type', $accountTransaction->type) == 'transfer' ? 'block' : 'none' }};">
                                <div class="col-md-12 mb-3">
                                    <label for="to_account_id" class="form-label">تحويل إلى الحساب</label>
                                    <select class="form-select @error('to_account_id') is-invalid @enderror" 
                                            id="to_account_id" name="to_account_id">
                                        <option value="">اختر الحساب المحول إليه</option>
                                        @foreach($accounts as $account)
                                            <option value="{{ $account->id }}" 
                                                {{ old('to_account_id', $accountTransaction->to_account_id) == $account->id ? 'selected' : '' }}>
                                                {{ $account->name }} ({{ $account->code }})
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('to_account_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- Description -->
                            <div class="mb-3">
                                <label for="description" class="form-label">الوصف</label>
                                <textarea class="form-control @error('description') is-invalid @enderror" 
                                          id="description" name="description" rows="3">{{ old('description', $accountTransaction->description) }}</textarea>
                                @error('description')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Current Balance Info -->
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle"></i> معلومات الرصيد الحالي:</h6>
                                <p class="mb-0">
                                    <strong>الرصيد قبل المعاملة:</strong> {{ number_format($accountTransaction->balance_before, 2) }} ريال<br>
                                    <strong>الرصيد بعد المعاملة:</strong> {{ number_format($accountTransaction->balance_after, 2) }} ريال
                                </p>
                            </div>

                            <!-- Submit Buttons -->
                            <div class="d-flex justify-content-end gap-2">
                                <a href="{{ route('account-transactions.show', $accountTransaction) }}" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> إلغاء
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> حفظ التغييرات
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript for Transfer Section -->
    <script>
        document.getElementById('type').addEventListener('change', function() {
            const transferSection = document.getElementById('transfer-section');
            if (this.value === 'transfer') {
                transferSection.style.display = 'block';
                document.getElementById('to_account_id').required = true;
            } else {
                transferSection.style.display = 'none';
                document.getElementById('to_account_id').required = false;
                document.getElementById('to_account_id').value = '';
            }
        });
    </script>
</x-app-layout>
