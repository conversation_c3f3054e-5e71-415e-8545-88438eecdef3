@extends('layouts.app')

@section('title', 'تقرير أداء المواقع')

@section('content')
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-0 text-gray-800">تقرير أداء المواقع</h1>
                <p class="text-muted mb-0">تحليل أداء المخازن والفروع في عمليات النقل</p>
            </div>
            <div>
                <a href="{{ user_route('inventory-transfer-reports.index') }}" class="btn btn-outline-secondary me-2">
                    <i class="fas fa-arrow-right me-2"></i>العودة للتقارير
                </a>
                <a href="{{ user_route('inventory-transfer-reports.export', array_merge(['type' => 'locations'], request()->query())) }}"
                    class="btn btn-success">
                    <i class="fas fa-file-csv me-2"></i>تصدير
                </a>
            </div>
        </div>

        <!-- Date Filter -->
        <div class="card shadow mb-4">
            <div class="card-body">
                <form method="GET" class="row g-3 align-items-end">
                    <div class="col-md-4">
                        <label class="form-label">من تاريخ</label>
                        <input type="date" name="date_from" class="form-control" value="{{ $dateFrom }}">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">إلى تاريخ</label>
                        <input type="date" name="date_to" class="form-control" value="{{ $dateTo }}">
                    </div>
                    <div class="col-md-4">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-search me-2"></i>تطبيق الفلتر
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <div class="row">
            <!-- Store Performance -->
            <div class="col-lg-6 mb-4">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">أداء المخازن</h6>
                    </div>
                    <div class="card-body">
                        @if ($storePerformance->count() > 0)
                            @foreach ($storePerformance as $store)
                                <div class="card border-left-info mb-3">
                                    <div class="card-body py-3">
                                        <div class="row align-items-center">
                                            <div class="col-auto">
                                                <div class="avatar-circle bg-info text-white">
                                                    {{ substr($store->store_name, 0, 1) }}
                                                </div>
                                            </div>
                                            <div class="col">
                                                <div class="fw-bold text-primary">{{ $store->store_name }}</div>
                                                <div class="row text-center mt-2">
                                                    <div class="col-4">
                                                        <div class="fw-bold text-success">{{ $store->incoming_transfers }}
                                                        </div>
                                                        <small class="text-muted">واردة</small>
                                                    </div>
                                                    <div class="col-4">
                                                        <div class="fw-bold text-warning">{{ $store->outgoing_transfers }}
                                                        </div>
                                                        <small class="text-muted">صادرة</small>
                                                    </div>
                                                    <div class="col-4">
                                                        <div class="fw-bold text-primary">{{ $store->total_transfers }}
                                                        </div>
                                                        <small class="text-muted">إجمالي</small>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-auto">
                                                <div class="progress" style="width: 100px; height: 8px;">
                                                    @php
                                                        $maxTransfers = $storePerformance->max('total_transfers');
                                                        $percentage =
                                                            $maxTransfers > 0
                                                                ? ($store->total_transfers / $maxTransfers) * 100
                                                                : 0;
                                                    @endphp
                                                    <div class="progress-bar bg-info" style="width: {{ $percentage }}%">
                                                    </div>
                                                </div>
                                                <small class="text-muted">{{ number_format($percentage, 1) }}%</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        @else
                            <div class="text-center py-4">
                                <i class="fas fa-warehouse fa-2x text-muted mb-2"></i>
                                <p class="text-muted">لا توجد بيانات أداء للمخازن</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Branch Performance -->
            <div class="col-lg-6 mb-4">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">أداء الفروع</h6>
                    </div>
                    <div class="card-body">
                        @if ($branchPerformance->count() > 0)
                            @foreach ($branchPerformance as $branch)
                                <div class="card border-left-success mb-3">
                                    <div class="card-body py-3">
                                        <div class="row align-items-center">
                                            <div class="col-auto">
                                                <div class="avatar-circle bg-success text-white">
                                                    {{ substr($branch->branch_name, 0, 1) }}
                                                </div>
                                            </div>
                                            <div class="col">
                                                <div class="fw-bold text-primary">{{ $branch->branch_name }}</div>
                                                <div class="row text-center mt-2">
                                                    <div class="col-4">
                                                        <div class="fw-bold text-success">{{ $branch->incoming_transfers }}
                                                        </div>
                                                        <small class="text-muted">واردة</small>
                                                    </div>
                                                    <div class="col-4">
                                                        <div class="fw-bold text-warning">{{ $branch->outgoing_transfers }}
                                                        </div>
                                                        <small class="text-muted">صادرة</small>
                                                    </div>
                                                    <div class="col-4">
                                                        <div class="fw-bold text-primary">{{ $branch->total_transfers }}
                                                        </div>
                                                        <small class="text-muted">إجمالي</small>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-auto">
                                                <div class="progress" style="width: 100px; height: 8px;">
                                                    @php
                                                        $maxTransfers = $branchPerformance->max('total_transfers');
                                                        $percentage =
                                                            $maxTransfers > 0
                                                                ? ($branch->total_transfers / $maxTransfers) * 100
                                                                : 0;
                                                    @endphp
                                                    <div class="progress-bar bg-success"
                                                        style="width: {{ $percentage }}%"></div>
                                                </div>
                                                <small class="text-muted">{{ number_format($percentage, 1) }}%</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        @else
                            <div class="text-center py-4">
                                <i class="fas fa-store fa-2x text-muted mb-2"></i>
                                <p class="text-muted">لا توجد بيانات أداء للفروع</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- Performance Summary -->
        @if ($storePerformance->count() > 0 || $branchPerformance->count() > 0)
            <div class="row">
                <div class="col-12">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">ملخص الأداء العام</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <!-- Store Statistics -->
                                <div class="col-lg-6">
                                    <h6 class="text-info mb-3">إحصائيات المخازن</h6>
                                    <div class="row">
                                        <div class="col-md-4 mb-3">
                                            <div class="card border-left-info h-100">
                                                <div class="card-body py-2 text-center">
                                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                                        عدد المخازن النشطة
                                                    </div>
                                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                        {{ $storePerformance->count() }}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <div class="card border-left-success h-100">
                                                <div class="card-body py-2 text-center">
                                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                                        إجمالي الواردة
                                                    </div>
                                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                        {{ $storePerformance->sum('incoming_transfers') }}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <div class="card border-left-warning h-100">
                                                <div class="card-body py-2 text-center">
                                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                                        إجمالي الصادرة
                                                    </div>
                                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                        {{ $storePerformance->sum('outgoing_transfers') }}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Branch Statistics -->
                                <div class="col-lg-6">
                                    <h6 class="text-success mb-3">إحصائيات الفروع</h6>
                                    <div class="row">
                                        <div class="col-md-4 mb-3">
                                            <div class="card border-left-success h-100">
                                                <div class="card-body py-2 text-center">
                                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                                        عدد الفروع النشطة
                                                    </div>
                                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                        {{ $branchPerformance->count() }}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <div class="card border-left-success h-100">
                                                <div class="card-body py-2 text-center">
                                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                                        إجمالي الواردة
                                                    </div>
                                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                        {{ $branchPerformance->sum('incoming_transfers') }}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <div class="card border-left-warning h-100">
                                                <div class="card-body py-2 text-center">
                                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                                        إجمالي الصادرة
                                                    </div>
                                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                        {{ $branchPerformance->sum('outgoing_transfers') }}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @endif
    </div>

    <style>
        .avatar-circle {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 14px;
        }

        .border-left-info {
            border-left: 0.25rem solid #36b9cc !important;
        }

        .border-left-success {
            border-left: 0.25rem solid #1cc88a !important;
        }

        .border-left-warning {
            border-left: 0.25rem solid #f6c23e !important;
        }
    </style>
@endsection
