<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Support\Facades\DB;

class Customer extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'email',
        'phone',
        'address',
        'opening_balance',
        'is_active',
    ];

    protected $casts = [
        'opening_balance' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    public function sales()
    {
        return $this->hasMany(Sale::class);
    }

    public function account(): MorphOne
    {
        return $this->morphOne(Account::class, 'accountable');
    }

    public function getRemainingBalance(): float
    {
        return $this->account?->getRemainingBalance() ?? 0;
    }

    public function getOwedAmount(): float
    {
        return $this->account?->getOwedAmount() ?? 0;
    }

    public function getCreditAmount(): float
    {
        return $this->account?->getCreditAmount() ?? 0;
    }

    public function getTotalSales(): float
    {
        return $this->sales()->sum('total_amount');
    }

    /**
     * Get net sales amount (total sales minus returns)
     */
    public function getNetSalesAmount(): float
    {
        $totalSales = $this->getTotalSales();

        // Get total returns for this customer's sales
        $totalReturns = \App\Models\SaleReturn::whereHas('sale', function ($q) {
            $q->where('customer_id', $this->id);
        })
            ->where('status', 'completed')
            ->sum('total_amount');

        return $totalSales - $totalReturns;
    }

    public function getTotalPaid(): float
    {
        return $this->account?->transactions()
            ->where('type', 'deposit')
            ->sum('amount') ?? 0;
    }

    public function getOutstandingAmount(): float
    {
        return $this->getTotalSales() - $this->getTotalPaid();
    }

    /**
     * Get total outstanding amount from unpaid sales (including returns adjustment).
     */
    public function getTotalOutstandingAmount(): float
    {
        // Get total unpaid amount from sales
        $unpaidSales = $this->sales()
            ->whereRaw('total_amount - discount_amount > paid_amount')
            ->sum(DB::raw('(total_amount - discount_amount) - paid_amount'));

        // Get total returns for this customer's sales
        $totalReturns = \App\Models\SaleReturn::whereHas('sale', function ($q) {
            $q->where('customer_id', $this->id);
        })
            ->where('status', 'completed')
            ->sum('total_amount');

        // Outstanding amount should be reduced by returns
        return max(0, $unpaidSales - $totalReturns);
    }
}
