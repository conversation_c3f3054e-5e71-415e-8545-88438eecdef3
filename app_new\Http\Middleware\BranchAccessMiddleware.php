<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Models\Store;

class BranchAccessMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = $request->user();

        if (!$user) {
            return redirect()->route('login');
        }

        // Admin users can access everything
        if ($user->isAdmin()) {
            return $next($request);
        }

        // Check if accessing a specific branch resource
        $branchId = $this->extractBranchId($request);
        if ($branchId && !$user->canAccessBranch($branchId)) {
            abort(403, 'You do not have access to this branch.');
        }

        // Check if accessing a specific store resource
        $storeId = $this->extractStoreId($request);
        if ($storeId) {
            $store = Store::find($storeId);
            if ($store && !$user->canAccessStore($store)) {
                abort(403, 'You do not have access to this store.');
            }
        }

        return $next($request);
    }

    /**
     * Extract branch ID from request parameters
     */
    private function extractBranchId(Request $request): ?int
    {
        // Check route parameters
        if ($request->route('branch')) {
            return is_object($request->route('branch'))
                ? $request->route('branch')->id
                : (int) $request->route('branch');
        }

        // Check query parameters
        if ($request->has('branch_id')) {
            return (int) $request->get('branch_id');
        }

        return null;
    }

    /**
     * Extract store ID from request parameters
     */
    private function extractStoreId(Request $request): ?int
    {
        // Check route parameters
        if ($request->route('store')) {
            return is_object($request->route('store'))
                ? $request->route('store')->id
                : (int) $request->route('store');
        }

        // Check query parameters
        if ($request->has('store_id')) {
            return (int) $request->get('store_id');
        }

        return null;
    }
}
