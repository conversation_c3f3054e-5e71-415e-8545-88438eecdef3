<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\Branch;
use App\Models\Store;
use App\Models\BranchInventory;
use App\Models\StoreInventory;
use App\Models\InventoryTransfer;
use App\Models\InventoryTransferItem;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use App\Helpers\AlertHelper;

class DirectTransferController extends Controller
{
    /**
     * Show the direct transfer form
     */
    public function create(Request $request)
    {
        $user = Auth::user();

        // Get user's branch if they're a seller
        $userBranch = $user->branch;

        // Get all branches and stores for admin, or limited access for sellers
        if ($user->hasRole('admin')) {
            $branches = Branch::where('is_active', true)->get();
            $stores = Store::where('is_active', true)->get();
        } else {
            // Sellers can only transfer from their branch
            $branches = Branch::where('is_active', true)->get();
            $stores = Store::where('is_active', true)->get();
        }

        // Get products with inventory
        $products = Product::with(['branchInventories', 'storeInventories'])->get();

        // Pre-fill form if parameters are provided
        $preselected = [
            'source_type' => $request->get('source_type'),
            'source_id' => $request->get('source_id'),
            'destination_type' => $request->get('destination_type'),
            'destination_id' => $request->get('destination_id'),
            'product_id' => $request->get('product_id'),
        ];

        return view('transfers.direct-create', compact('branches', 'stores', 'products', 'userBranch', 'preselected'));
    }

    /**
     * Execute direct transfer
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'source_type' => 'required|in:branch,store',
            'source_id' => 'required|integer',
            'destination_type' => 'required|in:branch,store',
            'destination_id' => 'required|integer',
            'notes' => 'nullable|string|max:1000',
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'required|exists:products,id',
            'items.*.quantity' => 'required|numeric|min:0.01',
            'items.*.notes' => 'nullable|string|max:500',
        ]);

        $user = Auth::user();

        // Validate user permissions
        if (!$user->hasRole('admin')) {
            // Sellers can only transfer FROM their own branch (but can transfer TO any branch)
            if ($validated['source_type'] === 'branch' && $validated['source_id'] != $user->branch_id) {
                return back()->withErrors(['source_id' => 'لا يمكنك النقل من فرع آخر']);
            }
            // Allow sellers to transfer TO any branch (removed destination restriction)
        }

        // Validate that source and destination are different
        if (
            $validated['source_type'] === $validated['destination_type'] &&
            $validated['source_id'] === $validated['destination_id']
        ) {
            return back()->withErrors(['destination_id' => 'لا يمكن النقل إلى نفس الموقع']);
        }

        DB::beginTransaction();
        try {
            // Create transfer record for tracking
            $transfer = InventoryTransfer::create([
                'transfer_number' => InventoryTransfer::generateTransferNumber(),
                'type' => $validated['source_type'] . '_to_' . $validated['destination_type'],
                'source_type' => $validated['source_type'],
                'source_id' => $validated['source_id'],
                'destination_type' => $validated['destination_type'],
                'destination_id' => $validated['destination_id'],
                'requested_by' => $user->id,
                'approved_by' => $user->id, // Auto-approved for direct transfers
                'received_by' => $user->id,
                'status' => 'completed', // Direct transfers are immediately completed
                'notes' => $validated['notes'],
                'requested_at' => now(),
                'approved_at' => now(),
                'received_at' => now(),
            ]);

            // Process each item
            foreach ($validated['items'] as $itemData) {
                $product = Product::findOrFail($itemData['product_id']);
                $quantity = $itemData['quantity'];

                // Get source inventory
                $sourceInventory = $this->getInventory($validated['source_type'], $validated['source_id'], $product->id);

                if (!$sourceInventory || $sourceInventory->quantity < $quantity) {
                    throw new \Exception("المنتج {$product->name} غير متوفر بالكمية المطلوبة في المصدر");
                }

                // Remove from source
                $sourceInventory->quantity -= $quantity;
                $sourceInventory->save();

                // Add to destination
                $destinationInventory = $this->getInventory($validated['destination_type'], $validated['destination_id'], $product->id);

                if ($destinationInventory) {
                    $destinationInventory->quantity += $quantity;
                    $destinationInventory->save();
                } else {
                    // Create new inventory record
                    $this->createInventoryRecord($validated['destination_type'], $validated['destination_id'], $product->id, $quantity, $sourceInventory);
                }

                // Create transfer item record
                InventoryTransferItem::create([
                    'inventory_transfer_id' => $transfer->id,
                    'product_id' => $product->id,
                    'requested_quantity' => $quantity,
                    'approved_quantity' => $quantity,
                    'received_quantity' => $quantity,
                    'unit_cost' => $this->getUnitCost($validated['source_type'], $sourceInventory, $product),
                    'notes' => $itemData['notes'],
                ]);
            }

            DB::commit();

            AlertHelper::success('تم تنفيذ عملية النقل بنجاح');

            return redirect(user_route('transfers.show', $transfer));
        } catch (\Exception $e) {
            DB::rollBack();
            AlertHelper::error('حدث خطأ: ' . $e->getMessage());
            return back()->withInput();
        }
    }

    /**
     * Show transfer details
     */
    public function show(InventoryTransfer $transfer)
    {
        $transfer->load([
            'items.product.branchInventories',
            'requestedBy',
            'approvedBy',
            'receivedBy'
        ]);

        // Load source and destination details
        if ($transfer->source_type === 'branch') {
            $transfer->load('sourceBranch');
        } else {
            $transfer->load('sourceStore');
        }

        if ($transfer->destination_type === 'branch') {
            $transfer->load('destinationBranch');
        } else {
            $transfer->load('destinationStore');
        }

        return view('transfers.show', compact('transfer'));
    }

    /**
     * Get inventory record for a location and product
     */
    private function getInventory($locationType, $locationId, $productId)
    {
        if ($locationType === 'branch') {
            return BranchInventory::where('branch_id', $locationId)
                ->where('product_id', $productId)
                ->first();
        } else {
            return StoreInventory::where('store_id', $locationId)
                ->where('product_id', $productId)
                ->first();
        }
    }

    /**
     * Create new inventory record
     */
    private function createInventoryRecord($locationType, $locationId, $productId, $quantity, $sourceInventory = null)
    {
        if ($locationType === 'branch') {
            // Get pricing information from source inventory or product
            $costPrice = 0;
            $salePrice1 = 0; // Required field, must have a value
            $salePrice2 = null;
            $salePrice3 = null;

            if ($sourceInventory) {
                if ($sourceInventory instanceof BranchInventory) {
                    // Copy all prices from source branch inventory
                    $costPrice = $sourceInventory->cost_price ?? 0;
                    $salePrice1 = $sourceInventory->sale_price_1 ?? 0;
                    $salePrice2 = $sourceInventory->sale_price_2;
                    $salePrice3 = $sourceInventory->sale_price_3;
                } elseif ($sourceInventory instanceof StoreInventory) {
                    // Copy prices from source store inventory
                    $costPrice = $sourceInventory->cost_price ?? 0;
                    $salePrice1 = $sourceInventory->sale_price_1 ?? 0;
                    $salePrice2 = $sourceInventory->sale_price_2;
                    $salePrice3 = $sourceInventory->sale_price_3;
                }
            }

            // If no source prices available, get from product defaults
            if ($salePrice1 == 0) {
                $product = Product::find($productId);
                $salePrice1 = $product->selling_price ?? $product->price ?? 0;
                $costPrice = $costPrice ?: ($product->price ?? 0);
            }

            // Ensure sale_price_1 is never 0 or null (required field)
            $salePrice1 = max($salePrice1, 0.01); // Minimum value of 0.01

            BranchInventory::create([
                'branch_id' => $locationId,
                'product_id' => $productId,
                'quantity' => $quantity,
                'cost_price' => $costPrice,
                'sale_price_1' => $salePrice1,
                'sale_price_2' => $salePrice2,
                'sale_price_3' => $salePrice3,
            ]);
        } else {
            // Store inventory creation - copy all pricing information
            $costPrice = 0;
            $salePrice1 = 0; // Required field for stores too
            $salePrice2 = null;
            $salePrice3 = null;

            if ($sourceInventory) {
                if ($sourceInventory instanceof BranchInventory) {
                    // Copy all prices from source branch inventory
                    $costPrice = $sourceInventory->cost_price ?? 0;
                    $salePrice1 = $sourceInventory->sale_price_1 ?? 0;
                    $salePrice2 = $sourceInventory->sale_price_2;
                    $salePrice3 = $sourceInventory->sale_price_3;
                } elseif ($sourceInventory instanceof StoreInventory) {
                    // Copy all prices from source store inventory
                    $costPrice = $sourceInventory->cost_price ?? 0;
                    $salePrice1 = $sourceInventory->sale_price_1 ?? 0;
                    $salePrice2 = $sourceInventory->sale_price_2;
                    $salePrice3 = $sourceInventory->sale_price_3;
                }
            }

            // If no source prices available, get from product defaults
            if ($salePrice1 == 0) {
                $product = Product::find($productId);
                $salePrice1 = $product->selling_price ?? $product->price ?? 0;
                $costPrice = $costPrice ?: ($product->price ?? 0);
            }

            // Ensure sale_price_1 has a valid value
            $salePrice1 = max($salePrice1, 0.01); // Minimum value of 0.01

            StoreInventory::create([
                'store_id' => $locationId,
                'product_id' => $productId,
                'quantity' => $quantity,
                'minimum_stock' => 0,
                'maximum_stock' => null,
                'cost_price' => $costPrice,
                'sale_price_1' => $salePrice1,
                'sale_price_2' => $salePrice2,
                'sale_price_3' => $salePrice3,
            ]);
        }
    }

    /**
     * Get unit cost for transfer item
     */
    private function getUnitCost($sourceType, $sourceInventory, $product)
    {
        if ($sourceType === 'branch' && $sourceInventory && $sourceInventory->cost_price) {
            return $sourceInventory->cost_price;
        }

        return $product->price ?? 0;
    }

    /**
     * Get available quantity for a product at a location
     */
    public function getAvailableQuantity(Request $request)
    {
        $validated = $request->validate([
            'location_type' => 'required|in:branch,store',
            'location_id' => 'required|integer',
            'product_id' => 'required|exists:products,id',
        ]);

        $inventory = $this->getInventory(
            $validated['location_type'],
            $validated['location_id'],
            $validated['product_id']
        );

        return response()->json([
            'available_quantity' => $inventory ? $inventory->quantity : 0,
            'cost_price' => $inventory && isset($inventory->cost_price) ? $inventory->cost_price : null,
        ]);
    }

    /**
     * Get available quantities for multiple products at a location (batch request)
     */
    public function getBatchAvailableQuantities(Request $request)
    {
        $validated = $request->validate([
            'location_type' => 'required|in:branch,store',
            'location_id' => 'required|integer',
            'product_ids' => 'required|array',
            'product_ids.*' => 'exists:products,id',
        ]);

        $quantities = [];

        foreach ($validated['product_ids'] as $productId) {
            $inventory = $this->getInventory(
                $validated['location_type'],
                $validated['location_id'],
                $productId
            );

            $quantities[$productId] = [
                'available_quantity' => $inventory ? $inventory->quantity : 0,
                'cost_price' => $inventory && isset($inventory->cost_price) ? $inventory->cost_price : null,
            ];
        }

        return response()->json([
            'success' => true,
            'quantities' => $quantities
        ]);
    }
}
