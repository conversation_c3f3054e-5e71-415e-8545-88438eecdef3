<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="d-sm-flex align-items-center justify-content-between mb-4">
            <div>
                <h1 class="h3 mb-0 text-gray-800">
                    <i class="fas fa-plus text-warning me-2"></i>
                    إنشاء مرتجع مبيعات جديد
                </h1>
                <p class="mb-0 text-muted">إنشاء مرتجع جديد لعملية بيع</p>
                <div class="alert alert-info mt-2 mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>ملاحظة:</strong> سيتم إكمال المرتجع تلقائياً عند الإنشاء وتحديث المخزون والحسابات فوراً.
                </div>
            </div>
            <div class="d-flex gap-2">
                <a href="<?php echo e(user_route('sale-returns.index')); ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-2"></i>العودة إلى المرتجعات
                </a>
            </div>
        </div>

        <form method="POST" action="<?php echo e(user_route('sale-returns.store')); ?>" id="returnForm">
            <?php echo csrf_field(); ?>

            <!-- Sale Selection Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-receipt me-2"></i>اختيار عملية البيع
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="sale_id" class="form-label fw-bold">
                                <i class="fas fa-shopping-cart text-info me-2"></i>عملية البيع
                            </label>
                            <select class="form-select <?php $__errorArgs = ['sale_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="sale_id"
                                name="sale_id" required>
                                <option value="">اختر عملية البيع</option>
                                <?php $__currentLoopData = $returnableSales; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $returnableSale): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($returnableSale->id); ?>"
                                        <?php echo e(old('sale_id', $sale?->id) == $returnableSale->id ? 'selected' : ''); ?>>
                                        <?php echo e($returnableSale->invoice_number); ?> -
                                        <?php echo e($returnableSale->customer?->name ?? 'عميل نقدي'); ?>

                                        (<?php echo e(number_format($returnableSale->total_amount, 2)); ?> ج.م)
                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <?php $__errorArgs = ['sale_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="return_date" class="form-label fw-bold">
                                <i class="fas fa-calendar text-warning me-2"></i>تاريخ المرتجع
                            </label>
                            <input type="date" class="form-control <?php $__errorArgs = ['return_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                id="return_date" name="return_date" value="<?php echo e(old('return_date', date('Y-m-d'))); ?>"
                                required>
                            <?php $__errorArgs = ['return_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sale Details Card -->
            <div class="card shadow mb-4" id="saleDetailsCard" style="display: none;">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-info-circle me-2"></i>تفاصيل عملية البيع
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label class="form-label fw-bold text-muted">العميل</label>
                            <div class="fw-bold text-primary" id="customerName">-</div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label class="form-label fw-bold text-muted">المبلغ الإجمالي</label>
                            <div class="fw-bold text-primary" id="totalAmount">-</div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label class="form-label fw-bold text-muted">مبلغ الخصم</label>
                            <div class="fw-bold text-warning" id="discountAmount">-</div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label class="form-label fw-bold text-muted">المبلغ بعد الخصم</label>
                            <div class="fw-bold text-info" id="netAmount">-</div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label class="form-label fw-bold text-muted">المبلغ المدفوع</label>
                            <div class="fw-bold text-success" id="paidAmount">-</div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label class="form-label fw-bold text-muted">المبلغ المتبقي</label>
                            <div class="fw-bold text-danger" id="remainingAmount">-</div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label class="form-label fw-bold text-muted">حالة الدفع</label>
                            <div id="paymentStatus">-</div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label class="form-label fw-bold text-muted">رقم الفاتورة</label>
                            <div class="fw-bold" id="invoiceNumber">-</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Return Details Card -->
            <div class="card shadow mb-4" id="returnDetailsCard" style="display: none;">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-undo me-2"></i>تفاصيل المرتجع
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="return_type" class="form-label fw-bold">
                                <i class="fas fa-tags text-info me-2"></i>نوع المرتجع
                            </label>
                            <select name="return_type" id="return_type"
                                class="form-select <?php $__errorArgs = ['return_type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" required>
                                <option value="">اختر نوع المرتجع...</option>
                                <option value="partial" <?php echo e(old('return_type') == 'partial' ? 'selected' : ''); ?>>
                                    مرتجع جزئي</option>
                                <option value="full" <?php echo e(old('return_type') == 'full' ? 'selected' : ''); ?>>
                                    مرتجع كامل</option>
                            </select>
                            <?php $__errorArgs = ['return_type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="refund_amount" class="form-label fw-bold">
                                <i class="fas fa-money-bill text-success me-2"></i>مبلغ الاسترداد
                            </label>
                            <div class="input-group">
                                <input type="number" step="0.01" min="0"
                                    class="form-control <?php $__errorArgs = ['refund_amount'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                    id="refund_amount" name="refund_amount" value="<?php echo e(old('refund_amount', 0)); ?>"
                                    required>
                                <span class="input-group-text">ج.م</span>
                            </div>
                            <?php $__errorArgs = ['refund_amount'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="return_type" class="form-label fw-bold">
                                <i class="fas fa-exchange-alt text-info me-2"></i>نوع المرتجع
                            </label>
                            <select class="form-select <?php $__errorArgs = ['return_type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="return_type"
                                name="return_type" required>
                                <option value="">اختر نوع المرتجع...</option>
                                <option value="partial" <?php echo e(old('return_type') == 'partial' ? 'selected' : ''); ?>>مرتجع
                                    جزئي</option>
                                <option value="full" <?php echo e(old('return_type') == 'full' ? 'selected' : ''); ?>>مرتجع كامل
                                </option>
                            </select>
                            <?php $__errorArgs = ['return_type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="reason" class="form-label fw-bold">
                                <i class="fas fa-question-circle text-warning me-2"></i>سبب المرتجع
                            </label>
                            <textarea class="form-control <?php $__errorArgs = ['reason'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="reason" name="reason" rows="2"
                                placeholder="اكتب سبب المرتجع..." required><?php echo e(old('reason')); ?></textarea>
                            <?php $__errorArgs = ['reason'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="refund_amount" class="form-label fw-bold">
                                <i class="fas fa-money-bill text-success me-2"></i>مبلغ الاسترداد
                            </label>
                            <input type="number" class="form-control <?php $__errorArgs = ['refund_amount'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                id="refund_amount" name="refund_amount" step="0.01" min="0"
                                value="<?php echo e(old('refund_amount', 0)); ?>" required>
                            <?php $__errorArgs = ['refund_amount'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        <div class="col-md-12 mb-3">
                            <label for="notes" class="form-label fw-bold">
                                <i class="fas fa-sticky-note text-warning me-2"></i>ملاحظات
                            </label>
                            <textarea class="form-control <?php $__errorArgs = ['notes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="notes" name="notes" rows="3"
                                placeholder="أي ملاحظات إضافية حول المرتجع..."><?php echo e(old('notes')); ?></textarea>
                            <?php $__errorArgs = ['notes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Items Selection Card -->
            <div class="card shadow mb-4" id="itemsCard" style="display: none;">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-boxes me-2"></i>المنتجات المراد إرجاعها
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" id="itemsTable">
                            <thead class="table-light">
                                <tr>
                                    <th width="5%">
                                        <input type="checkbox" id="selectAll" class="form-check-input">
                                    </th>
                                    <th width="30%">المنتج</th>
                                    <th width="15%">الكمية الأصلية</th>
                                    <th width="15%">الكمية المتاحة</th>
                                    <th width="15%">الكمية المرتجعة</th>
                                    <th width="10%">الحالة</th>
                                    <th width="10%">ملاحظات</th>
                                </tr>
                            </thead>
                            <tbody id="itemsTableBody">
                                <!-- Items will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                    <div id="emptyItemsMessage" class="text-center py-5 text-muted">
                        <i class="fas fa-boxes fa-3x mb-3"></i>
                        <h5>اختر عملية بيع أولاً</h5>
                        <p>قم بتحديد عملية البيع لعرض المنتجات المتاحة للإرجاع</p>
                    </div>
                </div>
            </div>

            <!-- Summary Card -->
            <div class="card shadow mb-4" id="summaryCard" style="display: none;">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-calculator me-2"></i>ملخص المرتجع
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="text-center">
                                <h5 class="text-primary">عدد المنتجات</h5>
                                <h3 class="text-primary" id="totalItems">0</h3>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <h5 class="text-warning">إجمالي الكمية</h5>
                                <h3 class="text-warning" id="totalQuantity">0</h3>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <h5 class="text-success">إجمالي المبلغ</h5>
                                <h3 class="text-success" id="totalReturnAmount">0.00 ج.م</h3>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="card shadow mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                تأكد من صحة البيانات قبل الحفظ
                            </small>
                        </div>
                        <div class="d-flex gap-2">
                            <a href="<?php echo e(user_route('sale-returns.index')); ?>" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </a>
                            <button type="submit" class="btn btn-warning btn-lg" id="submitBtn" disabled>
                                <i class="fas fa-save me-2"></i>إنشاء المرتجع
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <?php $__env->startPush('scripts'); ?>
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const saleSelect = document.getElementById('sale_id');
                const saleDetails = document.getElementById('saleDetails');
                const itemsCard = document.getElementById('itemsCard');
                const summaryCard = document.getElementById('summaryCard');
                const itemsTableBody = document.getElementById('itemsTableBody');
                const emptyItemsMessage = document.getElementById('emptyItemsMessage');
                const submitBtn = document.getElementById('submitBtn');
                const selectAllCheckbox = document.getElementById('selectAll');

                let saleItems = [];

                // Load sale details when sale is selected
                saleSelect.addEventListener('change', function() {
                    const saleId = this.value;

                    if (saleId) {
                        loadSaleDetails(saleId);
                    } else {
                        hideSaleDetails();
                    }
                });

                function loadSaleDetails(saleId) {
                    const url =
                        <?php if(auth()->user()->isAdmin()): ?>
                            '<?php echo e(route('admin.sale-returns.sale-details')); ?>'
                        <?php else: ?>
                            '<?php echo e(route('seller.sale-returns.sale-details')); ?>'
                        <?php endif; ?> ;
                    fetch(`${url}?sale_id=${saleId}`)
                        .then(response => response.json())
                        .then(data => {
                            if (data.error) {
                                alert(data.error);
                                return;
                            }

                            const sale = data.sale;
                            saleItems = sale.items;

                            // Update sale details
                            document.getElementById('customerName').textContent = sale.customer_name;
                            document.getElementById('totalAmount').textContent = sale.total_amount + ' ج.م';
                            document.getElementById('discountAmount').textContent = (sale.discount_amount || 0) +
                                ' ج.م';
                            document.getElementById('netAmount').textContent = (sale.total_amount - (sale
                                .discount_amount || 0)) + ' ج.م';
                            document.getElementById('paidAmount').textContent = sale.paid_amount + ' ج.م';
                            document.getElementById('remainingAmount').textContent = sale.remaining_amount + ' ج.م';
                            document.getElementById('invoiceNumber').textContent = sale.invoice_number;

                            // Update payment status
                            const paymentStatusElement = document.getElementById('paymentStatus');
                            if (sale.payment_status === 'paid') {
                                paymentStatusElement.innerHTML =
                                    '<span class="badge bg-success"><i class="fas fa-check-circle me-1"></i>مدفوع بالكامل</span>';
                            } else if (sale.payment_status === 'partial') {
                                paymentStatusElement.innerHTML =
                                    '<span class="badge bg-warning"><i class="fas fa-clock me-1"></i>مدفوع جزئياً</span>';
                            } else {
                                paymentStatusElement.innerHTML =
                                    '<span class="badge bg-danger"><i class="fas fa-times-circle me-1"></i>غير مدفوع</span>';
                            }

                            // Show sale details and return details cards
                            document.getElementById('saleDetailsCard').style.display = 'block';
                            document.getElementById('returnDetailsCard').style.display = 'block';

                            // Load items
                            loadItems();
                        })
                        .catch(error => {
                            console.error('Error loading sale details:', error);
                            alert('حدث خطأ أثناء تحميل تفاصيل العملية');
                        });
                }

                function hideSaleDetails() {
                    document.getElementById('saleDetailsCard').style.display = 'none';
                    document.getElementById('returnDetailsCard').style.display = 'none';
                    itemsCard.style.display = 'none';
                    summaryCard.style.display = 'none';
                    submitBtn.disabled = true;
                    saleItems = [];
                }

                function loadItems() {
                    if (saleItems.length === 0) {
                        emptyItemsMessage.style.display = 'block';
                        itemsCard.style.display = 'none';
                        return;
                    }

                    emptyItemsMessage.style.display = 'none';
                    itemsCard.style.display = 'block';
                    summaryCard.style.display = 'block';

                    itemsTableBody.innerHTML = '';

                    saleItems.forEach((item, index) => {
                        const row = createItemRow(item, index);
                        itemsTableBody.appendChild(row);
                    });

                    updateSummary();
                }

                function createItemRow(item, index) {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>
                            <input type="checkbox" class="form-check-input item-checkbox" data-index="${index}">
                        </td>
                        <td>
                            <div class="fw-bold">${item.product_name}</div>
                            <small class="text-muted">السعر: ${item.sale_price} ج.م</small>
                        </td>
                        <td>
                            <span class="badge bg-info">${item.quantity}</span>
                        </td>
                        <td>
                            <span class="badge bg-success">${item.available_quantity}</span>
                        </td>
                        <td>
                            <input type="number" name="items[${index}][quantity_returned]"
                                   class="form-control quantity-input"
                                   min="0.01" max="${item.available_quantity}" step="0.01"
                                   data-index="${index}" disabled>
                            <input type="hidden" name="items[${index}][sale_item_id]" value="${item.id}">
                        </td>
                        <td>
                            <select name="items[${index}][condition]" class="form-select condition-select" data-index="${index}" disabled>
                                <option value="good">جيد</option>
                                <option value="damaged">تالف</option>
                                <option value="expired">منتهي الصلاحية</option>
                                <option value="defective">معيب</option>
                            </select>
                        </td>
                        <td>
                            <input type="text" name="items[${index}][item_notes]"
                                   class="form-control notes-input"
                                   placeholder="ملاحظات" data-index="${index}" disabled>
                        </td>
                    `;

                    return row;
                }

                // Handle item checkbox changes
                document.addEventListener('change', function(e) {
                    if (e.target.classList.contains('item-checkbox')) {
                        const index = e.target.dataset.index;
                        const isChecked = e.target.checked;
                        const row = e.target.closest('tr');

                        // Enable/disable inputs
                        const inputs = row.querySelectorAll('input:not(.item-checkbox), select');
                        inputs.forEach(input => {
                            input.disabled = !isChecked;
                            if (!isChecked) {
                                input.value = input.type === 'number' ? '' : (input.tagName ===
                                    'SELECT' ? 'good' : '');
                            }
                        });

                        updateSummary();
                        updateSelectAll();
                    }

                    if (e.target.classList.contains('quantity-input')) {
                        updateSummary();
                    }
                });

                // Handle select all checkbox
                selectAllCheckbox.addEventListener('change', function() {
                    const checkboxes = document.querySelectorAll('.item-checkbox');
                    checkboxes.forEach(checkbox => {
                        checkbox.checked = this.checked;
                        checkbox.dispatchEvent(new Event('change'));
                    });
                });

                function updateSelectAll() {
                    const checkboxes = document.querySelectorAll('.item-checkbox');
                    const checkedBoxes = document.querySelectorAll('.item-checkbox:checked');

                    selectAllCheckbox.checked = checkboxes.length > 0 && checkboxes.length === checkedBoxes.length;
                    selectAllCheckbox.indeterminate = checkedBoxes.length > 0 && checkedBoxes.length < checkboxes
                        .length;
                }

                function updateSummary() {
                    let totalItems = 0;
                    let totalQuantity = 0;
                    let totalAmount = 0;

                    document.querySelectorAll('.item-checkbox:checked').forEach(checkbox => {
                        const index = checkbox.dataset.index;
                        const quantityInput = document.querySelector(
                            `input[name="items[${index}][quantity_returned]"]`);
                        const quantity = parseFloat(quantityInput.value) || 0;

                        if (quantity > 0) {
                            totalItems++;
                            totalQuantity += quantity;
                            totalAmount += quantity * saleItems[index].sale_price;
                        }
                    });

                    document.getElementById('totalItems').textContent = totalItems;
                    document.getElementById('totalQuantity').textContent = totalQuantity;
                    document.getElementById('totalReturnAmount').textContent = totalAmount + ' ج.م';

                    // Update refund amount automatically
                    document.getElementById('refund_amount').value = totalAmount.toFixed(2);

                    // Enable/disable submit button
                    submitBtn.disabled = totalItems === 0;
                }

                // Trigger change event if sale is pre-selected
                if (saleSelect.value) {
                    saleSelect.dispatchEvent(new Event('change'));
                }
            });
        </script>
    <?php $__env->stopPush(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH D:\pos-app\resources\views/sale-returns/create.blade.php ENDPATH**/ ?>