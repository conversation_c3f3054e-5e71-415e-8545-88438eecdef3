<x-app-layout>
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-0 text-gray-800">المدفوعات المعلقة للموردين</h1>
                <p class="text-muted mb-0">إدارة المدفوعات المتبقية لعمليات الشراء</p>
            </div>
            <div>
                <a href="{{ user_route('purchases.index') }}" class="btn btn-outline-primary">
                    <i class="fas fa-shopping-cart me-2"></i>عمليات الشراء
                </a>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-xl-4 col-md-6 mb-4">
                <div class="card border-left-danger shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                    إجمالي المبالغ المعلقة
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    {{ format_currency($totalOutstanding) }}
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-4 col-md-6 mb-4">
                <div class="card border-left-warning shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                    عدد العمليات المعلقة
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    {{ number_format($totalPurchases) }}
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-file-invoice fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-4 col-md-6 mb-4">
                <div class="card border-left-info shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                    عدد الموردين
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    {{ number_format($suppliersWithOutstanding) }}
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-users fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">فلترة النتائج</h6>
            </div>
            <div class="card-body">
                <form method="GET" action="{{ user_route('supplier-payments.index') }}">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label for="supplier_id" class="form-label">المورد</label>
                            <select name="supplier_id" id="supplier_id" class="form-select">
                                <option value="">جميع الموردين</option>
                                @foreach ($suppliers as $supplier)
                                    <option value="{{ $supplier->id }}"
                                        {{ request('supplier_id') == $supplier->id ? 'selected' : '' }}>
                                        {{ $supplier->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="date_from" class="form-label">من تاريخ</label>
                            <input type="date" name="date_from" id="date_from" class="form-control"
                                value="{{ request('date_from') }}">
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="date_to" class="form-label">إلى تاريخ</label>
                            <input type="date" name="date_to" id="date_to" class="form-control"
                                value="{{ request('date_to') }}">
                        </div>
                        <div class="col-md-3 mb-3 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="fas fa-search me-1"></i>بحث
                            </button>
                            <a href="{{ user_route('supplier-payments.index') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i>إلغاء
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Outstanding Purchases Table -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">عمليات الشراء المعلقة</h6>
            </div>
            <div class="card-body">
                @if ($outstandingPurchases->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>رقم العملية</th>
                                    <th>المورد</th>
                                    <th>تاريخ الشراء</th>
                                    <th>إجمالي المبلغ</th>
                                    <th>المبلغ المدفوع</th>
                                    <th>المبلغ المتبقي</th>
                                    <th>حالة الدفع</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($outstandingPurchases as $purchase)
                                    <tr>
                                        <td>
                                            <a href="{{ user_route('purchases.show', $purchase) }}"
                                                class="text-decoration-none">
                                                {{ $purchase->invoice_number }}
                                            </a>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-circle bg-primary text-white me-2">
                                                    {{ substr($purchase->supplier->name, 0, 1) }}
                                                </div>
                                                <div>
                                                    <div class="fw-bold">{{ $purchase->supplier->name }}</div>
                                                    @if ($purchase->supplier->phone)
                                                        <small
                                                            class="text-muted">{{ $purchase->supplier->phone }}</small>
                                                    @endif
                                                </div>
                                            </div>
                                        </td>
                                        <td>{{ $purchase->purchase_date }}</td>
                                        <td>{{ format_currency($purchase->total_amount) }}</td>
                                        <td>{{ format_currency($purchase->paid_amount) }}</td>
                                        <td>
                                            <span class="fw-bold text-danger">
                                                {{ format_currency($purchase->actual_remaining_amount) }}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-{{ $purchase->payment_status_color }}">
                                                {{ $purchase->payment_status }}
                                            </span>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ user_route('supplier-payments.create', $purchase) }}"
                                                    class="btn btn-sm btn-success" title="دفع">
                                                    <i class="fas fa-money-bill-wave"></i>
                                                </a>
                                                <a href="{{ user_route('supplier-payments.show', $purchase) }}"
                                                    class="btn btn-sm btn-info" title="تاريخ المدفوعات">
                                                    <i class="fas fa-history"></i>
                                                </a>
                                                <a href="{{ user_route('purchases.show', $purchase) }}"
                                                    class="btn btn-sm btn-primary" title="عرض">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-center">
                        {{ $outstandingPurchases->appends(request()->query())->links() }}
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                        <h5>لا توجد مدفوعات معلقة</h5>
                        <p class="text-muted">جميع عمليات الشراء مدفوعة بالكامل</p>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <style>
        .avatar-circle {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 14px;
        }

        .border-left-danger {
            border-left: 0.25rem solid #e74a3b !important;
        }

        .border-left-warning {
            border-left: 0.25rem solid #f39c12 !important;
        }

        .border-left-info {
            border-left: 0.25rem solid #36b9cc !important;
        }
    </style>
</x-app-layout>
