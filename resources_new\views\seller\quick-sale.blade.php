<x-app-layout>
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h2 class="h3 mb-0">بيع سريع</h2>
                    <div class="text-muted">
                        <i class="fas fa-store"></i> {{ auth()->user()->branch->name ?? 'غير محدد' }}
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Product Selection -->
            <div class="col-lg-8 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-search"></i> البحث عن المنتجات
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- Search Bar -->
                        <div class="row mb-3">
                            <div class="col-md-8">
                                <div class="input-group">
                                    <input type="text" id="productSearch" class="form-control"
                                        placeholder="ابحث عن المنتج بالاسم أو الباركود...">
                                    <button class="btn btn-primary" type="button" id="searchBtn">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <select id="categoryFilter" class="form-select">
                                    <option value="">جميع الفئات</option>
                                    @foreach ($categories as $category)
                                        <option value="{{ $category->id }}">{{ $category->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>

                        <!-- Products Grid -->
                        <div id="productsGrid" class="row">
                            @foreach ($products as $product)
                                <div class="col-lg-3 col-md-4 col-sm-6 mb-3 product-item"
                                    data-category="{{ $product->category_id }}"
                                    data-name="{{ strtolower($product->name) }}" data-barcode="{{ $product->barcode }}">
                                    <div class="card h-100 product-card {{ $product->availability_status === 'local' ? '' : 'border-warning' }}"
                                        style="cursor: pointer;" data-product-id="{{ $product->id }}"
                                        data-product-name="{{ $product->name }}"
                                        data-product-price="{{ number_format($product->selling_price ?? 0, 2, '.', '') }}"
                                        data-product-stock="{{ $product->local_quantity }}"
                                        data-availability-status="{{ $product->availability_status }}"
                                        data-availability-text="{{ $product->availability_text }}"
                                        data-total-stock="{{ $product->total_quantity }}"
                                        onclick="handleProductClick(this)">

                                        <!-- Availability indicator -->
                                        @if ($product->availability_status !== 'local')
                                            <div class="position-absolute top-0 end-0 p-1">
                                                @if ($product->availability_status === 'transferable')
                                                    <i class="fas fa-exchange-alt text-warning"
                                                        title="متوفر في مواقع أخرى"></i>
                                                @else
                                                    <i class="fas fa-times-circle text-danger" title="غير متوفر"></i>
                                                @endif
                                            </div>
                                        @endif

                                        <div class="card-body text-center">
                                            <div class="mb-2">
                                                @if ($product->image)
                                                    <img src="{{ asset('storage/' . $product->image) }}"
                                                        class="img-fluid rounded" style="max-height: 60px;">
                                                @else
                                                    <i class="fas fa-box fa-3x text-muted"></i>
                                                @endif
                                            </div>
                                            <h6 class="card-title mb-1">{{ $product->name }}</h6>
                                            <p class="card-text small text-muted mb-1">
                                                {{ $product->category->name ?? 'بدون فئة' }}</p>

                                            <!-- Availability info -->
                                            <div class="mb-2">
                                                <span class="badge bg-{{ $product->availability_class }} small">
                                                    {{ $product->availability_text }}
                                                </span>
                                                @if ($product->local_quantity > 0)
                                                    <div class="small text-muted">محلياً:
                                                        {{ $product->local_quantity }}</div>
                                                @endif
                                                @if ($product->availability_status === 'transferable')
                                                    <div class="small text-warning">إجمالي:
                                                        {{ $product->total_quantity }}</div>
                                                @endif
                                            </div>

                                            <div class="d-flex justify-content-between align-items-center">
                                                <span
                                                    class="fw-bold text-primary">{{ format_currency($product->selling_price) }}</span>
                                                @if ($product->availability_status === 'transferable' && $product->local_quantity == 0)
                                                    <button class="btn btn-sm btn-outline-warning"
                                                        onclick="event.stopPropagation(); showDetailedAvailability({{ $product->id }}, '{{ $product->name }}', {{ json_encode($product->all_available_locations) }})">
                                                        <i class="fas fa-exchange-alt"></i>
                                                    </button>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>

                        @if ($products->isEmpty())
                            <div class="text-center py-5">
                                <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">لا توجد منتجات متاحة</h5>
                                <p class="text-muted">تأكد من إضافة منتجات إلى المخزون أولاً</p>
                                @if (config('app.debug'))
                                    <div class="mt-3 text-start">
                                        <small class="text-muted">
                                            <strong>Debug Info:</strong><br>
                                            User Branch ID: {{ auth()->user()->branch_id ?? 'null' }}<br>
                                            Accessible Store IDs:
                                            {{ json_encode(auth()->user()->getAccessibleStoreIds()) }}<br>
                                            Can Access Independent Stores:
                                            {{ auth()->user()->canAccessIndependentStores() ? 'Yes' : 'No' }}<br>
                                            Total Products in DB: {{ \App\Models\Product::count() }}<br>
                                            Products with Branch Inventory:
                                            {{ \App\Models\Product::whereHas('branchInventories')->count() }}<br>
                                            Products with Store Inventory:
                                            {{ \App\Models\Product::whereHas('storeInventories')->count() }}
                                        </small>
                                    </div>
                                @endif
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Shopping Cart -->
            <div class="col-lg-4 mb-4">
                <div class="card sticky-top" style="top: 20px;">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-shopping-cart"></i> سلة المشتريات
                        </h5>
                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="clearCart()">
                            <i class="fas fa-trash"></i> مسح الكل
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="text-center text-muted py-4" id="emptyCart">
                            <i class="fas fa-shopping-cart fa-2x mb-2"></i>
                            <p>السلة فارغة</p>
                            <small>اختر المنتجات لإضافتها</small>
                        </div>
                        <div id="cartItems"></div>

                        <!-- Cart Summary -->
                        <div id="cartSummary" style="display: none;">
                            <hr>
                            <div class="d-flex justify-content-between mb-3">
                                <strong>الإجمالي:</strong>
                                <strong id="total" class="text-primary">0.00 {{ currency_symbol() }}</strong>
                            </div>

                            <!-- Customer Selection -->
                            <div class="mb-3">
                                <label for="customer_id" class="form-label">العميل (اختياري)</label>
                                <select id="customer_id" class="form-select">
                                    <option value="">عميل نقدي</option>
                                    @foreach ($customers as $customer)
                                        <option value="{{ $customer->id }}">{{ $customer->name }}</option>
                                    @endforeach
                                </select>
                            </div>

                            <!-- Payment Method -->
                            <div class="mb-3">
                                <label for="payment_method" class="form-label">طريقة الدفع</label>
                                <select id="payment_method" class="form-select">
                                    <option value="cash">نقدي</option>
                                    <option value="card">بطاقة ائتمان</option>
                                    <option value="credit">آجل</option>
                                </select>
                            </div>

                            <!-- Amount Paid (for credit sales) -->
                            <div class="mb-3" id="amountPaidDiv" style="display: none;">
                                <label for="amount_paid" class="form-label">المبلغ المدفوع</label>
                                <input type="number" id="amount_paid" class="form-control" step="0.01"
                                    min="0">
                            </div>

                            <!-- Complete Sale Button -->
                            <button type="button" class="btn btn-success w-100 btn-lg" onclick="completeSale()">
                                <i class="fas fa-check"></i> إتمام البيع
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Product Availability Modal -->
    <div class="modal fade" id="availabilityModal" tabindex="-1" aria-labelledby="availabilityModalLabel"
        aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="availabilityModalLabel">توفر المنتج في المواقع</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="modalProductName" class="mb-3">
                        <h6 class="text-primary"></h6>
                    </div>
                    <div id="modalAvailabilityList">
                        <!-- Locations will be populated here -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    <button type="button" class="btn btn-warning" id="modalRequestTransferBtn">
                        <i class="fas fa-exchange-alt"></i> طلب نقل
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript for Quick Sale -->
    <script>
        let cart = [];
        let cartTotal = 0;

        // Handle product click based on availability status
        function handleProductClick(element) {
            const availabilityStatus = element.dataset.availabilityStatus;
            const localStock = parseInt(element.dataset.productStock) || 0;

            if (availabilityStatus === 'local' && localStock > 0) {
                // Product is available locally, add to cart
                addToCartFromData(element);
            } else if (availabilityStatus === 'transferable') {
                // Product is available elsewhere, show transfer option
                showTransferDialog(element);
            } else {
                // Product is not available anywhere
                toastr.error('المنتج غير متوفر حالياً');
            }
        }

        // Show transfer dialog for products available in other locations
        function showTransferDialog(element) {
            const productId = parseInt(element.dataset.productId);
            const productName = element.dataset.productName;
            const totalStock = parseInt(element.dataset.totalStock) || 0;
            const availabilityText = element.dataset.availabilityText || 'متوفر في مواقع أخرى';

            if (confirm(
                    `المنتج "${productName}" غير متوفر محلياً.\n\n${availabilityText}\n\nإجمالي الكمية المتوفرة: ${totalStock}\n\nهل تريد طلب نقله إلى فرعك؟`
                )) {
                requestTransfer(productId, 1); // Request 1 unit by default
            }
        }

        // Show detailed availability modal
        function showDetailedAvailability(productId, productName, locations) {
            // Set product name
            document.querySelector('#modalProductName h6').textContent = productName;

            // Build locations list
            let locationsHtml = '';
            if (locations && locations.length > 0) {
                locationsHtml = '<div class="list-group">';
                locations.forEach(location => {
                    const iconClass = location.location_type === 'branch' ? 'fa-building text-primary' :
                        'fa-warehouse text-info';
                    locationsHtml += `
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <i class="fas ${iconClass} me-2"></i>
                                <strong>${location.location_display}</strong>
                                ${location.branch_code || location.store_code ? `<small class="text-muted"> (${location.branch_code || location.store_code})</small>` : ''}
                            </div>
                            <span class="badge bg-primary rounded-pill">${location.quantity} قطعة</span>
                        </div>
                    `;
                });
                locationsHtml += '</div>';
            } else {
                locationsHtml = '<p class="text-muted">لا توجد مواقع متوفرة</p>';
            }

            document.getElementById('modalAvailabilityList').innerHTML = locationsHtml;

            // Set up transfer button
            const transferBtn = document.getElementById('modalRequestTransferBtn');
            transferBtn.onclick = function() {
                requestTransfer(productId, 1);
                bootstrap.Modal.getInstance(document.getElementById('availabilityModal')).hide();
            };

            // Show modal
            new bootstrap.Modal(document.getElementById('availabilityModal')).show();
        }

        // Request transfer for a product from best available location
        function requestTransfer(productId, quantity = 1) {
            // Show quantity input dialog
            const requestedQuantity = prompt('كم قطعة تريد نقلها؟', quantity);

            if (requestedQuantity && parseInt(requestedQuantity) > 0) {
                // Send auto transfer request (system will find best source location)
                fetch('{{ route('seller.transfer.auto') }}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': '{{ csrf_token() }}'
                        },
                        body: JSON.stringify({
                            product_id: productId,
                            quantity: parseInt(requestedQuantity)
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            toastr.success('تم تنفيذ عملية النقل بنجاح');
                            // Refresh the product list to show updated quantities
                            location.reload();
                        } else {
                            toastr.error(data.message || 'حدث خطأ أثناء تنفيذ عملية النقل');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        toastr.error('حدث خطأ أثناء تنفيذ عملية النقل');
                    });
            }
        }

        // Add product to cart from data attributes
        async function addToCartFromData(element) {
            const productId = parseInt(element.dataset.productId);
            const productName = element.dataset.productName;
            const fallbackPrice = parseFloat(element.dataset.productPrice) || 0;
            const stock = parseInt(element.dataset.productStock) || 0;

            try {
                // Get the correct price from the server
                const branchId = {{ auth()->user()->branch_id }};
                const response = await fetch(
                    `{{ route('seller.sales.product-price') }}?product_id=${productId}&branch_id=${branchId}`);
                const data = await response.json();

                let price = fallbackPrice;
                if (data.success) {
                    price = data.price_info.price;

                    // Show price source info if different from fallback
                    if (Math.abs(price - fallbackPrice) > 0.01) {
                        const priceSourceText = getPriceSourceText(data.price_info.source);
                        toastr.info(`تم تحديث السعر إلى ${price.toFixed(2)} ج.م (${priceSourceText})`);
                    }
                }

                addToCart(productId, productName, price, stock);
            } catch (error) {
                console.error('Error loading price:', error);
                // Fallback to original price
                addToCart(productId, productName, fallbackPrice, stock);
            }
        }

        function getPriceSourceText(source) {
            const sources = {
                'branch_inventory': 'سعر الفرع',
                'product_selling_price': 'سعر المنتج',
                'product_price': 'السعر الأساسي',
                'cost_plus_calculated': 'محسوب تلقائياً'
            };
            return sources[source] || 'غير محدد';
        }

        // Add product to cart
        function addToCart(productId, productName, price, stock) {
            console.log('addToCart called:', {
                productId,
                productName,
                price,
                stock
            });

            if (stock <= 0) {
                toastr.error('المنتج غير متوفر في المخزون');
                return;
            }

            // Check if product already in cart
            let existingItem = cart.find(item => item.id === productId);

            if (existingItem) {
                if (existingItem.quantity >= stock) {
                    toastr.error('لا يمكن إضافة كمية أكثر من المتوفر في المخزون');
                    return;
                }
                existingItem.quantity++;
            } else {
                cart.push({
                    id: productId,
                    name: productName,
                    price: price,
                    quantity: 1,
                    stock: stock
                });
            }

            updateCartDisplay();
            toastr.success('تم إضافة المنتج إلى السلة');
        }

        // Update cart display
        function updateCartDisplay() {
            console.log('Updating cart display, cart:', cart);

            const cartItemsDiv = document.getElementById('cartItems');
            const emptyCart = document.getElementById('emptyCart');
            const cartSummary = document.getElementById('cartSummary');

            if (cart.length === 0) {
                emptyCart.style.display = 'block';
                cartSummary.style.display = 'none';
                cartItemsDiv.innerHTML = '';
                return;
            }

            emptyCart.style.display = 'none';
            cartSummary.style.display = 'block';

            let cartHTML = '';
            let subtotal = 0;

            cart.forEach((item, index) => {
                const itemTotal = item.price * item.quantity;
                subtotal += itemTotal;

                cartHTML += `
                    <div class="d-flex justify-content-between align-items-center mb-2 p-2 border rounded">
                        <div class="flex-grow-1">
                            <h6 class="mb-1">${item.name}</h6>
                            <small class="text-muted">${item.price.toFixed(2)} {{ currency_symbol() }} × ${item.quantity}</small>
                        </div>
                        <div class="text-end">
                            <div class="btn-group btn-group-sm mb-1">
                                <button class="btn btn-outline-secondary" onclick="decreaseQuantity(${index})">-</button>
                                <button class="btn btn-outline-secondary" onclick="increaseQuantity(${index})">+</button>
                            </div>
                            <div>
                                <strong>${itemTotal.toFixed(2)} {{ currency_symbol() }}</strong>
                                <button class="btn btn-sm btn-outline-danger ms-1" onclick="removeFromCart(${index})">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                `;
            });

            cartItemsDiv.innerHTML = cartHTML;

            // Update totals (no tax)
            const total = subtotal;

            document.getElementById('total').textContent = total.toFixed(2) + ' {{ currency_symbol() }}';

            cartTotal = total;
        }

        // Increase quantity
        function increaseQuantity(index) {
            console.log('Increasing quantity for index:', index, 'Current item:', cart[index]);
            if (cart[index].quantity >= cart[index].stock) {
                toastr.error('لا يمكن إضافة كمية أكثر من المتوفر في المخزون');
                return;
            }
            cart[index].quantity++;
            updateCartDisplay();
        }

        // Decrease quantity
        function decreaseQuantity(index) {
            console.log('Decreasing quantity for index:', index, 'Current item:', cart[index]);
            if (cart[index].quantity > 1) {
                cart[index].quantity--;
                updateCartDisplay();
            }
        }

        // Remove from cart
        function removeFromCart(index) {
            console.log('Removing item at index:', index, 'Item:', cart[index]);
            cart.splice(index, 1);
            updateCartDisplay();
        }

        // Clear cart
        function clearCart() {
            cart = [];
            updateCartDisplay();
        }

        // Complete sale
        function completeSale() {
            if (cart.length === 0) {
                toastr.error('السلة فارغة');
                return;
            }

            console.log('Cart contents:', cart);
            console.log('Cart length:', cart.length);

            @if (!auth()->user()->branch_id)
                toastr.error('لا يمكن إتمام البيع - المستخدم غير مرتبط بفرع');
                return;
            @endif

            const customerId = document.getElementById('customer_id').value;
            const paymentMethod = document.getElementById('payment_method').value;
            const amountPaid = document.getElementById('amount_paid').value || cartTotal;

            // Prepare sale data
            const saleData = {
                branch_id: {{ auth()->user()->branch_id ?? 'null' }},
                payment_method: paymentMethod,
                paid_amount: parseFloat(amountPaid),
                notes: 'بيع سريع',
                products: cart.map(item => ({
                    product_id: item.id,
                    quantity: item.quantity,
                    price: item.price
                }))
            };

            // Only add customer_id if a customer is selected
            if (customerId) {
                saleData.customer_id = customerId;
            }

            console.log('Sending sale data:', saleData);

            // Send to server
            fetch('{{ route('seller.quick-sale.process') }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                        'Accept': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify(saleData)
                })
                .then(response => {
                    console.log('Response status:', response.status);
                    console.log('Response headers:', response.headers);

                    // Check if response is JSON first
                    const contentType = response.headers.get('content-type');
                    if (!contentType || !contentType.includes('application/json')) {
                        return response.text().then(text => {
                            console.error('Non-JSON response:', text);
                            throw new Error('Server returned non-JSON response: ' + text.substring(0, 200));
                        });
                    }

                    // Parse JSON even for error responses
                    return response.json().then(data => {
                        if (!response.ok) {
                            // Include the parsed error data in the error
                            const error = new Error(`HTTP error! status: ${response.status}`);
                            error.data = data;
                            throw error;
                        }
                        return data;
                    });
                })
                .then(data => {
                    console.log('Response data:', data);
                    if (data.success) {
                        toastr.success('تم إتمام البيع بنجاح');
                        clearCart();
                        // Optionally redirect to sale details
                        if (data.sale_id) {
                            window.location.href = `{{ route('seller.sales.index') }}`;
                        }
                    } else {
                        console.error('Sale failed:', data);
                        toastr.error(data.message || 'حدث خطأ أثناء إتمام البيع');

                        // Show validation errors if available
                        if (data.errors) {
                            Object.values(data.errors).forEach(errorArray => {
                                errorArray.forEach(error => {
                                    toastr.error(error);
                                });
                            });
                        }
                    }
                })
                .catch(error => {
                    console.error('Detailed Error:', error);
                    console.error('Error message:', error.message);
                    console.error('Error data:', error.data);

                    if (error.data) {
                        // Handle validation errors
                        if (error.data.errors) {
                            console.error('Validation errors:', error.data.errors);
                            Object.values(error.data.errors).forEach(errorArray => {
                                errorArray.forEach(errorMsg => {
                                    toastr.error(errorMsg);
                                });
                            });
                        } else if (error.data.message) {
                            toastr.error(error.data.message);
                        } else {
                            toastr.error('خطأ تفصيلي: ' + error.message);
                        }
                    } else {
                        toastr.error('خطأ تفصيلي: ' + error.message);
                    }
                });
        }

        // Payment method change handler
        document.getElementById('payment_method').addEventListener('change', function() {
            const amountPaidDiv = document.getElementById('amountPaidDiv');
            if (this.value === 'credit') {
                amountPaidDiv.style.display = 'block';
                document.getElementById('amount_paid').value = 0;
            } else {
                amountPaidDiv.style.display = 'none';
                document.getElementById('amount_paid').value = cartTotal;
            }
        });

        // Product search functionality
        document.getElementById('productSearch').addEventListener('input', filterProducts);
        document.getElementById('categoryFilter').addEventListener('change', filterProducts);

        function filterProducts() {
            const searchTerm = document.getElementById('productSearch').value.toLowerCase();
            const categoryFilter = document.getElementById('categoryFilter').value;
            const products = document.querySelectorAll('.product-item');

            products.forEach(product => {
                const name = product.dataset.name;
                const barcode = product.dataset.barcode;
                const category = product.dataset.category;

                const matchesSearch = name.includes(searchTerm) || barcode.includes(searchTerm);
                const matchesCategory = !categoryFilter || category === categoryFilter;

                if (matchesSearch && matchesCategory) {
                    product.style.display = 'block';
                } else {
                    product.style.display = 'none';
                }
            });
        }
    </script>
</x-app-layout>
