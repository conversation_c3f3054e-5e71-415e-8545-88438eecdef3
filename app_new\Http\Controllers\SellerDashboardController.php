<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Sale;
use App\Models\Product;
use App\Models\Customer;
use App\Models\Category;
use App\Services\FinancialService;
use App\Services\ProductAvailabilityService;
use App\Services\DirectTransferService;
use App\Models\SaleItem;
use App\Models\BranchInventory;
// Removed unused imports for transfer requests
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class SellerDashboardController extends Controller
{
    protected $financialService;
    protected $productAvailabilityService;
    protected $directTransferService;

    public function __construct(
        FinancialService $financialService,
        ProductAvailabilityService $productAvailabilityService,
        DirectTransferService $directTransferService
    ) {
        $this->financialService = $financialService;
        $this->productAvailabilityService = $productAvailabilityService;
        $this->directTransferService = $directTransferService;
    }

    /**
     * Display the seller dashboard
     */
    public function index(Request $request)
    {
        $user = $request->user();

        // Calculate returns for today and month (seller's branch only)
        $todayReturns = \App\Models\SaleReturn::whereDate('created_at', today())
            ->where('status', 'completed')
            ->where('branch_id', $user->branch_id)
            ->sum('total_amount');

        $monthReturns = \App\Models\SaleReturn::whereMonth('created_at', now()->month)
            ->whereYear('created_at', now()->year)
            ->where('status', 'completed')
            ->where('branch_id', $user->branch_id)
            ->sum('total_amount');

        // Seller can only see data from their branch
        $stats = [
            'today_sales' => Sale::accessibleBy($user)
                ->whereDate('created_at', today())
                ->sum('total_amount') - $todayReturns,
            'today_sales_count' => Sale::accessibleBy($user)
                ->whereDate('created_at', today())
                ->count(),
            'month_sales' => Sale::accessibleBy($user)
                ->whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->sum('total_amount') - $monthReturns,
            'low_stock_products' => Product::whereHas('branchInventories', function ($q) use ($user) {
                $q->where('branch_id', $user->branch_id)
                    ->where('quantity', '<=', 10);
            })->count(),
        ];

        // Recent sales for this seller's branch
        $recentSales = Sale::with(['customer', 'items.product'])
            ->accessibleBy($user)
            ->latest()
            ->limit(10)
            ->get();

        // Low stock products in seller's accessible locations
        $lowStockProducts = Product::with(['branchInventories', 'storeInventories'])
            ->where(function ($query) use ($user) {
                // Include products from user's branch
                if ($user->branch_id) {
                    $query->whereHas('branchInventories', function ($q) use ($user) {
                        $q->where('branch_id', $user->branch_id)
                            ->where('quantity', '<=', 10);
                    });
                }

                // Include products from accessible stores
                $accessibleStoreIds = $user->getAccessibleStoreIds();
                if (!empty($accessibleStoreIds)) {
                    $query->orWhereHas('storeInventories', function ($q) use ($accessibleStoreIds) {
                        $q->whereIn('store_id', $accessibleStoreIds)
                            ->where('quantity', '<=', 10);
                    });
                }
            })
            ->limit(10)
            ->get();

        // Customer financial summary for this branch
        $customerSummary = $this->financialService->getCustomerFinancialSummary($user);

        return view('seller.dashboard', compact(
            'stats',
            'recentSales',
            'lowStockProducts',
            'customerSummary'
        ));
    }

    /**
     * Quick sale page for sellers
     */
    public function quickSale(Request $request)
    {
        $user = $request->user();

        // Get filters from request
        $filters = [
            'search' => $request->get('search'),
            'category_id' => $request->get('category_id'),
            'availability' => $request->get('availability'),
        ];

        // Get products with cross-location availability
        $products = $this->productAvailabilityService->getProductsWithAvailability($user, $filters);

        $customers = Customer::all();
        $categories = Category::all();

        return view('seller.quick-sale', compact('products', 'customers', 'categories'));
    }

    /**
     * Process quick sale submission
     */
    public function processQuickSale(Request $request)
    {
        try {
            $user = $request->user();

            // Debug: Log the incoming request data
            Log::info('Quick sale request data:', [
                'all_data' => $request->all(),
                'products' => $request->get('products'),
                'user_id' => $user->id
            ]);

            // Validate the request
            $validated = $request->validate([
                'branch_id' => 'required|exists:branches,id',
                'payment_method' => 'required|in:cash,card,credit',
                'paid_amount' => 'required|numeric|min:0',
                'notes' => 'nullable|string',
                'products' => 'required|array|min:1',
                'products.*.product_id' => 'required|exists:products,id',
                'products.*.quantity' => 'required|numeric|min:1',
                'products.*.price' => 'required|numeric|min:0',
            ]);

            // Check branch access
            if (!$user->canAccessBranch($validated['branch_id'])) {
                return response()->json([
                    'success' => false,
                    'message' => 'ليس لديك صلاحية للوصول إلى هذا الفرع'
                ], 403);
            }

            DB::beginTransaction();

            // Calculate total
            $total = 0;
            foreach ($validated['products'] as $productData) {
                $total += $productData['quantity'] * $productData['price'];
            }

            // Create the sale with only the basic required fields from the original migration
            $sale = Sale::create([
                'customer_id' => null, // Quick sales are anonymous
                'user_id' => $user->id, // Add the user who created the sale
                'branch_id' => $validated['branch_id'],
                'total_amount' => $total,
                'paid_amount' => $validated['paid_amount'],
                'discount_amount' => 0,
                'status' => 'completed',
            ]);

            // Create sale items and update inventory
            foreach ($validated['products'] as $productData) {
                $product = Product::findOrFail($productData['product_id']);

                // Check inventory availability
                $branchInventory = BranchInventory::where('branch_id', $validated['branch_id'])
                    ->where('product_id', $product->id)
                    ->first();

                if (!$branchInventory || $branchInventory->quantity < $productData['quantity']) {
                    throw new \Exception("المنتج {$product->name} غير متوفر بالكمية المطلوبة");
                }

                // Create sale item
                $itemSubtotal = $productData['quantity'] * $productData['price'];
                SaleItem::create([
                    'sale_id' => $sale->id,
                    'product_id' => $product->id,
                    'quantity' => $productData['quantity'],
                    'price' => $productData['price'],
                    'subtotal' => $itemSubtotal,
                ]);

                // Update inventory
                $branchInventory->decrement('quantity', $productData['quantity']);
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'تم إتمام البيع بنجاح',
                'sale_id' => $sale->id,
                'invoice_number' => $sale->invoice_number
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            Log::error('Quick sale validation error:', [
                'errors' => $e->errors(),
                'message' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'خطأ في البيانات المرسلة',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Quick sale error:', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Inventory view for seller's accessible locations
     */
    public function inventory(Request $request)
    {
        $user = $request->user();

        // Get filters from request
        $filters = [
            'search' => $request->get('search'),
            'category_id' => $request->get('category_id'),
            'stock_status' => $request->get('stock_status'), // Keep original parameter name
        ];

        // Get products with cross-location availability
        $allProducts = $this->productAvailabilityService->getProductsWithAvailability($user, $filters);

        // Paginate the results manually since we're using a collection
        $currentPage = $request->get('page', 1);
        $perPage = 20;
        $products = $allProducts->forPage($currentPage, $perPage);

        // Create a paginator instance
        $products = new \Illuminate\Pagination\LengthAwarePaginator(
            $products,
            $allProducts->count(),
            $perPage,
            $currentPage,
            ['path' => $request->url(), 'query' => $request->query()]
        );

        // Get categories for filter dropdown
        $categories = Category::all();

        // Calculate inventory summary from the enhanced products
        $inventorySummary = [
            'total_products' => $allProducts->count(),
            'available_products' => $allProducts->where('local_available', true)->count(),
            'low_stock_products' => $allProducts->filter(function ($product) {
                return $product->local_quantity > 0 && $product->local_quantity <= 10;
            })->count(),
            'out_of_stock_products' => $allProducts->where('availability_status', 'unavailable')->count(),
            'transferable_products' => $allProducts->where('availability_status', 'transferable')->count(),
        ];

        return view('seller.inventory', compact('products', 'categories', 'inventorySummary'));
    }



    // Transfer request methods removed - keeping only direct transfers

    /**
     * Execute a direct transfer to another branch
     */
    public function executeDirectTransfer(Request $request)
    {
        $user = $request->user();

        $validated = $request->validate([
            'product_id' => 'required|exists:products,id',
            'destination_branch_id' => 'required|exists:branches,id',
            'quantity' => 'required|integer|min:1',
            'notes' => 'nullable|string|max:255',
        ]);

        try {
            $result = $this->directTransferService->executeDirectTransfer(
                $user,
                $validated['product_id'],
                $validated['destination_branch_id'],
                $validated['quantity'],
                $validated['notes']
            );

            return response()->json($result);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Execute auto transfer from best available location to seller's branch
     */
    public function executeAutoTransfer(Request $request)
    {
        $user = $request->user();

        $validated = $request->validate([
            'product_id' => 'required|exists:products,id',
            'quantity' => 'required|integer|min:1',
        ]);

        try {
            $result = $this->directTransferService->executeAutoTransfer(
                $user,
                $validated['product_id'],
                $validated['quantity']
            );

            return response()->json($result);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Get available branches for direct transfer
     */
    public function getAvailableBranches(Request $request)
    {
        $user = $request->user();
        $branches = $this->directTransferService->getAvailableBranches($user);

        return response()->json([
            'success' => true,
            'branches' => $branches->map(function ($branch) {
                return [
                    'id' => $branch->id,
                    'name' => $branch->name,
                    'code' => $branch->code,
                ];
            })
        ]);
    }



    /**
     * Confirm receipt of a direct transfer
     */
    public function confirmTransferReceipt(Request $request, $transferId)
    {
        $user = $request->user();

        $validated = $request->validate([
            'received_quantities' => 'nullable|array',
            'received_quantities.*' => 'integer|min:0',
        ]);

        try {
            $result = $this->directTransferService->confirmTransferReceipt(
                $transferId,
                $user,
                $validated['received_quantities'] ?? []
            );

            return response()->json($result);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }
}
