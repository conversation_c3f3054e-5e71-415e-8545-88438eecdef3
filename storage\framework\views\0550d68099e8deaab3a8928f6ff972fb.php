<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h2 class="h3 mb-0">لوحة تحكم البائع</h2>
                    <div class="text-muted">
                        <i class="fas fa-store"></i> <?php echo e(auth()->user()->branch->name ?? 'غير محدد'); ?>

                        <span class="mx-2">|</span>
                        <i class="fas fa-calendar-alt"></i> <?php echo e(now()->format('Y-m-d')); ?>

                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card bg-gradient-primary text-white">
                    <div class="card-body">
                        <h5 class="card-title mb-3">
                            <i class="fas fa-bolt"></i> إجراءات سريعة
                        </h5>
                        <div class="row">
                            <div class="col mb-2">
                                <a href="<?php echo e(route('seller.sales.create')); ?>" class="btn btn-light btn-lg w-100">
                                    <i class="fas fa-plus text-primary"></i>
                                    <div class="mt-1">عملية بيع جديدة</div>
                                </a>
                            </div>
                            <div class="col mb-2">
                                <a href="<?php echo e(route('seller.quick-sale')); ?>" class="btn btn-light btn-lg w-100">
                                    <i class="fas fa-shopping-cart text-success"></i>
                                    <div class="mt-1">بيع سريع</div>
                                </a>
                            </div>
                            <div class="col mb-2">
                                <a href="<?php echo e(route('seller.inventory')); ?>" class="btn btn-light btn-lg w-100">
                                    <i class="fas fa-boxes text-info"></i>
                                    <div class="mt-1">المخزون</div>
                                </a>
                            </div>
                            <div class="col mb-2">
                                <a href="<?php echo e(route('seller.customers.index')); ?>" class="btn btn-light btn-lg w-100">
                                    <i class="fas fa-users text-warning"></i>
                                    <div class="mt-1">العملاء</div>
                                </a>
                            </div>

                            <div class="col mb-2">
                                <a href="<?php echo e(route('seller.transfers.direct.create')); ?>"
                                    class="btn btn-light btn-lg w-100">
                                    <i class="fas fa-exchange-alt text-primary"></i>
                                    <div class="mt-1">نقل مباشر</div>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>






        <!-- Quick Direct Transfer Section -->
        

        <!-- Statistics Cards -->
        

        <!-- Recent Sales and Low Stock -->
        <div class="row">
            <div class="col-lg-8 mb-3">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-shopping-cart"></i> أحدث المبيعات
                        </h5>
                        <a href="<?php echo e(route('seller.sales.index')); ?>" class="btn btn-sm btn-outline-primary">عرض
                            الكل</a>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>رقم الفاتورة</th>
                                        <th>العميل</th>
                                        <th>عدد المنتجات</th>
                                        <th>المبلغ</th>
                                        <th>التاريخ</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__empty_1 = true; $__currentLoopData = $recentSales; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sale): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                        <tr>
                                            <td>
                                                <span class="badge bg-primary">#<?php echo e($sale->id); ?></span>
                                            </td>
                                            <td><?php echo e($sale->customer->name ?? 'عميل نقدي'); ?></td>
                                            <td>
                                                <span class="badge bg-secondary"><?php echo e($sale->items->count()); ?></span>
                                            </td>
                                            <td class="fw-bold"><?php echo e(format_currency($sale->total_amount)); ?></td>
                                            <td><?php echo e($sale->created_at->format('Y-m-d H:i')); ?></td>
                                            <td>
                                                <a href="<?php echo e(route('seller.sales.show', $sale)); ?>"
                                                    class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <?php if($sale->status === 'pending'): ?>
                                                    <a href="<?php echo e(route('seller.sales.edit', $sale)); ?>"
                                                        class="btn btn-sm btn-outline-warning">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                        <tr>
                                            <td colspan="6" class="text-center text-muted py-4">
                                                <i class="fas fa-shopping-cart fa-2x mb-2"></i>
                                                <div>لا توجد مبيعات حديثة</div>
                                                <a href="<?php echo e(route('seller.sales.create')); ?>"
                                                    class="btn btn-primary btn-sm mt-2">
                                                    إنشاء عملية بيع جديدة
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4 mb-3">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-exclamation-triangle text-warning"></i> منتجات قليلة المخزون
                        </h5>
                        <a href="<?php echo e(route('seller.inventory')); ?>" class="btn btn-sm btn-outline-warning">عرض الكل</a>
                    </div>
                    <div class="card-body p-0">
                        <div class="list-group list-group-flush">
                            <?php $__empty_1 = true; $__currentLoopData = $lowStockProducts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-1"><?php echo e($product->name); ?></h6>
                                        <small class="text-muted"><?php echo e($product->category->name ?? 'بدون فئة'); ?></small>
                                    </div>
                                    <div class="text-end">
                                        <span class="badge bg-warning">
                                            <?php echo e($product->branchInventories->first()->quantity ?? 0); ?>

                                        </span>
                                        <div class="small text-muted">متبقي</div>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <div class="list-group-item text-center text-muted py-4">
                                    <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                                    <div>جميع المنتجات متوفرة بكمية كافية</div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Customer Financial Summary -->
        <?php if(isset($customerSummary['customers']) && count($customerSummary['customers']) > 0): ?>
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-users"></i> العملاء المدينون
                            </h5>
                            <a href="<?php echo e(route('seller.customers.index')); ?>" class="btn btn-sm btn-outline-primary">عرض
                                الكل</a>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th>اسم العميل</th>
                                            <th>رقم الهاتف</th>
                                            <th>إجمالي المشتريات</th>
                                            <th>المبلغ المدفوع</th>
                                            <th>المبلغ المستحق</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $__currentLoopData = array_slice($customerSummary['customers'], 0, 5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $customer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <tr>
                                                <td><?php echo e($customer['name']); ?></td>
                                                <td><?php echo e($customer['phone'] ?? '-'); ?></td>
                                                <td><?php echo e(format_currency($customer['total_sales'])); ?></td>
                                                <td class="text-success">
                                                    <?php echo e(format_currency($customer['total_paid'])); ?></td>
                                                <td class="text-warning fw-bold">
                                                    <?php echo e(format_currency($customer['amount_owed'])); ?></td>
                                                <td>
                                                    <a href="<?php echo e(route('seller.customers.show', $customer['id'])); ?>"
                                                        class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-eye"></i> عرض
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <!-- JavaScript for Quick Transfer -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            loadAvailableProducts();
            loadAvailableBranches();
        });

        // Load products available in current branch
        function loadAvailableProducts() {
            fetch('<?php echo e(route('seller.inventory')); ?>?ajax=1')
                .then(response => response.json())
                .then(data => {
                    const select = document.getElementById('quickTransferProduct');
                    select.innerHTML = '<option value="">ابحث عن منتج...</option>';

                    if (data.products) {
                        data.products.forEach(product => {
                            if (product.local_quantity > 0) {
                                const option = document.createElement('option');
                                option.value = product.id;
                                option.textContent = `${product.name} (متوفر: ${product.local_quantity})`;
                                option.dataset.maxQuantity = product.local_quantity;
                                select.appendChild(option);
                            }
                        });
                    }
                })
                .catch(error => {
                    console.error('Error loading products:', error);
                    // Fallback: Add some sample options
                    const select = document.getElementById('quickTransferProduct');
                    select.innerHTML = '<option value="">لا توجد منتجات متوفرة</option>';
                });
        }

        // Load available branches for transfer
        function loadAvailableBranches() {
            fetch('<?php echo e(route('seller.transfer.branches')); ?>')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const select = document.getElementById('quickTransferBranch');
                        select.innerHTML = '<option value="">اختر الفرع...</option>';

                        data.branches.forEach(branch => {
                            const option = document.createElement('option');
                            option.value = branch.id;
                            option.textContent = `${branch.name} ${branch.code ? '(' + branch.code + ')' : ''}`;
                            select.appendChild(option);
                        });
                    }
                })
                .catch(error => {
                    console.error('Error loading branches:', error);
                });
        }

        // Update max quantity when product is selected
        document.getElementById('quickTransferProduct').addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            const maxQuantity = selectedOption.dataset.maxQuantity || 1;
            const quantityInput = document.getElementById('quickTransferQuantity');
            quantityInput.max = maxQuantity;
            quantityInput.value = Math.min(quantityInput.value, maxQuantity);
        });

        // Execute quick direct transfer
        function executeQuickTransfer() {
            const productId = document.getElementById('quickTransferProduct').value;
            const branchId = document.getElementById('quickTransferBranch').value;
            const quantity = parseInt(document.getElementById('quickTransferQuantity').value);

            if (!productId) {
                toastr.error('يرجى اختيار المنتج');
                return;
            }

            if (!branchId) {
                toastr.error('يرجى اختيار الفرع المستهدف');
                return;
            }

            if (!quantity || quantity <= 0) {
                toastr.error('يرجى إدخال كمية صحيحة');
                return;
            }

            // Show confirmation dialog
            if (!confirm(`هل أنت متأكد من تنفيذ النقل المباشر؟\nسيتم نقل ${quantity} وحدة إلى الفرع المحدد فوراً.`)) {
                return;
            }

            // Disable button during processing
            const button = document.querySelector('button[onclick="executeQuickTransfer()"]');
            const originalText = button.innerHTML;
            button.disabled = true;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التنفيذ...';

            // Send direct transfer request
            fetch('<?php echo e(route('seller.transfer.direct')); ?>', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                    },
                    body: JSON.stringify({
                        product_id: productId,
                        destination_branch_id: branchId,
                        quantity: quantity,
                        notes: 'نقل مباشر سريع من لوحة التحكم'
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        toastr.success('تم تنفيذ النقل المباشر بنجاح! ' + data.message);
                        // Reset form
                        document.getElementById('quickTransferProduct').value = '';
                        document.getElementById('quickTransferBranch').value = '';
                        document.getElementById('quickTransferQuantity').value = 1;
                        // Reload products to update quantities
                        loadAvailableProducts();
                    } else {
                        toastr.error(data.message || 'حدث خطأ أثناء تنفيذ النقل المباشر');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    toastr.error('حدث خطأ أثناء تنفيذ النقل المباشر');
                })
                .finally(() => {
                    // Re-enable button
                    button.disabled = false;
                    button.innerHTML = originalText;
                });
        }
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH D:\pos-app\resources\views/seller/dashboard.blade.php ENDPATH**/ ?>