<x-app-layout>
    <div class="container-fluid px-4">
        <!-- <PERSON> Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-0 text-gray-800">
                    <i class="fas fa-user-plus text-primary me-2"></i>
                    إضافة مستخدم جديد
                </h1>
                <p class="text-muted mb-0">إضافة مستخدم جديد إلى النظام</p>
            </div>
            <a href="{{ route('admin.settings.users') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-2"></i>
                العودة إلى المستخدمين
            </a>
        </div>

        <!-- Progress Steps -->
        <div class="card shadow-sm mb-4">
            <div class="card-body py-3">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <div class="step-indicator active">
                            <i class="fas fa-user-plus"></i>
                        </div>
                    </div>
                    <div class="col">
                        <h6 class="mb-0 text-primary">الخطوة 1 من 1</h6>
                        <small class="text-muted">إدخال بيانات المستخدم الجديد</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Form -->
        <form action="{{ route('admin.settings.users.store') }}" method="POST" class="rtl" id="userForm">
            @csrf

            <div class="row">
                <!-- Basic Information Card -->
                <div class="col-lg-6 mb-4">
                    <div class="card shadow h-100">
                        <div class="card-header bg-primary text-white">
                            <h6 class="m-0 font-weight-bold">
                                <i class="fas fa-user me-2"></i>
                                المعلومات الأساسية
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-4">
                                <label for="name" class="form-label fw-bold">
                                    <i class="fas fa-signature text-primary me-1"></i>
                                    اسم المستخدم
                                    <span class="text-danger">*</span>
                                </label>
                                <input type="text"
                                    class="form-control form-control-lg @error('name') is-invalid @enderror"
                                    id="name" name="name" value="{{ old('name') }}"
                                    placeholder="أدخل اسم المستخدم الكامل" required>
                                @error('name')
                                    <div class="invalid-feedback">
                                        <i class="fas fa-exclamation-circle me-1"></i>
                                        {{ $message }}
                                    </div>
                                @enderror
                                <div class="form-text">اسم المستخدم كما سيظهر في النظام</div>
                            </div>

                            <div class="mb-4">
                                <label for="email" class="form-label fw-bold">
                                    <i class="fas fa-envelope text-info me-1"></i>
                                    البريد الإلكتروني
                                    <span class="text-danger">*</span>
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-at"></i>
                                    </span>
                                    <input type="email" class="form-control @error('email') is-invalid @enderror"
                                        id="email" name="email" value="{{ old('email') }}"
                                        placeholder="<EMAIL>" required>
                                </div>
                                @error('email')
                                    <div class="invalid-feedback">
                                        <i class="fas fa-exclamation-circle me-1"></i>
                                        {{ $message }}
                                    </div>
                                @enderror
                                <div class="form-text">البريد الإلكتروني لتسجيل الدخول</div>
                            </div>

                            <div class="mb-4">
                                <label for="phone" class="form-label fw-bold">
                                    <i class="fas fa-phone text-success me-1"></i>
                                    رقم الهاتف
                                    <span class="text-danger">*</span>
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-phone-alt"></i>
                                    </span>
                                    <input type="tel" class="form-control @error('phone') is-invalid @enderror"
                                        id="phone" name="phone" value="{{ old('phone') }}"
                                        placeholder="05xxxxxxxx" required>
                                </div>
                                @error('phone')
                                    <div class="invalid-feedback">
                                        <i class="fas fa-exclamation-circle me-1"></i>
                                        {{ $message }}
                                    </div>
                                @enderror
                                <div class="form-text">رقم الهاتف للتواصل مع المستخدم</div>
                            </div>

                            <div class="mb-4">
                                <label for="password" class="form-label fw-bold">
                                    <i class="fas fa-lock text-warning me-1"></i>
                                    كلمة المرور
                                    <span class="text-danger">*</span>
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-key"></i>
                                    </span>
                                    <input type="password" class="form-control @error('password') is-invalid @enderror"
                                        id="password" name="password" placeholder="أدخل كلمة مرور قوية" required>
                                    <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                @error('password')
                                    <div class="invalid-feedback">
                                        <i class="fas fa-exclamation-circle me-1"></i>
                                        {{ $message }}
                                    </div>
                                @enderror
                                <div class="form-text">كلمة المرور يجب أن تكون 8 أحرف على الأقل</div>
                            </div>

                            <div class="mb-4">
                                <label for="password_confirmation" class="form-label fw-bold">
                                    <i class="fas fa-lock text-warning me-1"></i>
                                    تأكيد كلمة المرور
                                    <span class="text-danger">*</span>
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-key"></i>
                                    </span>
                                    <input type="password"
                                        class="form-control @error('password_confirmation') is-invalid @enderror"
                                        id="password_confirmation" name="password_confirmation"
                                        placeholder="أعد إدخال كلمة المرور" required>
                                    <button class="btn btn-outline-secondary" type="button"
                                        id="togglePasswordConfirm">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                @error('password_confirmation')
                                    <div class="invalid-feedback">
                                        <i class="fas fa-exclamation-circle me-1"></i>
                                        {{ $message }}
                                    </div>
                                @enderror
                                <div class="form-text">يجب أن تطابق كلمة المرور</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Role and Branch Information Card -->
                <div class="col-lg-6 mb-4">
                    <div class="card shadow h-100">
                        <div class="card-header bg-success text-white">
                            <h6 class="m-0 font-weight-bold">
                                <i class="fas fa-user-tag me-2"></i>
                                الصلاحيات والفرع
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-4">
                                <label for="role_id" class="form-label fw-bold">
                                    <i class="fas fa-user-shield text-primary me-1"></i>
                                    الدور
                                    <span class="text-danger">*</span>
                                </label>
                                <select class="form-select form-select-lg @error('role_id') is-invalid @enderror"
                                    id="role_id" name="role_id" required>
                                    <option value="">اختر الدور</option>
                                    @foreach ($roles as $role)
                                        <option value="{{ $role->id }}"
                                            {{ old('role_id') == $role->id ? 'selected' : '' }}>
                                            {{ $role->name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('role_id')
                                    <div class="invalid-feedback">
                                        <i class="fas fa-exclamation-circle me-1"></i>
                                        {{ $message }}
                                    </div>
                                @enderror
                                <div class="form-text">حدد دور المستخدم في النظام</div>
                            </div>

                            <div class="mb-4">
                                <label for="branch_id" class="form-label fw-bold">
                                    <i class="fas fa-building text-info me-1"></i>
                                    الفرع
                                    <span class="text-danger">*</span>
                                </label>
                                <select class="form-select form-select-lg @error('branch_id') is-invalid @enderror"
                                    id="branch_id" name="branch_id" required>
                                    <option value="">اختر الفرع</option>
                                    @foreach ($branches as $branch)
                                        <option value="{{ $branch->id }}"
                                            {{ old('branch_id', request('branch_id')) == $branch->id ? 'selected' : '' }}>
                                            {{ $branch->name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('branch_id')
                                    <div class="invalid-feedback">
                                        <i class="fas fa-exclamation-circle me-1"></i>
                                        {{ $message }}
                                    </div>
                                @enderror
                                <div class="form-text">حدد الفرع الذي سيعمل به المستخدم</div>
                            </div>

                            <div class="mb-4">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active"
                                        value="1" {{ old('is_active', true) ? 'checked' : '' }}>
                                    <label class="form-check-label fw-bold" for="is_active">
                                        <i class="fas fa-toggle-on text-success me-1"></i>
                                        المستخدم نشط
                                    </label>
                                </div>
                                <div class="form-text">إذا كان المستخدم نشطاً يمكنه تسجيل الدخول</div>
                            </div>

                            <!-- Role Description -->
                            <div class="alert alert-info" id="roleDescription" style="display: none;">
                                <i class="fas fa-info-circle me-2"></i>
                                <span id="roleDescriptionText"></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="row">
                <div class="col-12">
                    <div class="card shadow">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="text-muted">
                                    <i class="fas fa-info-circle me-1"></i>
                                    تأكد من صحة جميع البيانات قبل الحفظ
                                </div>
                                <div class="d-flex gap-2">
                                    <a href="{{ route('admin.settings.users') }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-times me-2"></i>
                                        إلغاء
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>
                                        حفظ المستخدم
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>

    @push('styles')
        <style>
            .step-indicator {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                background: #007bff;
                color: white;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 16px;
            }

            .step-indicator.active {
                background: #28a745;
                box-shadow: 0 0 0 4px rgba(40, 167, 69, 0.25);
            }

            .form-control:focus {
                border-color: #007bff;
                box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
            }

            .form-select:focus {
                border-color: #007bff;
                box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
            }
        </style>
    @endpush

    @push('scripts')
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                // Toggle password visibility
                const togglePassword = document.getElementById('togglePassword');
                const password = document.getElementById('password');

                if (togglePassword && password) {
                    togglePassword.addEventListener('click', function() {
                        const type = password.getAttribute('type') === 'password' ? 'text' : 'password';
                        password.setAttribute('type', type);
                        this.querySelector('i').classList.toggle('fa-eye');
                        this.querySelector('i').classList.toggle('fa-eye-slash');
                    });
                }

                // Toggle password confirmation visibility
                const togglePasswordConfirm = document.getElementById('togglePasswordConfirm');
                const passwordConfirm = document.getElementById('password_confirmation');

                if (togglePasswordConfirm && passwordConfirm) {
                    togglePasswordConfirm.addEventListener('click', function() {
                        const type = passwordConfirm.getAttribute('type') === 'password' ? 'text' : 'password';
                        passwordConfirm.setAttribute('type', type);
                        this.querySelector('i').classList.toggle('fa-eye');
                        this.querySelector('i').classList.toggle('fa-eye-slash');
                    });
                }

                // Role descriptions
                const roleDescriptions = {
                    'admin': 'المدير له صلاحية كاملة على جميع أجزاء النظام',
                    'manager': 'المدير له صلاحية إدارة الفرع والمبيعات والمخزون',
                    'seller': 'البائع له صلاحية إجراء المبيعات وإدارة العملاء',
                    'warehouse_staff': 'موظف المخزن له صلاحية إدارة المخزون والنقل'
                };

                // Show role description
                const roleSelect = document.getElementById('role_id');
                const roleDescription = document.getElementById('roleDescription');
                const roleDescriptionText = document.getElementById('roleDescriptionText');

                if (roleSelect && roleDescription && roleDescriptionText) {
                    roleSelect.addEventListener('change', function() {
                        const selectedOption = this.options[this.selectedIndex];
                        const roleName = selectedOption.text.toLowerCase();

                        let description = '';
                        for (const [key, value] of Object.entries(roleDescriptions)) {
                            if (roleName.includes(key) || roleName.includes('مدير') || roleName.includes(
                                'بائع') || roleName.includes('مخزن')) {
                                if (roleName.includes('مدير') && key === 'admin') description = value;
                                else if (roleName.includes('مدير') && key === 'manager') description = value;
                                else if (roleName.includes('بائع') && key === 'seller') description = value;
                                else if (roleName.includes('مخزن') && key === 'warehouse_staff') description =
                                    value;
                                break;
                            }
                        }

                        if (description) {
                            roleDescriptionText.textContent = description;
                            roleDescription.style.display = 'block';
                        } else {
                            roleDescription.style.display = 'none';
                        }
                    });
                }

                // Form validation
                const form = document.getElementById('userForm');
                if (form) {
                    form.addEventListener('submit', function(e) {
                        const password = document.getElementById('password').value;
                        const passwordConfirm = document.getElementById('password_confirmation').value;

                        if (password !== passwordConfirm) {
                            e.preventDefault();
                            alert('كلمة المرور وتأكيد كلمة المرور غير متطابقتين');
                            return false;
                        }

                        if (password.length < 8) {
                            e.preventDefault();
                            alert('كلمة المرور يجب أن تكون 8 أحرف على الأقل');
                            return false;
                        }
                    });
                }
            });
        </script>
    @endpush
</x-app-layout>
