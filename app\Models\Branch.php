<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\MorphOne;

class Branch extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'code',
        'address',
        'phone',
        'email',
        'opening_balance',
        'is_active',
    ];

    protected $casts = [
        'opening_balance' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    public function users()
    {
        return $this->hasMany(User::class);
    }

    public function stores()
    {
        return $this->hasMany(Store::class);
    }

    public function inventory()
    {
        return $this->belongsToMany(Product::class, 'branch_inventory')
            ->withPivot('quantity', 'minimum_stock')
            ->withTimestamps();
    }

    public function branchInventories()
    {
        return $this->hasMany(BranchInventory::class);
    }

    public function outgoingTransfers()
    {
        return $this->hasMany(InventoryTransfer::class, 'source_id')
            ->where('source_type', 'branch');
    }

    public function incomingTransfers()
    {
        return $this->hasMany(InventoryTransfer::class, 'destination_id')
            ->where('destination_type', 'branch');
    }

    public function sales()
    {
        return $this->hasMany(Sale::class);
    }

    public function saleReturns()
    {
        return $this->hasMany(SaleReturn::class);
    }

    public function purchases()
    {
        return $this->hasMany(Purchase::class);
    }

    public function expenses()
    {
        return $this->hasMany(Expense::class);
    }

    public function cashTransactions()
    {
        return $this->hasMany(CashTransaction::class);
    }

    public function account(): MorphOne
    {
        return $this->morphOne(Account::class, 'accountable');
    }

    // Computed Properties for Analytics
    public function getTotalSalesAttribute()
    {
        $totalSales = $this->sales->sum('total_amount');
        $totalReturns = $this->saleReturns->where('status', 'completed')->sum('total_amount');
        return $totalSales - $totalReturns;
    }

    public function getTotalPurchasesAttribute()
    {
        return $this->purchases->sum('total_amount');
    }

    public function getTotalExpensesAttribute()
    {
        return $this->expenses->sum('amount');
    }

    public function getNetProfitAttribute()
    {
        return $this->total_sales - $this->total_purchases - $this->total_expenses;
    }

    public function getTodaySalesAttribute()
    {
        $todaySales = $this->sales()
            ->whereDate('created_at', today())
            ->sum('total_amount');

        $todayReturns = $this->saleReturns()
            ->whereDate('created_at', today())
            ->where('status', 'completed')
            ->sum('total_amount');

        return $todaySales - $todayReturns;
    }

    public function getTodayPurchasesAttribute()
    {
        return $this->purchases()
            ->whereDate('created_at', today())
            ->sum('total_amount');
    }

    public function getMonthSalesAttribute()
    {
        $monthSales = $this->sales()
            ->whereMonth('created_at', now()->month)
            ->whereYear('created_at', now()->year)
            ->sum('total_amount');

        $monthReturns = $this->saleReturns()
            ->whereMonth('created_at', now()->month)
            ->whereYear('created_at', now()->year)
            ->where('status', 'completed')
            ->sum('total_amount');

        return $monthSales - $monthReturns;
    }

    public function getMonthPurchasesAttribute()
    {
        return $this->purchases()
            ->whereMonth('created_at', now()->month)
            ->whereYear('created_at', now()->year)
            ->sum('total_amount');
    }

    public function getActiveStoresCountAttribute()
    {
        return $this->stores()->where('is_active', true)->count();
    }

    public function getActiveUsersCountAttribute()
    {
        return $this->users()->where('is_active', true)->count();
    }

    public function getInventoryValueAttribute()
    {
        return $this->inventory->sum(function ($item) {
            return $item->pivot->quantity * ($item->cost_price ?? 0);
        });
    }

    // Performance Metrics
    public function getSalesGrowthAttribute()
    {
        $currentMonth = $this->getMonthSalesAttribute();
        $lastMonth = $this->sales()
            ->whereMonth('created_at', now()->subMonth()->month)
            ->whereYear('created_at', now()->subMonth()->year)
            ->sum('total_amount');

        if ($lastMonth == 0) {
            return $currentMonth > 0 ? 100 : 0;
        }

        return (($currentMonth - $lastMonth) / $lastMonth) * 100;
    }

    public function getAverageSaleAmountAttribute()
    {
        $salesCount = $this->sales->count();
        return $salesCount > 0 ? $this->total_sales / $salesCount : 0;
    }

    // Status Methods
    public function isActive()
    {
        return $this->is_active;
    }

    public function hasStores()
    {
        return $this->stores()->exists();
    }

    public function hasUsers()
    {
        return $this->users()->exists();
    }

    public function hasSales()
    {
        return $this->sales()->exists();
    }

    // Financial Summary
    public function getFinancialSummary()
    {
        return [
            'total_sales' => $this->total_sales,
            'total_purchases' => $this->total_purchases,
            'total_expenses' => $this->total_expenses,
            'net_profit' => $this->net_profit,
            'today_sales' => $this->today_sales,
            'today_purchases' => $this->today_purchases,
            'month_sales' => $this->month_sales,
            'month_purchases' => $this->month_purchases,
            'sales_growth' => $this->sales_growth,
            'average_sale_amount' => $this->average_sale_amount,
            'inventory_value' => $this->inventory_value,
        ];
    }

    // Scope for user access control
    public function scopeAccessibleBy($query, User $user)
    {
        if ($user->canAccessAllBranches()) {
            return $query;
        }

        return $query->where('id', $user->branch_id);
    }
}
