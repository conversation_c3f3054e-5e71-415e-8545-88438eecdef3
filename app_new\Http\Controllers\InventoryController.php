<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\Branch;
use App\Models\Store;
use App\Models\BranchInventory;
use App\Models\StoreInventory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class InventoryController extends Controller
{
    /**
     * Overview of all inventory across branches and stores
     */
    public function overview(Request $request)
    {
        // Get search and filter parameters
        $search = $request->get('search');
        $categoryId = $request->get('category_id');
        $stockStatus = $request->get('stock_status');

        // Base query for products with inventory
        $query = Product::with(['category', 'branchInventories.branch', 'storeInventories.store']);

        // Apply search filter
        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                    ->orWhere('sku', 'like', "%{$search}%");
            });
        }

        // Apply category filter
        if ($categoryId) {
            $query->where('category_id', $categoryId);
        }

        $products = $query->get()->map(function ($product) {
            // Calculate total quantities across all locations
            $branchQuantity = $product->branchInventories->sum('quantity');
            $storeQuantity = $product->storeInventories->sum('quantity');
            $totalQuantity = $branchQuantity + $storeQuantity;

            // Calculate total value
            $totalValue = $totalQuantity * ($product->price ?? 0);

            // Determine stock status
            $stockStatus = 'available';
            if ($totalQuantity <= 0) {
                $stockStatus = 'out_of_stock';
            } elseif ($totalQuantity <= ($product->minimum_stock ?? 10)) {
                $stockStatus = 'low_stock';
            }

            return (object) [
                'id' => $product->id,
                'name' => $product->name,
                'sku' => $product->sku,
                'category' => $product->category->name ?? 'غير محدد',
                'price' => $product->price ?? 0,
                'branch_quantity' => $branchQuantity,
                'store_quantity' => $storeQuantity,
                'total_quantity' => $totalQuantity,
                'total_value' => $totalValue,
                'stock_status' => $stockStatus,
                'locations' => [
                    'branches' => $product->branchInventories->map(function ($inv) {
                        return [
                            'name' => $inv->branch->name,
                            'quantity' => $inv->quantity,
                            'cost_price' => $inv->cost_price ?? 0,
                        ];
                    }),
                    'stores' => $product->storeInventories->map(function ($inv) {
                        return [
                            'name' => $inv->store->name,
                            'quantity' => $inv->quantity,
                        ];
                    }),
                ],
            ];
        });

        // Apply stock status filter
        if ($stockStatus) {
            $products = $products->filter(function ($product) use ($stockStatus) {
                return $product->stock_status === $stockStatus;
            });
        }

        // Get summary statistics
        $stats = [
            'total_products' => $products->count(),
            'total_value' => $products->sum('total_value'),
            'low_stock_count' => $products->where('stock_status', 'low_stock')->count(),
            'out_of_stock_count' => $products->where('stock_status', 'out_of_stock')->count(),
        ];

        // Get categories for filter
        $categories = \App\Models\Category::all();

        return view('admin.inventory.overview', compact('products', 'stats', 'categories'));
    }

    /**
     * Show inventory for all branches
     */
    public function branches(Request $request)
    {
        $branches = Branch::with(['branchInventories.product.category'])
            ->withCount('branchInventories')
            ->get()
            ->map(function ($branch) {
                $totalValue = $branch->branchInventories->sum(function ($inv) {
                    return $inv->quantity * ($inv->cost_price ?? 0);
                });

                $lowStockCount = $branch->branchInventories->filter(function ($inv) {
                    return $inv->quantity <= 10; // Low stock threshold
                })->count();

                return (object) [
                    'id' => $branch->id,
                    'name' => $branch->name,
                    'code' => $branch->code,
                    'total_products' => $branch->branch_inventories_count,
                    'total_value' => $totalValue,
                    'low_stock_count' => $lowStockCount,
                ];
            });

        return view('admin.inventory.branches', compact('branches'));
    }

    /**
     * Show inventory for all stores
     */
    public function stores(Request $request)
    {
        $stores = Store::with(['storeInventories.product.category'])
            ->withCount('storeInventories')
            ->get()
            ->map(function ($store) {
                $totalValue = $store->storeInventories->sum(function ($inv) {
                    return $inv->quantity * ($inv->product->price ?? 0);
                });

                $lowStockCount = $store->storeInventories->filter(function ($inv) {
                    return $inv->quantity <= $inv->minimum_stock;
                })->count();

                return (object) [
                    'id' => $store->id,
                    'name' => $store->name,
                    'code' => $store->code,
                    'branch_name' => $store->branch->name ?? 'مستقل',
                    'total_products' => $store->store_inventories_count,
                    'total_value' => $totalValue,
                    'low_stock_count' => $lowStockCount,
                ];
            });

        return view('admin.inventory.stores', compact('stores'));
    }

    /**
     * Show low stock items across all locations
     */
    public function lowStock(Request $request)
    {
        // Get low stock items from branches
        $branchLowStock = BranchInventory::with(['product.category', 'branch'])
            ->whereRaw('quantity <= 10') // Low stock threshold
            ->get()
            ->map(function ($inv) {
                return (object) [
                    'product_name' => $inv->product->name,
                    'sku' => $inv->product->sku,
                    'category' => $inv->product->category->name ?? 'غير محدد',
                    'location_type' => 'فرع',
                    'location_name' => $inv->branch->name,
                    'current_quantity' => $inv->quantity,
                    'threshold' => 10,
                    'cost_price' => $inv->cost_price ?? 0,
                ];
            });

        // Get low stock items from stores
        $storeLowStock = StoreInventory::with(['product.category', 'store'])
            ->whereRaw('quantity <= minimum_stock')
            ->get()
            ->map(function ($inv) {
                return (object) [
                    'product_name' => $inv->product->name,
                    'sku' => $inv->product->sku,
                    'category' => $inv->product->category->name ?? 'غير محدد',
                    'location_type' => 'مخزن',
                    'location_name' => $inv->store->name,
                    'current_quantity' => $inv->quantity,
                    'threshold' => $inv->minimum_stock,
                    'cost_price' => $inv->product->price ?? 0,
                ];
            });

        $lowStockItems = $branchLowStock->concat($storeLowStock);

        return view('admin.inventory.low-stock', compact('lowStockItems'));
    }

    /**
     * Show detailed inventory for a specific branch
     */
    public function branchDetails(Branch $branch, Request $request)
    {
        $inventory = $branch->branchInventories()
            ->with(['product.category'])
            ->when($request->search, function ($query, $search) {
                $query->whereHas('product', function ($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                        ->orWhere('sku', 'like', "%{$search}%");
                });
            })
            ->paginate(20);

        return view('admin.inventory.branch-details', compact('branch', 'inventory'));
    }

    /**
     * Show detailed inventory for a specific store
     */
    public function storeDetails(Store $store, Request $request)
    {
        $inventory = $store->storeInventories()
            ->with(['product.category'])
            ->when($request->search, function ($query, $search) {
                $query->whereHas('product', function ($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                        ->orWhere('sku', 'like', "%{$search}%");
                });
            })
            ->paginate(20);

        return view('admin.inventory.store-details', compact('store', 'inventory'));
    }
}
