<x-app-layout>
    <div class="container-fluid">
        <!-- <PERSON> Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h2 class="h3 mb-0">لوحة تحكم البائع</h2>
                    <div class="text-muted">
                        <i class="fas fa-store"></i> {{ auth()->user()->branch->name ?? 'غير محدد' }}
                        <span class="mx-2">|</span>
                        <i class="fas fa-calendar-alt"></i> {{ now()->format('Y-m-d') }}
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card bg-gradient-primary text-white">
                    <div class="card-body">
                        <h5 class="card-title mb-3">
                            <i class="fas fa-bolt"></i> إجراءات سريعة
                        </h5>
                        <div class="row">
                            <div class="col mb-2">
                                <a href="{{ route('seller.sales.create') }}" class="btn btn-light btn-lg w-100">
                                    <i class="fas fa-plus text-primary"></i>
                                    <div class="mt-1">عملية بيع جديدة</div>
                                </a>
                            </div>
                            <div class="col mb-2">
                                <a href="{{ route('seller.quick-sale') }}" class="btn btn-light btn-lg w-100">
                                    <i class="fas fa-shopping-cart text-success"></i>
                                    <div class="mt-1">بيع سريع</div>
                                </a>
                            </div>
                            <div class="col mb-2">
                                <a href="{{ route('seller.inventory') }}" class="btn btn-light btn-lg w-100">
                                    <i class="fas fa-boxes text-info"></i>
                                    <div class="mt-1">المخزون</div>
                                </a>
                            </div>
                            <div class="col mb-2">
                                <a href="{{ route('seller.customers.index') }}" class="btn btn-light btn-lg w-100">
                                    <i class="fas fa-users text-warning"></i>
                                    <div class="mt-1">العملاء</div>
                                </a>
                            </div>

                            <div class="col mb-2">
                                <a href="{{ route('seller.transfers.direct.create') }}"
                                    class="btn btn-light btn-lg w-100">
                                    <i class="fas fa-exchange-alt text-primary"></i>
                                    <div class="mt-1">نقل مباشر</div>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>






        <!-- Quick Direct Transfer Section -->
        {{-- <div class="row mb-4">
            <div class="col-12">
                <div class="card border-primary">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-exchange-alt"></i>
                            نقل مباشر للمنتجات
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <label for="quickTransferProduct" class="form-label fw-bold">اختر المنتج</label>
                                <select class="form-select" id="quickTransferProduct">
                                    <option value="">ابحث عن منتج...</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="quickTransferBranch" class="form-label fw-bold">الفرع المستهدف</label>
                                <select class="form-select" id="quickTransferBranch">
                                    <option value="">اختر الفرع...</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="quickTransferQuantity" class="form-label fw-bold">الكمية</label>
                                <input type="number" class="form-control" id="quickTransferQuantity" min="1"
                                    value="1">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="button" class="btn btn-success btn-lg"
                                        onclick="executeQuickTransfer()">
                                        <i class="fas fa-paper-plane"></i> تنفيذ النقل المباشر
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="mt-3">
                            <div class="alert alert-info mb-0">
                                <i class="fas fa-info-circle"></i>
                                <strong>نقل مباشر:</strong> يتم تنفيذ النقل فوراً بدون الحاجة لموافقة. المنتجات ستصل
                                للفرع المستهدف مباشرة.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div> --}}

        <!-- Statistics Cards -->
        {{-- <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">مبيعات اليوم</h6>
                                <h3 class="mb-0">{{ format_currency($stats['today_sales']) }}</h3>
                                <small class="text-white-50">{{ $stats['today_sales_count'] }} عملية</small>
                            </div>
                            <div class="text-white-50">
                                <i class="fas fa-chart-line fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">مبيعات الشهر</h6>
                                <h3 class="mb-0">{{ format_currency($stats['month_sales']) }}</h3>
                                <small class="text-white-50">{{ now()->format('M Y') }}</small>
                            </div>
                            <div class="text-white-50">
                                <i class="fas fa-calendar-month fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">منتجات قليلة المخزون</h6>
                                <h3 class="mb-0">{{ $stats['low_stock_products'] }}</h3>
                                <small class="text-white-50">تحتاج إعادة تموين</small>
                            </div>
                            <div class="text-white-50">
                                <i class="fas fa-exclamation-triangle fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">مستحقات العملاء</h6>
                                <h3 class="mb-0">{{ format_currency($customerSummary['total_owed'] ?? 0) }}</h3>
                                <small class="text-white-50">{{ $customerSummary['customers_count'] ?? 0 }}
                                    عميل</small>
                            </div>
                            <div class="text-white-50">
                                <i class="fas fa-money-bill-wave fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div> --}}

        <!-- Recent Sales and Low Stock -->
        <div class="row">
            <div class="col-lg-8 mb-3">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-shopping-cart"></i> أحدث المبيعات
                        </h5>
                        <a href="{{ route('seller.sales.index') }}" class="btn btn-sm btn-outline-primary">عرض
                            الكل</a>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>رقم الفاتورة</th>
                                        <th>العميل</th>
                                        <th>عدد المنتجات</th>
                                        <th>المبلغ</th>
                                        <th>التاريخ</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse($recentSales as $sale)
                                        <tr>
                                            <td>
                                                <span class="badge bg-primary">#{{ $sale->id }}</span>
                                            </td>
                                            <td>{{ $sale->customer->name ?? 'عميل نقدي' }}</td>
                                            <td>
                                                <span class="badge bg-secondary">{{ $sale->items->count() }}</span>
                                            </td>
                                            <td class="fw-bold">{{ format_currency($sale->total_amount) }}</td>
                                            <td>{{ $sale->created_at->format('Y-m-d H:i') }}</td>
                                            <td>
                                                <a href="{{ route('seller.sales.show', $sale) }}"
                                                    class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                @if ($sale->status === 'pending')
                                                    <a href="{{ route('seller.sales.edit', $sale) }}"
                                                        class="btn btn-sm btn-outline-warning">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                @endif
                                            </td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="6" class="text-center text-muted py-4">
                                                <i class="fas fa-shopping-cart fa-2x mb-2"></i>
                                                <div>لا توجد مبيعات حديثة</div>
                                                <a href="{{ route('seller.sales.create') }}"
                                                    class="btn btn-primary btn-sm mt-2">
                                                    إنشاء عملية بيع جديدة
                                                </a>
                                            </td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4 mb-3">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-exclamation-triangle text-warning"></i> منتجات قليلة المخزون
                        </h5>
                        <a href="{{ route('seller.inventory') }}" class="btn btn-sm btn-outline-warning">عرض الكل</a>
                    </div>
                    <div class="card-body p-0">
                        <div class="list-group list-group-flush">
                            @forelse($lowStockProducts as $product)
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-1">{{ $product->name }}</h6>
                                        <small class="text-muted">{{ $product->category->name ?? 'بدون فئة' }}</small>
                                    </div>
                                    <div class="text-end">
                                        <span class="badge bg-warning">
                                            {{ $product->branchInventories->first()->quantity ?? 0 }}
                                        </span>
                                        <div class="small text-muted">متبقي</div>
                                    </div>
                                </div>
                            @empty
                                <div class="list-group-item text-center text-muted py-4">
                                    <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                                    <div>جميع المنتجات متوفرة بكمية كافية</div>
                                </div>
                            @endforelse
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Customer Financial Summary -->
        @if (isset($customerSummary['customers']) && count($customerSummary['customers']) > 0)
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-users"></i> العملاء المدينون
                            </h5>
                            <a href="{{ route('seller.customers.index') }}" class="btn btn-sm btn-outline-primary">عرض
                                الكل</a>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th>اسم العميل</th>
                                            <th>رقم الهاتف</th>
                                            <th>إجمالي المشتريات</th>
                                            <th>المبلغ المدفوع</th>
                                            <th>المبلغ المستحق</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (array_slice($customerSummary['customers'], 0, 5) as $customer)
                                            <tr>
                                                <td>{{ $customer['name'] }}</td>
                                                <td>{{ $customer['phone'] ?? '-' }}</td>
                                                <td>{{ format_currency($customer['total_sales']) }}</td>
                                                <td class="text-success">
                                                    {{ format_currency($customer['total_paid']) }}</td>
                                                <td class="text-warning fw-bold">
                                                    {{ format_currency($customer['amount_owed']) }}</td>
                                                <td>
                                                    <a href="{{ route('seller.customers.show', $customer['id']) }}"
                                                        class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-eye"></i> عرض
                                                    </a>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @endif
    </div>

    <!-- JavaScript for Quick Transfer -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            loadAvailableProducts();
            loadAvailableBranches();
        });

        // Load products available in current branch
        function loadAvailableProducts() {
            fetch('{{ route('seller.inventory') }}?ajax=1')
                .then(response => response.json())
                .then(data => {
                    const select = document.getElementById('quickTransferProduct');
                    select.innerHTML = '<option value="">ابحث عن منتج...</option>';

                    if (data.products) {
                        data.products.forEach(product => {
                            if (product.local_quantity > 0) {
                                const option = document.createElement('option');
                                option.value = product.id;
                                option.textContent = `${product.name} (متوفر: ${product.local_quantity})`;
                                option.dataset.maxQuantity = product.local_quantity;
                                select.appendChild(option);
                            }
                        });
                    }
                })
                .catch(error => {
                    console.error('Error loading products:', error);
                    // Fallback: Add some sample options
                    const select = document.getElementById('quickTransferProduct');
                    select.innerHTML = '<option value="">لا توجد منتجات متوفرة</option>';
                });
        }

        // Load available branches for transfer
        function loadAvailableBranches() {
            fetch('{{ route('seller.transfer.branches') }}')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const select = document.getElementById('quickTransferBranch');
                        select.innerHTML = '<option value="">اختر الفرع...</option>';

                        data.branches.forEach(branch => {
                            const option = document.createElement('option');
                            option.value = branch.id;
                            option.textContent = `${branch.name} ${branch.code ? '(' + branch.code + ')' : ''}`;
                            select.appendChild(option);
                        });
                    }
                })
                .catch(error => {
                    console.error('Error loading branches:', error);
                });
        }

        // Update max quantity when product is selected
        document.getElementById('quickTransferProduct').addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            const maxQuantity = selectedOption.dataset.maxQuantity || 1;
            const quantityInput = document.getElementById('quickTransferQuantity');
            quantityInput.max = maxQuantity;
            quantityInput.value = Math.min(quantityInput.value, maxQuantity);
        });

        // Execute quick direct transfer
        function executeQuickTransfer() {
            const productId = document.getElementById('quickTransferProduct').value;
            const branchId = document.getElementById('quickTransferBranch').value;
            const quantity = parseInt(document.getElementById('quickTransferQuantity').value);

            if (!productId) {
                toastr.error('يرجى اختيار المنتج');
                return;
            }

            if (!branchId) {
                toastr.error('يرجى اختيار الفرع المستهدف');
                return;
            }

            if (!quantity || quantity <= 0) {
                toastr.error('يرجى إدخال كمية صحيحة');
                return;
            }

            // Show confirmation dialog
            if (!confirm(`هل أنت متأكد من تنفيذ النقل المباشر؟\nسيتم نقل ${quantity} وحدة إلى الفرع المحدد فوراً.`)) {
                return;
            }

            // Disable button during processing
            const button = document.querySelector('button[onclick="executeQuickTransfer()"]');
            const originalText = button.innerHTML;
            button.disabled = true;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التنفيذ...';

            // Send direct transfer request
            fetch('{{ route('seller.transfer.direct') }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    body: JSON.stringify({
                        product_id: productId,
                        destination_branch_id: branchId,
                        quantity: quantity,
                        notes: 'نقل مباشر سريع من لوحة التحكم'
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        toastr.success('تم تنفيذ النقل المباشر بنجاح! ' + data.message);
                        // Reset form
                        document.getElementById('quickTransferProduct').value = '';
                        document.getElementById('quickTransferBranch').value = '';
                        document.getElementById('quickTransferQuantity').value = 1;
                        // Reload products to update quantities
                        loadAvailableProducts();
                    } else {
                        toastr.error(data.message || 'حدث خطأ أثناء تنفيذ النقل المباشر');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    toastr.error('حدث خطأ أثناء تنفيذ النقل المباشر');
                })
                .finally(() => {
                    // Re-enable button
                    button.disabled = false;
                    button.innerHTML = originalText;
                });
        }
    </script>
</x-app-layout>
