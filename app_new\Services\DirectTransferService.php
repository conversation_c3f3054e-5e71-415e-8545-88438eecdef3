<?php

namespace App\Services;

use App\Models\Product;
use App\Models\User;
use App\Models\Branch;
use App\Models\BranchInventory;
use App\Models\StoreInventory;
use App\Models\InventoryTransfer;
use App\Models\InventoryTransferItem;
use Illuminate\Support\Facades\DB;
use Exception;

class DirectTransferService
{
    /**
     * Execute a direct transfer between branches
     */
    public function executeDirectTransfer(User $user, int $productId, int $destinationBranchId, int $quantity, string $notes = null): array
    {
        // Validate user has permission to transfer
        if (!$user->branch_id) {
            throw new Exception('المستخدم غير مرتبط بفرع');
        }

        if ($user->branch_id === $destinationBranchId) {
            throw new Exception('لا يمكن النقل إلى نفس الفرع');
        }

        // Get product and validate
        $product = Product::findOrFail($productId);

        // Get source branch inventory
        $sourceBranchInventory = BranchInventory::where('branch_id', $user->branch_id)
            ->where('product_id', $productId)
            ->first();

        if (!$sourceBranchInventory || $sourceBranchInventory->quantity < $quantity) {
            throw new Exception('الكمية المطلوبة غير متوفرة في فرعك');
        }

        // Get destination branch
        $destinationBranch = Branch::findOrFail($destinationBranchId);

        DB::beginTransaction();
        try {
            // Create transfer record
            $transfer = InventoryTransfer::create([
                'transfer_number' => InventoryTransfer::generateTransferNumber(),
                'type' => 'direct',
                'source_type' => 'branch',
                'source_id' => $user->branch_id,
                'destination_type' => 'branch',
                'destination_id' => $destinationBranchId,
                'requested_by' => $user->id,
                'status' => 'shipped', // Direct transfer goes straight to shipped
                'notes' => $notes ?? "نقل مباشر من {$user->branch->name} إلى {$destinationBranch->name}",
                'requested_at' => now(),
                'shipped_at' => now(),
            ]);

            // Create transfer item
            $transferItem = InventoryTransferItem::create([
                'inventory_transfer_id' => $transfer->id,
                'product_id' => $productId,
                'requested_quantity' => $quantity,
                'approved_quantity' => $quantity, // Auto-approved for direct transfers
                'unit_cost' => $product->purchase_price ?? 0,
                'notes' => 'نقل مباشر',
            ]);

            // Deduct from source branch immediately
            $sourceBranchInventory->decrement('quantity', $quantity);

            // Log the transfer in inventory movements (if you have such a system)
            $this->logInventoryMovement($user->branch_id, $productId, -$quantity, 'direct_transfer_out', $transfer->id);

            DB::commit();

            return [
                'success' => true,
                'message' => "تم نقل {$quantity} من {$product->name} إلى {$destinationBranch->name} بنجاح",
                'transfer_id' => $transfer->id,
                'transfer_number' => $transfer->transfer_number,
            ];
        } catch (Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    /**
     * Execute auto transfer from best available location to user's branch
     */
    public function executeAutoTransfer(User $user, int $productId, int $quantity): array
    {
        // Validate user has a branch
        if (!$user->branch_id) {
            throw new Exception('المستخدم غير مرتبط بفرع');
        }

        // Get product and validate
        $product = Product::findOrFail($productId);

        // Find best source location (prioritize stores, then other branches)
        $bestSource = $this->findBestSourceLocation($productId, $quantity, $user->branch_id);

        if (!$bestSource) {
            throw new Exception('المنتج غير متوفر في أي موقع آخر بالكمية المطلوبة');
        }

        // Execute transfer from best source to user's branch
        if ($bestSource['type'] === 'store') {
            return $this->executeStoreTobranchTransfer($user, $productId, $bestSource['id'], $quantity);
        } else {
            return $this->executeBranchToBranchTransfer($user, $productId, $bestSource['id'], $quantity);
        }
    }

    /**
     * Find the best source location for a product
     */
    private function findBestSourceLocation(int $productId, int $quantity, int $excludeBranchId): ?array
    {
        // First, check stores (preferred source)
        $storeInventory = StoreInventory::where('product_id', $productId)
            ->where('quantity', '>=', $quantity)
            ->orderBy('quantity', 'desc')
            ->first();

        if ($storeInventory) {
            return [
                'type' => 'store',
                'id' => $storeInventory->store_id,
                'quantity' => $storeInventory->quantity
            ];
        }

        // Then check other branches
        $branchInventory = BranchInventory::where('product_id', $productId)
            ->where('branch_id', '!=', $excludeBranchId)
            ->where('quantity', '>=', $quantity)
            ->orderBy('quantity', 'desc')
            ->first();

        if ($branchInventory) {
            return [
                'type' => 'branch',
                'id' => $branchInventory->branch_id,
                'quantity' => $branchInventory->quantity
            ];
        }

        return null;
    }

    /**
     * Execute transfer from store to branch
     */
    private function executeStoreTobranchTransfer(User $user, int $productId, int $storeId, int $quantity): array
    {
        // Use existing direct transfer logic but adapted for store-to-branch
        $product = Product::findOrFail($productId);

        // Get source store inventory
        $sourceInventory = StoreInventory::where('store_id', $storeId)
            ->where('product_id', $productId)
            ->first();

        if (!$sourceInventory || $sourceInventory->quantity < $quantity) {
            throw new Exception('الكمية المطلوبة غير متوفرة في المخزن');
        }

        // Get or create destination branch inventory
        $destinationInventory = BranchInventory::firstOrCreate(
            ['branch_id' => $user->branch_id, 'product_id' => $productId],
            [
                'quantity' => 0,
                'cost_price' => $sourceInventory->cost_price ?? 0,
                'sale_price_1' => $sourceInventory->sale_price_1 ?? $product->selling_price ?? $product->price ?? 0.01,
                'sale_price_2' => $sourceInventory->sale_price_2,
                'sale_price_3' => $sourceInventory->sale_price_3,
            ]
        );

        DB::beginTransaction();
        try {
            // Create transfer record
            $transfer = InventoryTransfer::create([
                'transfer_number' => InventoryTransfer::generateTransferNumber(),
                'type' => 'store_to_branch',
                'source_type' => 'store',
                'source_id' => $storeId,
                'destination_type' => 'branch',
                'destination_id' => $user->branch_id,
                'requested_by' => $user->id,
                'approved_by' => $user->id,
                'received_by' => $user->id,
                'status' => 'completed',
                'notes' => 'نقل تلقائي من صفحة البيع السريع',
                'requested_at' => now(),
                'approved_at' => now(),
                'received_at' => now(),
            ]);

            // Create transfer item
            InventoryTransferItem::create([
                'inventory_transfer_id' => $transfer->id,
                'product_id' => $productId,
                'requested_quantity' => $quantity,
                'approved_quantity' => $quantity,
                'received_quantity' => $quantity,
                'unit_cost' => $sourceInventory->unit_cost ?? $product->cost_price,
                'notes' => null,
            ]);

            // Update inventories
            $sourceInventory->quantity -= $quantity;
            $sourceInventory->save();

            $destinationInventory->quantity += $quantity;
            $destinationInventory->save();

            DB::commit();

            return [
                'success' => true,
                'message' => "تم نقل {$quantity} قطعة من {$product->name} من المخزن إلى فرعك بنجاح",
                'transfer_id' => $transfer->id
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Execute transfer from branch to branch
     */
    private function executeBranchToBranchTransfer(User $user, int $productId, int $sourceBranchId, int $quantity): array
    {
        $product = Product::findOrFail($productId);

        // Get source branch inventory
        $sourceInventory = BranchInventory::where('branch_id', $sourceBranchId)
            ->where('product_id', $productId)
            ->first();

        if (!$sourceInventory || $sourceInventory->quantity < $quantity) {
            throw new Exception('الكمية المطلوبة غير متوفرة في الفرع المصدر');
        }

        // Get or create destination branch inventory
        $destinationInventory = BranchInventory::firstOrCreate(
            ['branch_id' => $user->branch_id, 'product_id' => $productId],
            [
                'quantity' => 0,
                'cost_price' => $sourceInventory->cost_price ?? 0,
                'sale_price_1' => $sourceInventory->sale_price_1 ?? $product->selling_price ?? $product->price ?? 0.01,
                'sale_price_2' => $sourceInventory->sale_price_2,
                'sale_price_3' => $sourceInventory->sale_price_3,
            ]
        );

        DB::beginTransaction();
        try {
            // Create transfer record
            $transfer = InventoryTransfer::create([
                'transfer_number' => InventoryTransfer::generateTransferNumber(),
                'type' => 'branch_to_branch',
                'source_type' => 'branch',
                'source_id' => $sourceBranchId,
                'destination_type' => 'branch',
                'destination_id' => $user->branch_id,
                'requested_by' => $user->id,
                'approved_by' => $user->id,
                'received_by' => $user->id,
                'status' => 'completed',
                'notes' => 'نقل تلقائي من صفحة البيع السريع',
                'requested_at' => now(),
                'approved_at' => now(),
                'received_at' => now(),
            ]);

            // Create transfer item
            InventoryTransferItem::create([
                'inventory_transfer_id' => $transfer->id,
                'product_id' => $productId,
                'requested_quantity' => $quantity,
                'approved_quantity' => $quantity,
                'received_quantity' => $quantity,
                'unit_cost' => $sourceInventory->unit_cost ?? $product->cost_price,
                'notes' => null,
            ]);

            // Update inventories
            $sourceInventory->quantity -= $quantity;
            $sourceInventory->save();

            $destinationInventory->quantity += $quantity;
            $destinationInventory->save();

            DB::commit();

            return [
                'success' => true,
                'message' => "تم نقل {$quantity} قطعة من {$product->name} من فرع آخر إلى فرعك بنجاح",
                'transfer_id' => $transfer->id
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Get available branches for transfer (excluding user's own branch)
     */
    public function getAvailableBranches(User $user): \Illuminate\Database\Eloquent\Collection
    {
        return Branch::where('is_active', true)
            ->where('id', '!=', $user->branch_id)
            ->orderBy('name')
            ->get();
    }

    /**
     * Get pending transfers for a branch to confirm
     */
    public function getPendingTransfersForBranch(int $branchId): \Illuminate\Database\Eloquent\Collection
    {
        return InventoryTransfer::with(['items.product', 'requestedBy', 'sourceBranch', 'sourceStore'])
            ->where('destination_type', 'branch')
            ->where('destination_id', $branchId)
            ->where('status', 'shipped')
            ->where('type', 'direct')
            ->orderBy('shipped_at', 'desc')
            ->get();
    }

    /**
     * Confirm receipt of a direct transfer
     */
    public function confirmTransferReceipt(int $transferId, User $user, array $receivedQuantities = []): array
    {
        $transfer = InventoryTransfer::with(['items.product'])
            ->where('id', $transferId)
            ->where('destination_id', $user->branch_id)
            ->where('status', 'shipped')
            ->where('type', 'direct')
            ->firstOrFail();

        DB::beginTransaction();
        try {
            foreach ($transfer->items as $item) {
                $receivedQty = $receivedQuantities[$item->id] ?? $item->approved_quantity;

                // Validate received quantity
                if ($receivedQty > $item->approved_quantity) {
                    throw new Exception("الكمية المستلمة لا يمكن أن تكون أكبر من الكمية المرسلة للمنتج {$item->product->name}");
                }

                if ($receivedQty > 0) {
                    // Get source pricing information
                    $sourceBranchInventory = BranchInventory::where('branch_id', $transfer->source_id)
                        ->where('product_id', $item->product_id)
                        ->first();

                    // Add to destination branch inventory
                    $destinationInventory = BranchInventory::firstOrCreate(
                        [
                            'branch_id' => $user->branch_id,
                            'product_id' => $item->product_id,
                        ],
                        [
                            'quantity' => 0,
                            'minimum_stock' => 0,
                            'cost_price' => $sourceBranchInventory ? $sourceBranchInventory->cost_price : ($item->product->price ?? 0),
                            'sale_price_1' => $sourceBranchInventory ? $sourceBranchInventory->sale_price_1 : ($item->product->selling_price ?? $item->product->price ?? 0),
                            'sale_price_2' => $sourceBranchInventory ? $sourceBranchInventory->sale_price_2 : null,
                            'sale_price_3' => $sourceBranchInventory ? $sourceBranchInventory->sale_price_3 : null,
                        ]
                    );

                    $destinationInventory->increment('quantity', $receivedQty);

                    // Update transfer item with received quantity
                    $item->update(['received_quantity' => $receivedQty]);

                    // Log the transfer in inventory movements
                    $this->logInventoryMovement($user->branch_id, $item->product_id, $receivedQty, 'direct_transfer_in', $transfer->id);
                }
            }

            // Update transfer status
            $transfer->update([
                'status' => 'completed',
                'received_by' => $user->id,
                'received_at' => now(),
            ]);

            DB::commit();

            return [
                'success' => true,
                'message' => 'تم تأكيد استلام النقل بنجاح',
                'transfer' => $transfer,
            ];
        } catch (Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    /**
     * Log inventory movement (placeholder - implement based on your system)
     */
    private function logInventoryMovement(int $branchId, int $productId, int $quantity, string $type, int $referenceId): void
    {
        // Implement inventory movement logging if you have such a system
        // This could be a separate InventoryMovement model
    }

    /**
     * Get transfer statistics for a branch
     */
    public function getTransferStats(int $branchId): array
    {
        $outgoingCount = InventoryTransfer::where('source_type', 'branch')
            ->where('source_id', $branchId)
            ->where('type', 'direct')
            ->count();

        $incomingPending = InventoryTransfer::where('destination_type', 'branch')
            ->where('destination_id', $branchId)
            ->where('status', 'shipped')
            ->where('type', 'direct')
            ->count();

        $incomingCompleted = InventoryTransfer::where('destination_type', 'branch')
            ->where('destination_id', $branchId)
            ->where('status', 'completed')
            ->where('type', 'direct')
            ->count();

        return [
            'outgoing_transfers' => $outgoingCount,
            'incoming_pending' => $incomingPending,
            'incoming_completed' => $incomingCompleted,
        ];
    }
}
