<x-app-layout>
    <x-slot name="header">
        <div class="d-flex justify-content-between align-items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('تعديل تصنيف المصروفات') }}
            </h2>
            <a href="{{ route('expense-categories.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-right"></i> {{ __('عودة') }}
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="container-fluid">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    <form action="{{ route('expense-categories.update', $expenseCategory) }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        @method('PUT')

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="name" class="form-label">{{ __('الاسم') }} <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror" id="name" name="name" value="{{ old('name', $expenseCategory->name) }}" required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="form-group mb-3">
                                    <label for="description" class="form-label">{{ __('الوصف') }}</label>
                                    <textarea class="form-control @error('description') is-invalid @enderror" id="description" name="description" rows="3">{{ old('description', $expenseCategory->description) }}</textarea>
                                    @error('description')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="form-group mb-3">
                                    <label for="parent_id" class="form-label">{{ __('التصنيف الأب') }}</label>
                                    <select class="form-select @error('parent_id') is-invalid @enderror" id="parent_id" name="parent_id">
                                        <option value="">{{ __('بدون تصنيف أب') }}</option>
                                        @foreach($parentCategories as $parent)
                                            <option value="{{ $parent->id }}" {{ old('parent_id', $expenseCategory->parent_id) == $parent->id ? 'selected' : '' }}>
                                                {{ $parent->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('parent_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="form-group mb-3">
                                    <label for="color" class="form-label">{{ __('اللون') }}</label>
                                    <input type="color" class="form-control form-control-color @error('color') is-invalid @enderror" id="color" name="color" value="{{ old('color', $expenseCategory->color ?: '#000000') }}">
                                    @error('color')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="form-group mb-3">
                                    <label for="icon" class="form-label">{{ __('الأيقونة') }}</label>
                                    @if($expenseCategory->icon)
                                        <div class="mb-2">
                                            <img src="{{ Storage::url($expenseCategory->icon) }}" alt="{{ $expenseCategory->name }}" class="img-thumbnail" style="max-width: 100px;">
                                        </div>
                                    @endif
                                    <input type="file" class="form-control @error('icon') is-invalid @enderror" id="icon" name="icon" accept="image/*">
                                    @error('icon')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="monthly_budget" class="form-label">{{ __('الميزانية الشهرية') }}</label>
                                    <input type="number" step="0.01" class="form-control @error('monthly_budget') is-invalid @enderror" id="monthly_budget" name="monthly_budget" value="{{ old('monthly_budget', $expenseCategory->monthly_budget) }}">
                                    @error('monthly_budget')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="form-group mb-3">
                                    <label for="yearly_budget" class="form-label">{{ __('الميزانية السنوية') }}</label>
                                    <input type="number" step="0.01" class="form-control @error('yearly_budget') is-invalid @enderror" id="yearly_budget" name="yearly_budget" value="{{ old('yearly_budget', $expenseCategory->yearly_budget) }}">
                                    @error('yearly_budget')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="form-group mb-3">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input @error('is_fixed') is-invalid @enderror" id="is_fixed" name="is_fixed" value="1" {{ old('is_fixed', $expenseCategory->is_fixed) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="is_fixed">{{ __('مصروف ثابت') }}</label>
                                        @error('is_fixed')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                <div class="form-group mb-3">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input @error('requires_approval') is-invalid @enderror" id="requires_approval" name="requires_approval" value="1" {{ old('requires_approval', $expenseCategory->requires_approval) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="requires_approval">{{ __('يتطلب موافقة') }}</label>
                                        @error('requires_approval')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                <div class="form-group mb-3">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input @error('is_active') is-invalid @enderror" id="is_active" name="is_active" value="1" {{ old('is_active', $expenseCategory->is_active) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="is_active">{{ __('نشط') }}</label>
                                        @error('is_active')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-12 text-start">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> {{ __('حفظ') }}
                                </button>
                                <a href="{{ route('expense-categories.index') }}" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> {{ __('إلغاء') }}
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    @push('styles')
    <style>
        .rtl {
            direction: rtl;
            text-align: right;
        }
        .rtl .form-select {
            text-align: right;
        }
        .rtl .form-control {
            text-align: right;
        }
        .rtl .form-control::placeholder {
            text-align: right;
        }
        .form-control-color {
            width: 100%;
            height: 38px;
        }
    </style>
    @endpush
</x-app-layout>
