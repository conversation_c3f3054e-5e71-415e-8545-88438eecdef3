<?php

namespace App\Http\Controllers;

use App\Models\Branch;
use App\Models\Expense;
use App\Models\ExpenseCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class ExpenseController extends Controller
{
    public function index(Request $request)
    {
        $query = Expense::with(['branch', 'category'])
            ->when($request->filled('search'), function ($query) use ($request) {
                $search = $request->search;
                $query->where(function ($q) use ($search) {
                    $q->where('reference_number', 'like', "%{$search}%")
                        ->orWhere('description', 'like', "%{$search}%");
                });
            })
            ->when($request->filled('category'), function ($query) use ($request) {
                $query->where('expense_category_id', $request->category);
            })
            ->when($request->filled('branch'), function ($query) use ($request) {
                $query->where('branch_id', $request->branch);
            })
            ->when($request->filled('date_range'), function ($query) use ($request) {
                $dates = explode(' - ', $request->date_range);
                $query->whereBetween('expense_date', $dates);
            });

        // Calculate totals
        $totalExpenses = Expense::sum('amount');
        $todayExpenses = Expense::whereDate('expense_date', today())->sum('amount');
        $monthExpenses = Expense::whereMonth('expense_date', now()->month)
            ->whereYear('expense_date', now()->year)
            ->sum('amount');
        $expensesCount = Expense::count();

        $expenses = $query->latest()->paginate(10);
        $categories = ExpenseCategory::where('is_active', true)->get();
        $branches = Branch::where('is_active', true)->get();

        return view('expenses.index', compact(
            'expenses',
            'categories',
            'branches',
            'totalExpenses',
            'todayExpenses',
            'monthExpenses',
            'expensesCount'
        ));
    }

    public function create()
    {
        $branches = Branch::where('is_active', true)->get();
        $categories = ExpenseCategory::where('is_active', true)->get();
        return view('expenses.create', compact('branches', 'categories'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'branch_id' => 'required|exists:branches,id',
            'expense_category_id' => 'required|exists:expense_categories,id',
            'expense_date' => 'required|date',
            'amount' => 'required|numeric|min:0.01',
            'description' => 'nullable|string',
            'payment_method' => 'required|in:cash,bank',
        ]);

        try {
            DB::beginTransaction();

            $expense = Expense::create([
                'reference_number' => 'EXP-' . strtoupper(Str::random(8)),
                'branch_id' => $validated['branch_id'],
                'expense_category_id' => $validated['expense_category_id'],
                'expense_date' => $validated['expense_date'],
                'amount' => $validated['amount'],
                'description' => $validated['description'],
                'payment_method' => $validated['payment_method'],
                'user_id' => Auth::id(),
            ]);

            // Create cash transaction
            $expense->cashTransaction()->create([
                'branch_id' => $validated['branch_id'],
                'type' => 'expense',
                'amount' => -$validated['amount'],
                'notes' => $validated['description'],
            ]);

            DB::commit();

            return redirect()->route('expenses.show', $expense)
                ->with('success', 'تم إضافة المصروف بنجاح');
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', 'حدث خطأ أثناء إضافة المصروف');
        }
    }

    public function show(Expense $expense)
    {
        $expense->load(['branch', 'category', 'cashTransaction', 'user']);
        return view('expenses.show', compact('expense'));
    }

    public function edit(Expense $expense)
    {
        $branches = Branch::where('is_active', true)->get();
        $categories = ExpenseCategory::where('is_active', true)->get();
        return view('expenses.edit', compact('expense', 'branches', 'categories'));
    }

    public function update(Request $request, Expense $expense)
    {
        $validated = $request->validate([
            'branch_id' => 'required|exists:branches,id',
            'expense_category_id' => 'required|exists:expense_categories,id',
            'expense_date' => 'required|date',
            'amount' => 'required|numeric|min:0.01',
            'description' => 'nullable|string',
            'payment_method' => 'required|in:cash,bank',
        ]);

        try {
            DB::beginTransaction();

            $expense->update([
                'branch_id' => $validated['branch_id'],
                'expense_category_id' => $validated['expense_category_id'],
                'expense_date' => $validated['expense_date'],
                'amount' => $validated['amount'],
                'description' => $validated['description'],
                'payment_method' => $validated['payment_method'],
            ]);

            // Update cash transaction
            $expense->cashTransaction()->update([
                'branch_id' => $validated['branch_id'],
                'amount' => -$validated['amount'],
                'notes' => $validated['description'],
            ]);

            DB::commit();

            return redirect()->route('expenses.show', $expense)
                ->with('success', 'تم تحديث المصروف بنجاح');
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', 'حدث خطأ أثناء تحديث المصروف');
        }
    }

    public function destroy(Expense $expense)
    {
        try {
            DB::beginTransaction();

            // Delete cash transaction
            $expense->cashTransaction()->delete();
            $expense->delete();

            DB::commit();

            return redirect()->route('expenses.index')
                ->with('success', 'تم حذف المصروف بنجاح');
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', 'حدث خطأ أثناء حذف المصروف');
        }
    }

    public function export(Request $request)
    {
        $query = Expense::with(['branch', 'category', 'user'])
            ->when($request->filled('search'), function ($query) use ($request) {
                $search = $request->search;
                $query->where(function ($q) use ($search) {
                    $q->where('reference_number', 'like', "%{$search}%")
                        ->orWhere('description', 'like', "%{$search}%");
                });
            })
            ->when($request->filled('category'), function ($query) use ($request) {
                $query->where('expense_category_id', $request->category);
            })
            ->when($request->filled('branch'), function ($query) use ($request) {
                $query->where('branch_id', $request->branch);
            })
            ->when($request->filled('date_range'), function ($query) use ($request) {
                $dates = explode(' - ', $request->date_range);
                $query->whereBetween('expense_date', $dates);
            });

        $expenses = $query->latest()->get();

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="expenses.csv"',
        ];

        $callback = function () use ($expenses) {
            $file = fopen('php://output', 'w');

            // Add UTF-8 BOM for proper Arabic encoding
            fprintf($file, chr(0xEF) . chr(0xBB) . chr(0xBF));

            // Add headers
            fputcsv($file, [
                'التاريخ',
                'المرجع',
                'التصنيف',
                'الفرع',
                'الوصف',
                'المبلغ',
                'طريقة الدفع',
                'المستخدم',
                'الحالة'
            ]);

            // Add data
            foreach ($expenses as $expense) {
                fputcsv($file, [
                    $expense->expense_date->format('Y-m-d'),
                    $expense->reference_number,
                    $expense->category->name,
                    $expense->branch->name,
                    $expense->description,
                    number_format($expense->amount, 2),
                    $expense->payment_method,
                    $expense->user->name,
                    $expense->status
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
