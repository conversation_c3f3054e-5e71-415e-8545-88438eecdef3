<?php

namespace App\Services;

use App\Models\Customer;
use App\Models\Supplier;
use App\Models\Account;
use App\Models\User;
use App\Models\Branch;
use App\Models\Store;
use App\Models\Sale;
use App\Models\Purchase;
use Illuminate\Support\Collection;

class FinancialService
{
    /**
     * Get financial summary for customers
     */
    public function getCustomerFinancialSummary(User $user): array
    {
        $query = Customer::with('account');

        if (!$user->canAccessAllBranches()) {
            // Filter customers based on sales in user's branch
            $query->whereHas('sales', function ($q) use ($user) {
                $q->where('branch_id', $user->branch_id);
            });
        }

        $customers = $query->get();

        return [
            'total_customers' => $customers->count(),
            'total_owed_to_us' => $customers->sum(fn($c) => $c->getTotalOutstandingAmount()),
            'total_credit_balance' => $customers->sum(fn($c) => $c->getCreditAmount()),
            'customers_with_debt' => $customers->filter(fn($c) => $c->getTotalOutstandingAmount() > 0)->count(),
            'customers_with_credit' => $customers->filter(fn($c) => $c->getCreditAmount() > 0)->count(),
            'total_net_sales' => $customers->sum(fn($c) => $c->getNetSalesAmount()),
        ];
    }

    /**
     * Get financial summary for suppliers
     */
    public function getSupplierFinancialSummary(User $user): array
    {
        $query = Supplier::with('account');

        if (!$user->canAccessAllBranches()) {
            // Filter suppliers based on purchases in user's branch
            $query->whereHas('purchases', function ($q) use ($user) {
                $q->where('branch_id', $user->branch_id);
            });
        }

        $suppliers = $query->get();

        return [
            'total_suppliers' => $suppliers->count(),
            'total_we_owe' => $suppliers->sum(fn($s) => $s->getOwedAmount()),
            'total_supplier_debt' => $suppliers->sum(fn($s) => $s->getCreditAmount()),
            'suppliers_we_owe' => $suppliers->filter(fn($s) => $s->getOwedAmount() > 0)->count(),
            'suppliers_owing_us' => $suppliers->filter(fn($s) => $s->getCreditAmount() > 0)->count(),
        ];
    }

    /**
     * Get customers with outstanding balances
     */
    public function getCustomersWithOutstandingBalances(User $user, int $limit = 10): Collection
    {
        $query = Customer::with('account')
            ->whereHas('sales', function ($q) {
                $q->whereRaw('total_amount > paid_amount');
            });

        if (!$user->canAccessAllBranches()) {
            $query->whereHas('sales', function ($q) use ($user) {
                $q->where('branch_id', $user->branch_id);
            });
        }

        return $query->limit($limit)
            ->get()
            ->map(function ($customer) {
                return [
                    'id' => $customer->id,
                    'name' => $customer->name,
                    'phone' => $customer->phone,
                    'owed_amount' => $customer->getTotalOutstandingAmount(),
                    'total_sales' => $customer->getTotalSales(),
                ];
            })
            ->sortByDesc('owed_amount')
            ->values();
    }

    /**
     * Get suppliers we owe money to
     */
    public function getSuppliersWeOwe(User $user, int $limit = 10): Collection
    {
        $query = Supplier::with('account')
            ->whereHas('purchases', function ($q) {
                $q->where('remaining_amount', '>', 0);
            });

        if (!$user->canAccessAllBranches()) {
            $query->whereHas('purchases', function ($q) use ($user) {
                $q->where('branch_id', $user->branch_id);
            });
        }

        return $query->orderByDesc(function ($query) {
            return $query->selectRaw('SUM(remaining_amount)')
                ->from('purchases')
                ->whereColumn('purchases.supplier_id', 'suppliers.id')
                ->where('remaining_amount', '>', 0);
        })
            ->limit($limit)
            ->get()
            ->map(function ($supplier) {
                return [
                    'id' => $supplier->id,
                    'name' => $supplier->name,
                    'phone' => $supplier->phone,
                    'owed_amount' => $supplier->getTotalOutstandingAmount(),
                    'total_purchases' => $supplier->getTotalPurchases(),
                ];
            });
    }

    /**
     * Get branch financial summary
     */
    public function getBranchFinancialSummary(Branch $branch): array
    {
        $customerSummary = $this->getCustomerFinancialSummaryForBranch($branch);
        $supplierSummary = $this->getSupplierFinancialSummaryForBranch($branch);

        return [
            'branch_id' => $branch->id,
            'branch_name' => $branch->name,
            'customers' => $customerSummary,
            'suppliers' => $supplierSummary,
            'net_receivables' => $customerSummary['total_owed_to_us'] - $customerSummary['total_credit_balance'],
            'net_payables' => $supplierSummary['total_we_owe'] - $supplierSummary['total_supplier_debt'],
        ];
    }

    private function getCustomerFinancialSummaryForBranch(Branch $branch): array
    {
        $customers = Customer::with('account')
            ->whereHas('sales', function ($q) use ($branch) {
                $q->where('branch_id', $branch->id);
            })
            ->get();

        return [
            'total_customers' => $customers->count(),
            'total_owed_to_us' => $customers->sum(fn($c) => $c->getTotalOutstandingAmount()),
            'total_credit_balance' => $customers->sum(fn($c) => $c->getCreditAmount()),
            'total_net_sales' => $customers->sum(fn($c) => $c->getNetSalesAmount()),
        ];
    }

    private function getSupplierFinancialSummaryForBranch(Branch $branch): array
    {
        $suppliers = Supplier::with('account')
            ->whereHas('purchases', function ($q) use ($branch) {
                $q->where('branch_id', $branch->id);
            })
            ->get();

        return [
            'total_suppliers' => $suppliers->count(),
            'total_we_owe' => $suppliers->sum(fn($s) => $s->getOwedAmount()),
            'total_supplier_debt' => $suppliers->sum(fn($s) => $s->getCreditAmount()),
        ];
    }

    /**
     * Get overall financial summary for admin
     */
    public function getOverallFinancialSummary(): array
    {
        $branches = Branch::with(['stores'])->get();
        $branchSummaries = $branches->map(fn($branch) => $this->getBranchFinancialSummary($branch));

        // Get independent stores
        $independentStores = Store::independent()->get();
        $independentStoreSummary = $this->getIndependentStoresFinancialSummary($independentStores);

        // Calculate totals from all customers and suppliers
        $allCustomers = Customer::with('account')->get();
        $allSuppliers = Supplier::with('account')->get();

        // Calculate total sales and purchases
        $totalSales = Sale::sum('total_amount');
        $totalPurchases = Purchase::sum('total_amount');

        return [
            'total_branches' => $branches->count(),
            'total_stores' => Store::count(),
            'independent_stores' => $independentStores->count(),
            'branch_stores' => Store::whereNotNull('branch_id')->count(),
            'total_net_receivables' => $branchSummaries->sum('net_receivables') + $independentStoreSummary['net_receivables'],
            'total_net_payables' => $branchSummaries->sum('net_payables') + $independentStoreSummary['net_payables'],
            'branch_summaries' => $branchSummaries,
            'independent_stores_summary' => $independentStoreSummary,
            // Add the missing keys that the dashboard expects
            'total_sales' => $totalSales,
            'total_purchases' => $totalPurchases,
            'customers_owe' => $allCustomers->sum(fn($c) => $c->getTotalOutstandingAmount()),
            'suppliers_owe' => $allSuppliers->sum(fn($s) => $s->getTotalOutstandingAmount()),
        ];
    }

    /**
     * Get financial summary for independent stores
     */
    private function getIndependentStoresFinancialSummary($independentStores): array
    {
        $totalSales = 0;
        $totalPurchases = 0;
        $totalInventoryValue = 0;

        foreach ($independentStores as $store) {
            $totalSales += $store->getTotalSales();
            $totalPurchases += $store->getTotalPurchases();
            $totalInventoryValue += $store->getInventoryValue();
        }

        return [
            'total_stores' => $independentStores->count(),
            'total_sales' => $totalSales,
            'total_purchases' => $totalPurchases,
            'total_inventory_value' => $totalInventoryValue,
            'net_receivables' => 0, // Independent stores don't have separate customer accounts
            'net_payables' => 0, // Independent stores don't have separate supplier accounts
        ];
    }
}
