@extends('layouts.admin')

@section('title', 'تعديل مرتجع الشراء')

@section('content')
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">تعديل مرتجع الشراء - {{ $purchaseReturn->return_number }}</h3>
                        <div class="card-tools">
                            <a href="{{ route('admin.purchase-returns.show', $purchaseReturn) }}" class="btn btn-info">
                                <i class="fas fa-eye"></i> عرض التفاصيل
                            </a>
                            <a href="{{ route('admin.purchase-returns.index') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> العودة للقائمة
                            </a>
                        </div>
                    </div>

                    <form method="POST" action="{{ route('admin.purchase-returns.update', $purchaseReturn) }}"
                        id="editReturnForm">
                        @csrf
                        @method('PUT')
                        <div class="card-body">
                            <!-- Purchase Information (Read-only) -->
                            <div class="row mb-4">
                                <div class="col-md-4">
                                    <label class="form-label">عملية الشراء</label>
                                    <input type="text" class="form-control"
                                        value="{{ $purchaseReturn->purchase->invoice_number }} - {{ $purchaseReturn->supplier->name }}"
                                        readonly>
                                </div>
                                <div class="col-md-4">
                                    <label for="return_date" class="form-label">تاريخ المرتجع <span
                                            class="text-danger">*</span></label>
                                    <input type="date" name="return_date" id="return_date" class="form-control"
                                        value="{{ old('return_date', $purchaseReturn->return_date->format('Y-m-d')) }}"
                                        required>
                                    @error('return_date')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">الحالة</label>
                                    <input type="text" class="form-control" value="{{ $purchaseReturn->status }}"
                                        readonly>
                                </div>
                            </div>

                            <!-- Return Details -->
                            <div class="row mb-4">
                                <div class="col-md-4">
                                    <label for="return_type" class="form-label">نوع المرتجع <span
                                            class="text-danger">*</span></label>
                                    <select name="return_type" id="return_type" class="form-select" required>
                                        <option value="partial"
                                            {{ old('return_type', $purchaseReturn->return_type) == 'partial' ? 'selected' : '' }}>
                                            مرتجع جزئي</option>
                                        <option value="full"
                                            {{ old('return_type', $purchaseReturn->return_type) == 'full' ? 'selected' : '' }}>
                                            مرتجع كامل</option>
                                    </select>
                                    @error('return_type')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="col-md-4">
                                    <label for="refund_amount" class="form-label">مبلغ الاسترداد (ج.م) <span
                                            class="text-danger">*</span></label>
                                    <input type="number" name="refund_amount" id="refund_amount" class="form-control"
                                        step="0.01" min="0"
                                        value="{{ old('refund_amount', $purchaseReturn->refund_amount) }}" required>
                                    @error('refund_amount')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="col-md-4">
                                    <label for="reason" class="form-label">سبب المرتجع <span
                                            class="text-danger">*</span></label>
                                    <select name="reason" id="reason" class="form-select" required>
                                        <option value="">اختر السبب...</option>
                                        <option value="تالف"
                                            {{ old('reason', $purchaseReturn->reason) == 'تالف' ? 'selected' : '' }}>تالف
                                        </option>
                                        <option value="منتهي الصلاحية"
                                            {{ old('reason', $purchaseReturn->reason) == 'منتهي الصلاحية' ? 'selected' : '' }}>
                                            منتهي الصلاحية</option>
                                        <option value="خطأ في الطلب"
                                            {{ old('reason', $purchaseReturn->reason) == 'خطأ في الطلب' ? 'selected' : '' }}>
                                            خطأ في الطلب</option>
                                        <option value="عيب في التصنيع"
                                            {{ old('reason', $purchaseReturn->reason) == 'عيب في التصنيع' ? 'selected' : '' }}>
                                            عيب في التصنيع</option>
                                        <option value="أخرى"
                                            {{ old('reason', $purchaseReturn->reason) == 'أخرى' ? 'selected' : '' }}>أخرى
                                        </option>
                                    </select>
                                    @error('reason')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="row mb-4">
                                <div class="col-12">
                                    <label for="notes" class="form-label">ملاحظات</label>
                                    <textarea name="notes" id="notes" class="form-control" rows="3" placeholder="ملاحظات إضافية...">{{ old('notes', $purchaseReturn->notes) }}</textarea>
                                    @error('notes')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- Return Items -->
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title">أصناف المرتجع</h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-bordered" id="itemsTable">
                                            <thead>
                                                <tr>
                                                    <th>المنتج</th>
                                                    <th>الكمية الأصلية</th>
                                                    <th>الكمية المتاحة للإرجاع</th>
                                                    <th>الكمية المرتجعة</th>
                                                    <th>سعر التكلفة</th>
                                                    <th>الإجمالي</th>
                                                    <th>حالة المنتج</th>
                                                    <th>ملاحظات</th>
                                                    <th>إجراء</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach ($purchaseReturn->items as $index => $item)
                                                    <tr>
                                                        <td>{{ $item->product->name }}</td>
                                                        <td>{{ $item->original_quantity }}</td>
                                                        <td>{{ $item->available_quantity }}</td>
                                                        <td>
                                                            <input type="number"
                                                                name="items[{{ $index }}][quantity_returned]"
                                                                class="form-control quantity-input" step="0.01"
                                                                min="0.01" max="{{ $item->available_quantity }}"
                                                                value="{{ old('items.' . $index . '.quantity_returned', $item->quantity_returned) }}"
                                                                data-cost="{{ $item->cost_price }}" required>
                                                            <input type="hidden" name="items[{{ $index }}][id]"
                                                                value="{{ $item->id }}">
                                                            <input type="hidden"
                                                                name="items[{{ $index }}][purchase_item_id]"
                                                                value="{{ $item->purchase_item_id }}">
                                                        </td>
                                                        <td>{{ number_format($item->cost_price, 2) }}</td>
                                                        <td class="item-total">{{ number_format($item->total_cost, 2) }}
                                                        </td>
                                                        <td>
                                                            <select name="items[{{ $index }}][condition]"
                                                                class="form-select">
                                                                <option value="good"
                                                                    {{ old('items.' . $index . '.condition', $item->condition) == 'good' ? 'selected' : '' }}>
                                                                    جيد</option>
                                                                <option value="damaged"
                                                                    {{ old('items.' . $index . '.condition', $item->condition) == 'damaged' ? 'selected' : '' }}>
                                                                    تالف</option>
                                                                <option value="expired"
                                                                    {{ old('items.' . $index . '.condition', $item->condition) == 'expired' ? 'selected' : '' }}>
                                                                    منتهي الصلاحية</option>
                                                                <option value="defective"
                                                                    {{ old('items.' . $index . '.condition', $item->condition) == 'defective' ? 'selected' : '' }}>
                                                                    معيب</option>
                                                            </select>
                                                        </td>
                                                        <td>
                                                            <input type="text"
                                                                name="items[{{ $index }}][item_notes]"
                                                                class="form-control" placeholder="ملاحظات..."
                                                                value="{{ old('items.' . $index . '.item_notes', $item->item_notes) }}">
                                                        </td>
                                                        <td>
                                                            <button type="button"
                                                                class="btn btn-sm btn-danger remove-item">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </td>
                                                    </tr>
                                                @endforeach
                                            </tbody>
                                        </table>
                                    </div>

                                    <div class="row mt-3">
                                        <div class="col-md-6">
                                            <strong>إجمالي المبلغ المرتجع: <span
                                                    id="totalReturnAmount">{{ number_format($purchaseReturn->total_amount, 2) }}</span>
                                                ج.م</strong>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card-footer">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> حفظ التعديلات
                            </button>
                            <a href="{{ route('admin.purchase-returns.show', $purchaseReturn) }}"
                                class="btn btn-secondary">
                                <i class="fas fa-times"></i> إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
        <script>
            $(document).ready(function() {
                // Calculate item total when quantity changes
                $('.quantity-input').on('input', function() {
                    const row = $(this).closest('tr');
                    const quantity = parseFloat($(this).val()) || 0;
                    const costPrice = parseFloat($(this).data('cost')) || 0;
                    const total = quantity * costPrice;

                    row.find('.item-total').text(total.toFixed(2));
                    calculateTotal();
                });

                // Remove item
                $('.remove-item').click(function() {
                    if (confirm('هل أنت متأكد من حذف هذا الصنف؟')) {
                        $(this).closest('tr').remove();
                        calculateTotal();
                    }
                });

                function calculateTotal() {
                    let total = 0;
                    $('.item-total').each(function() {
                        const value = parseFloat($(this).text().replace(/,/g, '')) || 0;
                        total += value;
                    });

                    $('#totalReturnAmount').text(total.toLocaleString('en-US', {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2
                    }));
                    $('#refund_amount').val(total.toFixed(2));
                }

                // Form validation
                $('#editReturnForm').submit(function(e) {
                    const itemsCount = $('#itemsTable tbody tr').length;
                    if (itemsCount === 0) {
                        e.preventDefault();
                        alert('يجب إضافة صنف واحد على الأقل للمرتجع');
                        return false;
                    }

                    // Validate quantities
                    let isValid = true;
                    $('.quantity-input').each(function() {
                        const quantity = parseFloat($(this).val()) || 0;
                        const max = parseFloat($(this).attr('max')) || 0;

                        if (quantity <= 0) {
                            isValid = false;
                            $(this).addClass('is-invalid');
                        } else if (quantity > max) {
                            isValid = false;
                            $(this).addClass('is-invalid');
                            alert('الكمية المرتجعة تتجاوز الكمية المتاحة للإرجاع');
                        } else {
                            $(this).removeClass('is-invalid');
                        }
                    });

                    if (!isValid) {
                        e.preventDefault();
                        return false;
                    }
                });
            });
        </script>
    @endpush
@endsection
