<x-app-layout>

    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h3 class="card-title">تفاصيل مرتجع الشراء - {{ $purchaseReturn->return_number }}</h3>
                        <div class="card-tools">
                            @if ($purchaseReturn->canBeEdited())
                                <a href="{{ route('admin.purchase-returns.edit', $purchaseReturn) }}"
                                    class="btn btn-warning">
                                    <i class="fas fa-edit"></i> تعديل
                                </a>
                            @endif
                            @if ($purchaseReturn->canBeApproved())
                                <form method="POST"
                                    action="{{ route('admin.purchase-returns.approve', $purchaseReturn) }}"
                                    class="d-inline">
                                    @csrf
                                    <button type="submit" class="btn btn-info"
                                        onclick="return confirm('هل أنت متأكد من اعتماد هذا المرتجع؟')">
                                        <i class="fas fa-check"></i> اعتماد
                                    </button>
                                </form>
                            @endif
                            @if ($purchaseReturn->canBeCompleted())
                                <form method="POST"
                                    action="{{ route('admin.purchase-returns.complete', $purchaseReturn) }}"
                                    class="d-inline">
                                    @csrf
                                    <button type="submit" class="btn btn-success"
                                        onclick="return confirm('هل أنت متأكد من إتمام هذا المرتجع؟ سيتم تعديل المخزون والحسابات.')">
                                        <i class="fas fa-check-double"></i> إتمام
                                    </button>
                                </form>
                            @endif
                            @if ($purchaseReturn->canBeCancelled())
                                <button type="button" class="btn btn-danger" data-bs-toggle="modal"
                                    data-bs-target="#cancelModal">
                                    <i class="fas fa-times"></i> إلغاء
                                </button>
                            @endif
                            <a href="{{ route('admin.purchase-returns.index') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> العودة للقائمة
                            </a>
                        </div>
                    </div>

                    <div class="card-body">
                        <!-- Return Information -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title">معلومات المرتجع</h5>
                                    </div>
                                    <div class="card-body">
                                        <table class="table table-borderless">
                                            <tr>
                                                <td><strong>رقم المرتجع:</strong></td>
                                                <td>{{ $purchaseReturn->return_number }}</td>
                                            </tr>
                                            <tr>
                                                <td><strong>تاريخ المرتجع:</strong></td>
                                                <td>{{ $purchaseReturn->return_date->format('Y-m-d') }}</td>
                                            </tr>
                                            <tr>
                                                <td><strong>نوع المرتجع:</strong></td>
                                                <td>
                                                    @if ($purchaseReturn->return_type == 'full')
                                                        <span class="badge bg-primary">مرتجع كامل</span>
                                                    @else
                                                        <span class="badge bg-secondary">مرتجع جزئي</span>
                                                    @endif
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><strong>الحالة:</strong></td>
                                                <td>
                                                    @switch($purchaseReturn->status)
                                                        @case('pending')
                                                            <span class="badge bg-warning">في الانتظار</span>
                                                        @break

                                                        @case('approved')
                                                            <span class="badge bg-info">معتمد</span>
                                                        @break

                                                        @case('completed')
                                                            <span class="badge bg-success">مكتمل</span>
                                                        @break

                                                        @case('cancelled')
                                                            <span class="badge bg-danger">ملغي</span>
                                                        @break
                                                    @endswitch
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><strong>المبلغ الإجمالي:</strong></td>
                                                <td>{{ number_format($purchaseReturn->total_amount, 2) }} ج.م</td>
                                            </tr>
                                            <tr>
                                                <td><strong>مبلغ الاسترداد:</strong></td>
                                                <td>{{ number_format($purchaseReturn->refund_amount, 2) }} ج.م</td>
                                            </tr>
                                            <tr>
                                                <td><strong>المستخدم:</strong></td>
                                                <td>{{ $purchaseReturn->user->name }}</td>
                                            </tr>
                                            @if ($purchaseReturn->approved_by)
                                                <tr>
                                                    <td><strong>معتمد بواسطة:</strong></td>
                                                    <td>{{ $purchaseReturn->approvedBy->name }}</td>
                                                </tr>
                                                <tr>
                                                    <td><strong>تاريخ الاعتماد:</strong></td>
                                                    <td>{{ $purchaseReturn->approved_at->format('Y-m-d H:i') }}</td>
                                                </tr>
                                            @endif
                                        </table>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title">معلومات الشراء الأصلي</h5>
                                    </div>
                                    <div class="card-body">
                                        <table class="table table-borderless">
                                            <tr>
                                                <td><strong>رقم الشراء:</strong></td>
                                                <td>
                                                    <a href="{{ route('admin.purchases.show', $purchaseReturn->purchase) }}"
                                                        class="text-decoration-none">
                                                        {{ $purchaseReturn->purchase->invoice_number }}
                                                    </a>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><strong>المورد:</strong></td>
                                                <td>{{ $purchaseReturn->supplier->name }}</td>
                                            </tr>
                                            <tr>
                                                <td><strong>الفرع:</strong></td>
                                                <td>{{ $purchaseReturn->branch ? $purchaseReturn->branch->name : 'غير محدد' }}
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><strong>المخزن:</strong></td>
                                                <td>{{ $purchaseReturn->store ? $purchaseReturn->store->name : 'غير محدد' }}
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><strong>تاريخ الشراء:</strong></td>
                                                <td>{{ $purchaseReturn->purchase->purchase_date }}</td>
                                            </tr>
                                            <tr>
                                                <td><strong>إجمالي الشراء:</strong></td>
                                                <td>{{ number_format($purchaseReturn->purchase->total_amount, 2) }} ج.م
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Return Reason and Notes -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title">سبب المرتجع والملاحظات</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <strong>سبب المرتجع:</strong>
                                                <p>{{ $purchaseReturn->reason }}</p>
                                            </div>
                                            @if ($purchaseReturn->notes)
                                                <div class="col-md-6">
                                                    <strong>ملاحظات:</strong>
                                                    <p>{{ $purchaseReturn->notes }}</p>
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Return Items -->
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title">أصناف المرتجع</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>المنتج</th>
                                                <th>الكمية الأصلية</th>
                                                <th>الكمية المرتجعة</th>
                                                <th>سعر التكلفة</th>
                                                <th>الإجمالي</th>
                                                <th>حالة المنتج</th>
                                                <th>تم تعديل المخزون</th>
                                                <th>ملاحظات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach ($purchaseReturn->items as $item)
                                                <tr>
                                                    <td>{{ $item->product->name }}</td>
                                                    <td>{{ $item->original_quantity }}</td>
                                                    <td>{{ $item->quantity_returned }}</td>
                                                    <td>{{ number_format($item->cost_price, 2) }} ج.م</td>
                                                    <td>{{ number_format($item->total_cost, 2) }} ج.م</td>
                                                    <td>
                                                        @switch($item->condition)
                                                            @case('good')
                                                                <span class="badge bg-success">جيد</span>
                                                            @break

                                                            @case('damaged')
                                                                <span class="badge bg-danger">تالف</span>
                                                            @break

                                                            @case('expired')
                                                                <span class="badge bg-warning">منتهي الصلاحية</span>
                                                            @break

                                                            @case('defective')
                                                                <span class="badge bg-secondary">معيب</span>
                                                            @break
                                                        @endswitch
                                                    </td>
                                                    <td>
                                                        @if ($item->inventory_adjusted)
                                                            <span class="badge bg-success">نعم</span>
                                                        @else
                                                            <span class="badge bg-warning">لا</span>
                                                        @endif
                                                    </td>
                                                    <td>{{ $item->item_notes ?? '-' }}</td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                        <tfoot>
                                            <tr class="table-info">
                                                <th colspan="4">الإجمالي</th>
                                                <th>{{ number_format($purchaseReturn->total_amount, 2) }} ج.م</th>
                                                <th colspan="3"></th>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- Financial Transactions -->
                        @if ($purchaseReturn->transactions->count() > 0)
                            <div class="card mt-4">
                                <div class="card-header">
                                    <h5 class="card-title">المعاملات المالية</h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-striped">
                                            <thead>
                                                <tr>
                                                    <th>رقم المرجع</th>
                                                    <th>النوع</th>
                                                    <th>المبلغ</th>
                                                    <th>الرصيد قبل</th>
                                                    <th>الرصيد بعد</th>
                                                    <th>الوصف</th>
                                                    <th>التاريخ</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach ($purchaseReturn->transactions as $transaction)
                                                    <tr>
                                                        <td>{{ $transaction->reference_number }}</td>
                                                        <td>
                                                            @if ($transaction->type == 'debit')
                                                                <span class="badge bg-danger">خصم</span>
                                                            @else
                                                                <span class="badge bg-success">إيداع</span>
                                                            @endif
                                                        </td>
                                                        <td>{{ number_format($transaction->amount, 2) }} ج.م</td>
                                                        <td>{{ number_format($transaction->balance_before, 2) }} ج.م
                                                        </td>
                                                        <td>{{ number_format($transaction->balance_after, 2) }} ج.م
                                                        </td>
                                                        <td>{{ $transaction->description }}</td>
                                                        <td>{{ $transaction->created_at->format('Y-m-d H:i') }}</td>
                                                    </tr>
                                                @endforeach
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Cancel Modal -->
    @if ($purchaseReturn->canBeCancelled())
        <div class="modal fade" id="cancelModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <form method="POST" action="{{ route('admin.purchase-returns.cancel', $purchaseReturn) }}">
                        @csrf
                        <div class="modal-header">
                            <h5 class="modal-title">إلغاء مرتجع الشراء</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="mb-3">
                                <label for="cancellation_reason" class="form-label">سبب الإلغاء <span
                                        class="text-danger">*</span></label>
                                <textarea name="cancellation_reason" id="cancellation_reason" class="form-control" rows="3" required
                                    placeholder="اكتب سبب إلغاء المرتجع..."></textarea>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="submit" class="btn btn-danger">تأكيد الإلغاء</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    @endif
</x-app-layout>
