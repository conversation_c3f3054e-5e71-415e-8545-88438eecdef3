<?php

namespace App\Http\Controllers;

use App\Models\Supplier;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use App\Helpers\AlertHelper;

class SupplierController extends Controller
{
    public function index()
    {
        $suppliers = Supplier::latest()->paginate(10);
        return view('suppliers.index', compact('suppliers'));
    }

    public function create()
    {
        return view('suppliers.create');
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'nullable|string|max:50|unique:suppliers',
            'contact_person' => 'nullable|string|max:255',
            'phone' => 'nullable|string|max:20',
            'email' => 'nullable|email|max:255',
            'address' => 'nullable|string',
            'opening_balance' => 'nullable|numeric|min:0',
        ]);

        DB::beginTransaction();
        try {

            $supplier = Supplier::create($validated);
            $supplier->code = 'SUP-' . str_pad($supplier->id, 6, '0', STR_PAD_LEFT);
            $supplier->save();

            // Create account for the supplier
            $account = $supplier->account()->create([
                'name' => $supplier->name,
                'code' => $supplier->code,
                'type' => 'supplier',
                'opening_balance' => $supplier->opening_balance ?? 0,
                'current_balance' => $supplier->opening_balance ?? 0,
            ]);

            DB::commit();
            AlertHelper::success('تم إضافة المورد بنجاح');
            return redirect()->route('admin.suppliers.index');
        } catch (\Exception $e) {
            dd($e->getMessage());
            DB::rollBack();
            AlertHelper::error('حدث خطأ أثناء إضافة المورد');
            return back()->withInput();
        }
    }

    public function show(Supplier $supplier)
    {
        $supplier->load(['purchases' => function ($query) {
            $query->with(['branch', 'items.product'])->latest();
        }]);
        return view('suppliers.show', compact('supplier'));
    }

    public function edit(Supplier $supplier)
    {
        return view('suppliers.edit', compact('supplier'));
    }

    public function update(Request $request, Supplier $supplier)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'contact_person' => 'nullable|string|max:255',
            'phone' => 'nullable|string|max:20',
            'email' => 'nullable|email|max:255',
            'address' => 'nullable|string',
            'opening_balance' => 'nullable|numeric|min:0',
            'is_active' => 'boolean',
        ]);

        $supplier->update($validated);

        return redirect()->route('admin.suppliers.index')
            ->with('success', 'تم تحديث بيانات المورد بنجاح');
    }

    public function destroy(Supplier $supplier)
    {
        $supplier->delete();

        return redirect()->route('admin.suppliers.index')
            ->with('success', 'تم حذف المورد بنجاح');
    }
}
