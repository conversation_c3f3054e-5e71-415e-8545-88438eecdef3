<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Branch;
use App\Models\Store;
use App\Models\User;
use App\Models\Sale;
use App\Models\Purchase;

use App\Services\FinancialService;

class AdminDashboardController extends Controller
{
    protected $financialService;

    public function __construct(FinancialService $financialService)
    {
        $this->financialService = $financialService;
    }

    /**
     * Display the admin dashboard
     */
    public function index(Request $request)
    {
        $user = $request->user();

        // Calculate returns for today
        $todayReturns = \App\Models\SaleReturn::whereDate('created_at', today())
            ->where('status', 'completed')
            ->sum('total_amount');

        // Overall statistics
        $stats = [
            'total_branches' => Branch::count(),
            'total_stores' => Store::count(),
            'independent_stores' => Store::independent()->count(),
            'branch_stores' => Store::whereNotNull('branch_id')->count(),
            'total_users' => User::count(),
            'total_sales_today' => Sale::whereDate('created_at', today())->sum('total_amount') - $todayReturns,
            'total_purchases_today' => Purchase::whereDate('created_at', today())->sum('total_amount'),
        ];

        // Financial summary
        $financialSummary = $this->financialService->getOverallFinancialSummary();

        // Recent activities
        $recentSales = Sale::with(['customer', 'branch', 'store'])
            ->latest()
            ->limit(10)
            ->get();

        $recentPurchases = Purchase::with(['supplier', 'branch'])
            ->latest()
            ->limit(10)
            ->get();

        // Branch statistics and information
        $branchStats = [
            'total_branches' => Branch::where('is_active', true)->count(),
            'branches_with_sales_today' => Branch::whereHas('sales', function ($q) {
                $q->whereDate('created_at', today());
            })->count(),
            'branches_with_stores' => Branch::whereHas('stores')->count(),
            'total_branch_users' => User::whereNotNull('branch_id')->count(),
            'today_direct_transfers' => \App\Models\InventoryTransfer::where('status', 'completed')
                ->whereDate('received_at', today())->count(),
        ];

        // Top performing branches
        $topBranches = Branch::with([
            'sales' => function ($q) {
                $q->whereMonth('created_at', now()->month);
            },
            'saleReturns' => function ($q) {
                $q->whereMonth('created_at', now()->month)
                    ->where('status', 'completed');
            }
        ])
            ->where('is_active', true)
            ->get()
            ->map(function ($branch) {
                $monthlySales = $branch->sales->sum('total_amount');
                $monthlyReturns = $branch->saleReturns->sum('total_amount');

                return [
                    'id' => $branch->id,
                    'name' => $branch->name,
                    'monthly_sales' => $monthlySales - $monthlyReturns,
                    'sales_count' => $branch->sales->count(),
                    'stores_count' => $branch->stores()->count(),
                    'users_count' => $branch->users()->count(),
                ];
            })
            ->sortByDesc('monthly_sales')
            ->take(5)
            ->values();

        // Outstanding payments summary
        $paymentStats = [
            'total_outstanding' => Purchase::where('remaining_amount', '>', 0)->sum('remaining_amount'),
            'overdue_purchases' => Purchase::where('remaining_amount', '>', 0)
                ->where('purchase_date', '<', now()->subDays(30))->count(),
            'suppliers_with_debt' => Purchase::where('remaining_amount', '>', 0)
                ->distinct('supplier_id')->count('supplier_id'),
        ];

        // Top customers with outstanding balances
        $topCustomersOwing = $this->financialService->getCustomersWithOutstandingBalances($user, 5);

        // Top suppliers we owe
        $topSuppliersWeOwe = $this->financialService->getSuppliersWeOwe($user, 5);

        return view('admin.dashboard', compact(
            'stats',
            'financialSummary',
            'recentSales',
            'recentPurchases',
            'branchStats',
            'topBranches',
            'paymentStats',
            'topCustomersOwing',
            'topSuppliersWeOwe'
        ));
    }



    /**
     * Branch analytics dashboard
     */
    public function branchAnalytics(Request $request)
    {
        // Get date filters
        $dateFrom = $request->get('date_from', now()->startOfMonth()->format('Y-m-d'));
        $dateTo = $request->get('date_to', now()->endOfMonth()->format('Y-m-d'));
        $period = $request->get('period', 'current_month'); // current_month, last_month, last_3_months, last_6_months, custom

        // Set date range based on period
        switch ($period) {
            case 'last_month':
                $dateFrom = now()->subMonth()->startOfMonth()->format('Y-m-d');
                $dateTo = now()->subMonth()->endOfMonth()->format('Y-m-d');
                break;
            case 'last_3_months':
                $dateFrom = now()->subMonths(3)->startOfMonth()->format('Y-m-d');
                $dateTo = now()->endOfMonth()->format('Y-m-d');
                break;
            case 'last_6_months':
                $dateFrom = now()->subMonths(6)->startOfMonth()->format('Y-m-d');
                $dateTo = now()->endOfMonth()->format('Y-m-d');
                break;
            case 'current_year':
                $dateFrom = now()->startOfYear()->format('Y-m-d');
                $dateTo = now()->endOfYear()->format('Y-m-d');
                break;
            case 'last_year':
                $dateFrom = now()->subYear()->startOfYear()->format('Y-m-d');
                $dateTo = now()->subYear()->endOfYear()->format('Y-m-d');
                break;
            case 'custom':
                // Use provided dates
                break;
            default: // current_month
                $dateFrom = now()->startOfMonth()->format('Y-m-d');
                $dateTo = now()->endOfMonth()->format('Y-m-d');
        }

        $branches = Branch::with([
            'sales' => function ($query) use ($dateFrom, $dateTo) {
                $query->whereBetween('created_at', [$dateFrom, $dateTo]);
            },
            'purchases' => function ($query) use ($dateFrom, $dateTo) {
                $query->whereBetween('created_at', [$dateFrom, $dateTo]);
            },
            'expenses' => function ($query) use ($dateFrom, $dateTo) {
                $query->whereBetween('created_at', [$dateFrom, $dateTo]);
            },
            'stores',
            'users'
        ])->where('is_active', true)->get();

        // Calculate filtered statistics
        $totalSales = $branches->sum(function ($branch) {
            return $branch->sales->sum('total_amount');
        });

        $totalPurchases = $branches->sum(function ($branch) {
            return $branch->purchases->sum('total_amount');
        });

        $totalExpenses = $branches->sum(function ($branch) {
            return $branch->expenses->sum('amount');
        });

        $totalProfit = $totalSales - $totalPurchases - $totalExpenses;

        // Get branch performance data
        $branchPerformance = $branches->map(function ($branch) {
            $sales = $branch->sales->sum('total_amount');
            $purchases = $branch->purchases->sum('total_amount');
            $expenses = $branch->expenses->sum('amount');

            return [
                'id' => $branch->id,
                'name' => $branch->name,
                'sales' => $sales,
                'purchases' => $purchases,
                'expenses' => $expenses,
                'profit' => $sales - $purchases - $expenses,
                'sales_count' => $branch->sales->count(),
                'avg_sale_amount' => $branch->sales->count() > 0 ? $sales / $branch->sales->count() : 0,
                'stores_count' => $branch->stores->count(),
                'users_count' => $branch->users->count(),
            ];
        });

        // Sort branches by performance
        $topBranchesBySales = $branchPerformance->sortByDesc('sales');
        $topBranchesByProfit = $branchPerformance->sortByDesc('profit');

        // Get monthly trends for the last 12 months
        $monthlyTrends = $this->getMonthlyTrends($dateFrom, $dateTo);

        // Get comparison with previous period
        $previousPeriodComparison = $this->getPreviousPeriodComparison($dateFrom, $dateTo);

        // Calculate current month data for backward compatibility
        $currentMonthStart = now()->startOfMonth()->format('Y-m-d');
        $currentMonthEnd = now()->endOfMonth()->format('Y-m-d');

        $monthSales = \App\Models\Sale::whereBetween('created_at', [$currentMonthStart, $currentMonthEnd])
            ->sum('total_amount');
        $monthPurchases = \App\Models\Purchase::whereBetween('created_at', [$currentMonthStart, $currentMonthEnd])
            ->sum('total_amount');

        return view('admin.branches.analytics', compact(
            'branches',
            'branchPerformance',
            'totalSales',
            'totalPurchases',
            'totalExpenses',
            'totalProfit',
            'topBranchesBySales',
            'topBranchesByProfit',
            'monthlyTrends',
            'previousPeriodComparison',
            'monthSales',
            'monthPurchases',
            'dateFrom',
            'dateTo',
            'period'
        ));
    }

    /**
     * Get monthly trends data
     */
    private function getMonthlyTrends($dateFrom, $dateTo)
    {
        $trends = [];
        $startDate = \Carbon\Carbon::parse($dateFrom)->startOfMonth();
        $endDate = \Carbon\Carbon::parse($dateTo)->endOfMonth();

        while ($startDate <= $endDate) {
            $monthStart = $startDate->copy()->startOfMonth();
            $monthEnd = $startDate->copy()->endOfMonth();

            $salesData = \App\Models\Sale::whereBetween('created_at', [$monthStart, $monthEnd])
                ->selectRaw('branch_id, SUM(total_amount) as total_sales, COUNT(*) as sales_count')
                ->groupBy('branch_id')
                ->get()
                ->keyBy('branch_id');

            $purchasesData = \App\Models\Purchase::whereBetween('created_at', [$monthStart, $monthEnd])
                ->selectRaw('branch_id, SUM(total_amount) as total_purchases')
                ->groupBy('branch_id')
                ->get()
                ->keyBy('branch_id');

            $trends[] = [
                'month' => $startDate->format('Y-m'),
                'month_name' => $startDate->format('M Y'),
                'sales_data' => $salesData,
                'purchases_data' => $purchasesData,
                'total_sales' => $salesData->sum('total_sales'),
                'total_purchases' => $purchasesData->sum('total_purchases'),
            ];

            $startDate->addMonth();
        }

        return collect($trends);
    }

    /**
     * Get comparison with previous period
     */
    private function getPreviousPeriodComparison($dateFrom, $dateTo)
    {
        $currentStart = \Carbon\Carbon::parse($dateFrom);
        $currentEnd = \Carbon\Carbon::parse($dateTo);
        $daysDiff = $currentStart->diffInDays($currentEnd) + 1;

        $previousStart = $currentStart->copy()->subDays($daysDiff);
        $previousEnd = $currentEnd->copy()->subDays($daysDiff);

        $currentSales = \App\Models\Sale::whereBetween('created_at', [$currentStart, $currentEnd])->sum('total_amount');
        $previousSales = \App\Models\Sale::whereBetween('created_at', [$previousStart, $previousEnd])->sum('total_amount');

        $currentPurchases = \App\Models\Purchase::whereBetween('created_at', [$currentStart, $currentEnd])->sum('total_amount');
        $previousPurchases = \App\Models\Purchase::whereBetween('created_at', [$previousStart, $previousEnd])->sum('total_amount');

        return [
            'sales_change' => $previousSales > 0 ? (($currentSales - $previousSales) / $previousSales) * 100 : 0,
            'purchases_change' => $previousPurchases > 0 ? (($currentPurchases - $previousPurchases) / $previousPurchases) * 100 : 0,
            'current_sales' => $currentSales,
            'previous_sales' => $previousSales,
            'current_purchases' => $currentPurchases,
            'previous_purchases' => $previousPurchases,
        ];
    }

    /**
     * Get analytics data for AJAX requests
     */
    public function getAnalyticsData(Request $request)
    {
        $dateFrom = $request->get('date_from', now()->startOfMonth()->format('Y-m-d'));
        $dateTo = $request->get('date_to', now()->endOfMonth()->format('Y-m-d'));
        $period = $request->get('period', 'current_month');

        // Set date range based on period (same logic as branchAnalytics)
        switch ($period) {
            case 'last_month':
                $dateFrom = now()->subMonth()->startOfMonth()->format('Y-m-d');
                $dateTo = now()->subMonth()->endOfMonth()->format('Y-m-d');
                break;
            case 'last_3_months':
                $dateFrom = now()->subMonths(3)->startOfMonth()->format('Y-m-d');
                $dateTo = now()->endOfMonth()->format('Y-m-d');
                break;
            case 'last_6_months':
                $dateFrom = now()->subMonths(6)->startOfMonth()->format('Y-m-d');
                $dateTo = now()->endOfMonth()->format('Y-m-d');
                break;
            case 'current_year':
                $dateFrom = now()->startOfYear()->format('Y-m-d');
                $dateTo = now()->endOfYear()->format('Y-m-d');
                break;
            case 'last_year':
                $dateFrom = now()->subYear()->startOfYear()->format('Y-m-d');
                $dateTo = now()->subYear()->endOfYear()->format('Y-m-d');
                break;
        }

        $branches = Branch::with([
            'sales' => function ($query) use ($dateFrom, $dateTo) {
                $query->whereBetween('created_at', [$dateFrom, $dateTo]);
            },
            'purchases' => function ($query) use ($dateFrom, $dateTo) {
                $query->whereBetween('created_at', [$dateFrom, $dateTo]);
            },
            'expenses' => function ($query) use ($dateFrom, $dateTo) {
                $query->whereBetween('created_at', [$dateFrom, $dateTo]);
            }
        ])->where('is_active', true)->get();

        $totalSales = $branches->sum(function ($branch) {
            return $branch->sales->sum('total_amount');
        });

        $totalPurchases = $branches->sum(function ($branch) {
            return $branch->purchases->sum('total_amount');
        });

        $totalExpenses = $branches->sum(function ($branch) {
            return $branch->expenses->sum('amount');
        });

        $previousPeriodComparison = $this->getPreviousPeriodComparison($dateFrom, $dateTo);

        return response()->json([
            'total_sales' => $totalSales,
            'total_purchases' => $totalPurchases,
            'total_expenses' => $totalExpenses,
            'total_profit' => $totalSales - $totalPurchases - $totalExpenses,
            'comparison' => $previousPeriodComparison,
            'date_from' => $dateFrom,
            'date_to' => $dateTo,
        ]);
    }

    /**
     * Get monthly trends data for charts
     */
    public function getMonthlyTrendsData(Request $request)
    {
        $dateFrom = $request->get('date_from', now()->subMonths(11)->startOfMonth()->format('Y-m-d'));
        $dateTo = $request->get('date_to', now()->endOfMonth()->format('Y-m-d'));

        $monthlyTrends = $this->getMonthlyTrends($dateFrom, $dateTo);

        return response()->json([
            'trends' => $monthlyTrends,
            'chart_data' => [
                'labels' => $monthlyTrends->pluck('month_name'),
                'sales' => $monthlyTrends->pluck('total_sales'),
                'purchases' => $monthlyTrends->pluck('total_purchases'),
                'profit' => $monthlyTrends->map(function ($trend) {
                    return $trend['total_sales'] - $trend['total_purchases'];
                })
            ]
        ]);
    }

    /**
     * Get branch comparison data for charts
     */
    public function getBranchComparisonData(Request $request)
    {
        $dateFrom = $request->get('date_from', now()->startOfMonth()->format('Y-m-d'));
        $dateTo = $request->get('date_to', now()->endOfMonth()->format('Y-m-d'));

        $branches = Branch::with([
            'sales' => function ($query) use ($dateFrom, $dateTo) {
                $query->whereBetween('created_at', [$dateFrom, $dateTo]);
            },
            'purchases' => function ($query) use ($dateFrom, $dateTo) {
                $query->whereBetween('created_at', [$dateFrom, $dateTo]);
            },
            'expenses' => function ($query) use ($dateFrom, $dateTo) {
                $query->whereBetween('created_at', [$dateFrom, $dateTo]);
            }
        ])->where('is_active', true)->get();

        $branchData = $branches->map(function ($branch) {
            $sales = $branch->sales->sum('total_amount');
            $purchases = $branch->purchases->sum('total_amount');
            $expenses = $branch->expenses->sum('amount');

            return [
                'name' => $branch->name,
                'sales' => $sales,
                'purchases' => $purchases,
                'expenses' => $expenses,
                'profit' => $sales - $purchases - $expenses,
                'sales_count' => $branch->sales->count(),
            ];
        });

        return response()->json([
            'branches' => $branchData,
            'chart_data' => [
                'labels' => $branchData->pluck('name'),
                'sales' => $branchData->pluck('sales'),
                'purchases' => $branchData->pluck('purchases'),
                'profit' => $branchData->pluck('profit'),
            ]
        ]);
    }

    /**
     * Store management page
     */
    public function stores()
    {
        $stores = Store::with(['branch', 'users', 'inventory'])->get();
        return view('admin.stores', compact('stores'));
    }



    /**
     * Financial reports page
     */
    public function financialReports(Request $request)
    {
        $user = $request->user();

        // Get branch filter
        $branchId = $request->get('branch_id');
        $branch = $branchId ? Branch::find($branchId) : null;

        if ($branch) {
            $financialSummary = $this->financialService->getBranchFinancialSummary($branch);
        } else {
            $financialSummary = $this->financialService->getOverallFinancialSummary();
        }

        $customerSummary = $this->financialService->getCustomerFinancialSummary($user);
        $supplierSummary = $this->financialService->getSupplierFinancialSummary($user);

        $branches = Branch::all();

        return view('admin.financial-reports', compact(
            'financialSummary',
            'customerSummary',
            'supplierSummary',
            'branches',
            'branch'
        ));
    }
}
