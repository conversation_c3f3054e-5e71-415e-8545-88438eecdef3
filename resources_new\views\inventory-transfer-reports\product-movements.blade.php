@extends('layouts.app')

@section('title', 'تقرير حركة المنتجات')

@section('content')
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-0 text-gray-800">تقرير حركة المنتجات</h1>
                <p class="text-muted mb-0">تتبع تفصيلي لحركة المنتجات بين المواقع</p>
            </div>
            <div>
                <a href="{{ user_route('inventory-transfer-reports.index') }}" class="btn btn-outline-secondary me-2">
                    <i class="fas fa-arrow-right me-2"></i>العودة للتقارير
                </a>
                <a href="{{ user_route('inventory-transfer-reports.export', array_merge(['type' => 'movements'], request()->query())) }}"
                    class="btn btn-success">
                    <i class="fas fa-file-csv me-2"></i>تصدير
                </a>
            </div>
        </div>

        <!-- Filters -->
        <div class="card shadow mb-4">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">من تاريخ</label>
                        <input type="date" name="date_from" class="form-control" value="{{ $dateFrom }}">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">إلى تاريخ</label>
                        <input type="date" name="date_to" class="form-control" value="{{ $dateTo }}">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">المنتج</label>
                        <select name="product_id" class="form-select">
                            <option value="">جميع المنتجات</option>
                            @foreach ($products as $product)
                                <option value="{{ $product->id }}" {{ $productId == $product->id ? 'selected' : '' }}>
                                    {{ $product->name }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <button type="submit" class="btn btn-primary d-block w-100">
                            <i class="fas fa-search me-2"></i>بحث
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Product Summary -->
        @if ($productSummary->count() > 0)
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">ملخص حركة المنتجات</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        @foreach ($productSummary->take(6) as $summary)
                            <div class="col-lg-4 col-md-6 mb-3">
                                <div class="card border-left-success h-100">
                                    <div class="card-body py-3">
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-circle bg-success text-white me-3">
                                                {{ substr($summary->name, 0, 1) }}
                                            </div>
                                            <div class="flex-grow-1">
                                                <div class="fw-bold text-truncate">{{ $summary->name }}</div>
                                                <div class="text-success fw-bold">{{ $summary->total_moved }} قطعة</div>
                                                <small class="text-muted">{{ $summary->transfer_count }} عملية نقل</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        @endif

        <!-- Product Movements Table -->
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">تفاصيل حركة المنتجات</h6>
            </div>
            <div class="card-body">
                @if ($movements->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th class="border-0 fw-bold">المنتج</th>
                                    <th class="border-0 fw-bold">الكمية</th>
                                    <th class="border-0 fw-bold">من موقع</th>
                                    <th class="border-0 fw-bold">إلى موقع</th>
                                    <th class="border-0 fw-bold">الحالة</th>
                                    <th class="border-0 fw-bold">تاريخ النقل</th>
                                    <th class="border-0 fw-bold text-center">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($movements as $movement)
                                    <tr class="border-bottom">
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-circle bg-primary text-white me-2">
                                                    {{ substr($movement->product_name, 0, 1) }}
                                                </div>
                                                <div>
                                                    <div class="fw-bold">{{ $movement->product_name }}</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="fw-bold text-success fs-6">{{ $movement->quantity }}</span>
                                            <small class="text-muted d-block">قطعة</small>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-circle bg-info text-white me-2">
                                                    {{ substr($movement->from_store_name ?: $movement->from_branch_name, 0, 1) }}
                                                </div>
                                                <div>
                                                    <div class="fw-bold">
                                                        {{ $movement->from_store_name ?: $movement->from_branch_name }}
                                                    </div>
                                                    <small
                                                        class="text-muted">{{ $movement->from_store_name ? 'مخزن' : 'فرع' }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-circle bg-success text-white me-2">
                                                    {{ substr($movement->to_store_name ?: $movement->to_branch_name, 0, 1) }}
                                                </div>
                                                <div>
                                                    <div class="fw-bold">
                                                        {{ $movement->to_store_name ?: $movement->to_branch_name }}</div>
                                                    <small
                                                        class="text-muted">{{ $movement->to_store_name ? 'مخزن' : 'فرع' }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            @php
                                                $statusColors = [
                                                    'pending' => 'warning',
                                                    'approved' => 'info',
                                                    'shipped' => 'secondary',
                                                    'received' => 'success',
                                                    'cancelled' => 'danger',
                                                ];
                                                $statusLabels = [
                                                    'pending' => 'قيد الانتظار',
                                                    'approved' => 'موافق عليه',
                                                    'shipped' => 'تم الشحن',
                                                    'received' => 'تم الاستلام',
                                                    'cancelled' => 'ملغي',
                                                ];
                                            @endphp
                                            <span class="badge bg-{{ $statusColors[$movement->status] ?? 'secondary' }}">
                                                {{ $statusLabels[$movement->status] ?? $movement->status }}
                                            </span>
                                        </td>
                                        <td>
                                            <div class="fw-bold">
                                                {{ \Carbon\Carbon::parse($movement->created_at)->format('Y-m-d') }}</div>
                                            <small
                                                class="text-muted">{{ \Carbon\Carbon::parse($movement->created_at)->format('H:i') }}</small>
                                            <div class="small text-muted">
                                                {{ \Carbon\Carbon::parse($movement->created_at)->diffForHumans() }}</div>
                                        </td>
                                        <td class="text-center">
                                            <a href="{{ route(user_route('inventory-transfers.show'), $movement->transfer_id) }}"
                                                class="btn btn-outline-info btn-sm" title="عرض عملية النقل">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-center mt-4">
                        {{ $movements->appends(request()->query())->links() }}
                    </div>
                @else
                    <div class="text-center py-5">
                        <i class="fas fa-box fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد حركة للمنتجات</h5>
                        <p class="text-muted">لم يتم العثور على حركة للمنتجات في الفترة المحددة</p>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <style>
        .avatar-circle {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 12px;
        }

        .border-left-success {
            border-left: 0.25rem solid #1cc88a !important;
        }

        .table-hover tbody tr:hover {
            background-color: rgba(0, 0, 0, 0.02);
        }
    </style>
@endsection
