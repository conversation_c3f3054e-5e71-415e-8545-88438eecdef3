<x-app-layout>
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h4 class="mb-1">مخزون المبيعات - {{ $branch->name }}</h4>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">الرئيسية</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.branches.index') }}">الفروع</a></li>
                        <li class="breadcrumb-item"><a
                                href="{{ route('admin.branches.show', $branch) }}">{{ $branch->name }}</a></li>
                        <li class="breadcrumb-item active">مخزون المبيعات</li>
                    </ol>
                </nav>
            </div>
            <div class="d-flex gap-2">
                <a href="{{ user_route('branches.add-product', $branch) }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> إضافة منتجات
                </a>
                <div class="btn-group">
                    <a href="{{ route('admin.transfers.direct.create') }}?source_type=branch&source_id={{ $branch->id }}"
                        class="btn btn-success">
                        <i class="fas fa-arrow-right"></i> نقل من الفرع
                    </a>
                    <a href="{{ route('admin.transfers.direct.create') }}?destination_type=branch&destination_id={{ $branch->id }}"
                        class="btn btn-outline-success">
                        <i class="fas fa-arrow-left"></i> نقل إلى الفرع
                    </a>
                </div>
                {{-- <a href="{{ route('admin.branches.receive-products', $branch) }}" class="btn btn-success">
                    <i class="fas fa-truck"></i> استلام منتجات
                </a> --}}
                <a href="{{ route('admin.branches.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right"></i> عودة
                </a>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">إجمالي المنتجات</h6>
                                <h3 class="mb-0">{{ number_format($stats['total_products']) }}</h3>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-boxes fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">مخزون منخفض</h6>
                                <h3 class="mb-0">{{ number_format($stats['low_stock_count']) }}</h3>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-exclamation-triangle fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-danger text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">نفد المخزون</h6>
                                <h3 class="mb-0">{{ number_format($stats['out_of_stock_count']) }}</h3>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-times-circle fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">قيمة المخزون</h6>
                                <h3 class="mb-0">{{ number_format($stats['total_value'], 2) }}</h3>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-dollar-sign fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters and Search -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET" action="{{ route('admin.branches.sales-inventory', $branch) }}">
                    <div class="row g-3">
                        <div class="col-md-4">
                            <label for="search" class="form-label">البحث</label>
                            <input type="text" class="form-control" id="search" name="search"
                                value="{{ request('search') }}" placeholder="اسم المنتج أو الباركود">
                        </div>
                        <div class="col-md-3">
                            <label for="stock_status" class="form-label">حالة المخزون</label>
                            <select class="form-select" id="stock_status" name="stock_status">
                                <option value="">جميع الحالات</option>
                                <option value="available"
                                    {{ request('stock_status') == 'available' ? 'selected' : '' }}>متوفر</option>
                                <option value="low" {{ request('stock_status') == 'low' ? 'selected' : '' }}>مخزون
                                    منخفض</option>
                                <option value="out" {{ request('stock_status') == 'out' ? 'selected' : '' }}>نفد
                                    المخزون</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="category_id" class="form-label">الفئة</label>
                            <select class="form-select" id="category_id" name="category_id">
                                <option value="">جميع الفئات</option>
                                @foreach ($categories as $category)
                                    @if ($category)
                                        <option value="{{ $category->id }}"
                                            {{ request('category_id') == $category->id ? 'selected' : '' }}>
                                            {{ $category->name }}
                                        </option>
                                    @endif
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i> بحث
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Inventory Table -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">قائمة المنتجات</h6>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>المنتج</th>
                                <th>الفئة</th>
                                <th>الكمية المتاحة</th>
                                <th>الحد الأدنى</th>
                                <th>حالة المخزون</th>
                                <th>سعر التكلفة</th>
                                <th>أسعار البيع</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($inventory as $item)
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div>
                                                <h6 class="mb-0">{{ $item->product->name }}</h6>
                                                <small class="text-muted">{{ $item->product->barcode }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-light text-dark">
                                            {{ $item->product->category->name ?? 'غير محدد' }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="fw-bold">{{ number_format($item->quantity) }}</span>
                                    </td>
                                    <td>
                                        <span
                                            class="text-muted">{{ number_format($item->minimum_stock ?? 10) }}</span>
                                    </td>
                                    <td>
                                        @if ($item->quantity <= 0)
                                            <span class="badge bg-danger">نفد المخزون</span>
                                        @elseif($item->quantity <= ($item->minimum_stock ?? 10))
                                            <span class="badge bg-warning">مخزون منخفض</span>
                                        @else
                                            <span class="badge bg-success">متوفر</span>
                                        @endif
                                    </td>
                                    <td>{{ number_format($item->cost_price, 2) }}</td>
                                    <td>
                                        <div class="small">
                                            <div>سعر 1: {{ number_format($item->sale_price_1, 2) }}</div>
                                            @if ($item->sale_price_2)
                                                <div>سعر 2: {{ number_format($item->sale_price_2, 2) }}</div>
                                            @endif
                                            @if ($item->sale_price_3)
                                                <div>سعر 3: {{ number_format($item->sale_price_3, 2) }}</div>
                                            @endif
                                        </div>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-sm btn-outline-primary"
                                                data-bs-toggle="modal"
                                                data-bs-target="#editPricesModal{{ $item->id }}">
                                                <i class="fas fa-dollar-sign"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-info"
                                                data-bs-toggle="modal"
                                                data-bs-target="#editMinStockModal{{ $item->id }}"
                                                title="تعديل الحد الأدنى">
                                                <i class="fas fa-chart-line"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-warning"
                                                data-bs-toggle="modal"
                                                data-bs-target="#adjustModal{{ $item->id }}">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>

                                <!-- Edit Prices Modal -->
                                <div class="modal fade" id="editPricesModal{{ $item->id }}" tabindex="-1">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <form method="POST"
                                                action="{{ route('admin.branches.update-sales-prices', [$branch, $item]) }}">
                                                @csrf
                                                @method('PUT')
                                                <div class="modal-header">
                                                    <h5 class="modal-title">تحديث أسعار البيع -
                                                        {{ $item->product->name }}</h5>
                                                    <button type="button" class="btn-close"
                                                        data-bs-dismiss="modal"></button>
                                                </div>
                                                <div class="modal-body">
                                                    <div class="mb-3">
                                                        <label class="form-label">سعر البيع الأول <span
                                                                class="text-danger">*</span></label>
                                                        <input type="number" step="0.01" class="form-control"
                                                            name="sale_price_1" value="{{ $item->sale_price_1 }}"
                                                            required>
                                                    </div>
                                                    <div class="mb-3">
                                                        <label class="form-label">سعر البيع الثاني</label>
                                                        <input type="number" step="0.01" class="form-control"
                                                            name="sale_price_2" value="{{ $item->sale_price_2 }}">
                                                    </div>
                                                    <div class="mb-3">
                                                        <label class="form-label">سعر البيع الثالث</label>
                                                        <input type="number" step="0.01" class="form-control"
                                                            name="sale_price_3" value="{{ $item->sale_price_3 }}">
                                                    </div>
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary"
                                                        data-bs-dismiss="modal">إلغاء</button>
                                                    <button type="submit" class="btn btn-primary">حفظ
                                                        التغييرات</button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>

                                <!-- Edit Minimum Stock Modal -->
                                <div class="modal fade" id="editMinStockModal{{ $item->id }}" tabindex="-1">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <form method="POST"
                                                action="{{ route('admin.branches.update-min-stock', [$branch, $item]) }}">
                                                @csrf
                                                @method('PUT')
                                                <div class="modal-header">
                                                    <h5 class="modal-title">تحديث الحد الأدنى -
                                                        {{ $item->product->name }}</h5>
                                                    <button type="button" class="btn-close"
                                                        data-bs-dismiss="modal"></button>
                                                </div>
                                                <div class="modal-body">
                                                    <div class="alert alert-info">
                                                        <i class="fas fa-info-circle"></i>
                                                        الحد الأدنى الحالي:
                                                        <strong>{{ number_format($item->minimum_stock ?? 10) }}</strong>
                                                    </div>
                                                    <div class="mb-3">
                                                        <label class="form-label">الحد الأدنى الجديد <span
                                                                class="text-danger">*</span></label>
                                                        <input type="number" step="0.01" class="form-control"
                                                            name="minimum_stock"
                                                            value="{{ $item->minimum_stock ?? 10 }}" min="0"
                                                            required>
                                                        <div class="form-text">سيتم تنبيهك عندما تصل الكمية إلى هذا
                                                            الحد أو أقل</div>
                                                    </div>
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary"
                                                        data-bs-dismiss="modal">إلغاء</button>
                                                    <button type="submit" class="btn btn-info">حفظ
                                                        التغييرات</button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>

                                <!-- Adjust Inventory Modal -->
                                <div class="modal fade" id="adjustModal{{ $item->id }}" tabindex="-1">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <form method="POST"
                                                action="{{ route('admin.branches.adjust-inventory', [$branch, $item]) }}">
                                                @csrf
                                                <div class="modal-header">
                                                    <h5 class="modal-title">تعديل المخزون - {{ $item->product->name }}
                                                    </h5>
                                                    <button type="button" class="btn-close"
                                                        data-bs-dismiss="modal"></button>
                                                </div>
                                                <div class="modal-body">
                                                    <div class="alert alert-info">
                                                        الكمية الحالية:
                                                        <strong>{{ number_format($item->quantity) }}</strong>
                                                    </div>
                                                    <div class="mb-3">
                                                        <label class="form-label">نوع التعديل</label>
                                                        <select class="form-select" name="adjustment_type" required>
                                                            <option value="">اختر نوع التعديل</option>
                                                            <option value="add">إضافة كمية</option>
                                                            <option value="subtract">خصم كمية</option>
                                                        </select>
                                                    </div>
                                                    <div class="mb-3">
                                                        <label class="form-label">الكمية</label>
                                                        <input type="number" step="0.01" class="form-control"
                                                            name="quantity" min="0.01" required>
                                                    </div>
                                                    <div class="mb-3">
                                                        <label class="form-label">السبب</label>
                                                        <textarea class="form-control" name="reason" rows="3" required placeholder="اذكر سبب التعديل..."></textarea>
                                                    </div>
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary"
                                                        data-bs-dismiss="modal">إلغاء</button>
                                                    <button type="submit" class="btn btn-warning">تطبيق
                                                        التعديل</button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            @empty
                                <tr>
                                    <td colspan="7" class="text-center py-4">
                                        <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">لا توجد منتجات في مخزون هذا الفرع</p>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
            @if ($inventory->hasPages())
                <div class="card-footer">
                    {{ $inventory->links() }}
                </div>
            @endif
        </div>
    </div>
</x-app-layout>
