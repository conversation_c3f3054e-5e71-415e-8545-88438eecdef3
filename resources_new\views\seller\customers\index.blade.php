<x-app-layout>
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="h3 mb-0">إدارة العملاء</h2>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb mb-0">
                                <li class="breadcrumb-item"><a href="{{ route('seller.dashboard') }}">لوحة التحكم</a></li>
                                <li class="breadcrumb-item active">العملاء</li>
                            </ol>
                        </nav>
                    </div>
                    <div class="text-muted">
                        <i class="fas fa-store"></i> {{ auth()->user()->branch->name ?? 'غير محدد' }}
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card border-0 shadow-sm">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="bg-primary bg-gradient rounded-3 p-3">
                                    <i class="fas fa-users text-white fa-lg"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="text-muted mb-1">إجمالي العملاء</h6>
                                <h4 class="mb-0">{{ number_format($customerSummary['total_customers'] ?? 0) }}</h4>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card border-0 shadow-sm">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="bg-success bg-gradient rounded-3 p-3">
                                    <i class="fas fa-chart-line text-white fa-lg"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="text-muted mb-1">إجمالي المبيعات</h6>
                                <h4 class="mb-0">{{ number_format($customerSummary['total_sales'] ?? 0, 2) }} ج.م</h4>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card border-0 shadow-sm">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="bg-info bg-gradient rounded-3 p-3">
                                    <i class="fas fa-money-bill-wave text-white fa-lg"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="text-muted mb-1">المبلغ المدفوع</h6>
                                <h4 class="mb-0">{{ number_format($customerSummary['total_paid'] ?? 0, 2) }} ج.م</h4>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card border-0 shadow-sm">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="bg-warning bg-gradient rounded-3 p-3">
                                    <i class="fas fa-exclamation-triangle text-white fa-lg"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="text-muted mb-1">المبلغ المستحق</h6>
                                <h4 class="mb-0">{{ number_format($customerSummary['total_owed'] ?? 0, 2) }} ج.م</h4>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Search and Filters -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-body">
                        <form method="GET" action="{{ route('seller.customers.index') }}" class="row g-3">
                            <div class="col-md-4">
                                <label for="search" class="form-label">البحث</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-search"></i>
                                    </span>
                                    <input type="text" class="form-control" id="search" name="search" 
                                           value="{{ request('search') }}" 
                                           placeholder="البحث بالاسم، الهاتف، البريد الإلكتروني...">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <label for="balance_status" class="form-label">حالة الرصيد</label>
                                <select class="form-select" id="balance_status" name="balance_status">
                                    <option value="">جميع العملاء</option>
                                    <option value="owing" {{ request('balance_status') === 'owing' ? 'selected' : '' }}>
                                        عملاء مدينون
                                    </option>
                                    <option value="paid" {{ request('balance_status') === 'paid' ? 'selected' : '' }}>
                                        عملاء مسددون
                                    </option>
                                </select>
                            </div>
                            <div class="col-md-3 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="fas fa-search"></i> بحث
                                </button>
                                <a href="{{ route('seller.customers.index') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times"></i> إعادة تعيين
                                </a>
                            </div>
                            <div class="col-md-2 d-flex align-items-end">
                                <a href="{{ route('seller.customers.create') }}" class="btn btn-success w-100">
                                    <i class="fas fa-plus"></i> إضافة عميل
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Customers Table -->
        <div class="row">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white border-bottom">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-list text-primary"></i> قائمة العملاء
                                @if(request('search'))
                                    <small class="text-muted">(نتائج البحث عن: "{{ request('search') }}")</small>
                                @endif
                            </h5>
                            <div class="btn-group">
                                <button type="button" class="btn btn-outline-success btn-sm">
                                    <i class="fas fa-file-excel"></i> تصدير Excel
                                </button>
                                <button type="button" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-print"></i> طباعة
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th class="border-0">العميل</th>
                                        <th class="border-0">معلومات الاتصال</th>
                                        <th class="border-0">إجمالي المشتريات</th>
                                        <th class="border-0">المبلغ المدفوع</th>
                                        <th class="border-0">المبلغ المستحق</th>
                                        <th class="border-0">آخر عملية شراء</th>
                                        <th class="border-0 text-center">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse($customers as $customer)
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3">
                                                        {{ strtoupper(substr($customer->name, 0, 1)) }}
                                                    </div>
                                                    <div>
                                                        <h6 class="mb-0 fw-semibold">{{ $customer->name }}</h6>
                                                        @if($customer->address)
                                                            <small class="text-muted">{{ Str::limit($customer->address, 40) }}</small>
                                                        @endif
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <div>
                                                    @if($customer->phone)
                                                        <div class="mb-1">
                                                            <i class="fas fa-phone text-muted me-1"></i>
                                                            <span>{{ $customer->phone }}</span>
                                                        </div>
                                                    @endif
                                                    @if($customer->email)
                                                        <div>
                                                            <i class="fas fa-envelope text-muted me-1"></i>
                                                            <small>{{ $customer->email }}</small>
                                                        </div>
                                                    @endif
                                                    @if(!$customer->phone && !$customer->email)
                                                        <span class="text-muted">غير متوفر</span>
                                                    @endif
                                                </div>
                                            </td>
                                            <td>
                                                <span class="fw-semibold">{{ number_format($customer->total_sales_amount, 2) }} ج.م</span>
                                                <br><small class="text-muted">{{ $customer->sales_count }} فاتورة</small>
                                            </td>
                                            <td>
                                                <span class="text-success fw-semibold">{{ number_format($customer->total_paid_amount, 2) }} ج.م</span>
                                            </td>
                                            <td>
                                                @if($customer->total_remaining_amount > 0)
                                                    <span class="badge bg-warning fs-6">{{ number_format($customer->total_remaining_amount, 2) }} ج.م</span>
                                                @else
                                                    <span class="badge bg-success fs-6">مسدد</span>
                                                @endif
                                            </td>
                                            <td>
                                                @if($customer->last_sale_date)
                                                    <span class="fw-semibold">{{ \Carbon\Carbon::parse($customer->last_sale_date)->format('Y-m-d') }}</span>
                                                    <br><small class="text-muted">{{ \Carbon\Carbon::parse($customer->last_sale_date)->diffForHumans() }}</small>
                                                @else
                                                    <span class="text-muted">لا توجد مشتريات</span>
                                                @endif
                                            </td>
                                            <td class="text-center">
                                                <div class="btn-group btn-group-sm">
                                                    <a href="{{ route('seller.customers.show', $customer) }}" 
                                                       class="btn btn-outline-primary" title="عرض التفاصيل">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="{{ route('seller.customers.edit', $customer) }}" 
                                                       class="btn btn-outline-warning" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    @if($customer->total_remaining_amount > 0)
                                                        <a href="{{ route('seller.customer-payments.create', ['customer_id' => $customer->id]) }}" 
                                                           class="btn btn-outline-success" title="تسجيل دفعة">
                                                            <i class="fas fa-money-bill"></i>
                                                        </a>
                                                    @endif
                                                    <a href="{{ route('seller.sales.create', ['customer_id' => $customer->id]) }}" 
                                                       class="btn btn-outline-info" title="بيع جديد">
                                                        <i class="fas fa-plus"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="7" class="text-center py-5">
                                                <div class="text-muted">
                                                    <i class="fas fa-users fa-3x mb-3"></i>
                                                    <h5>لا توجد عملاء</h5>
                                                    @if(request('search'))
                                                        <p>لا توجد عملاء تطابق معايير البحث المحددة</p>
                                                        <a href="{{ route('seller.customers.index') }}" class="btn btn-outline-primary">
                                                            <i class="fas fa-times"></i> إعادة تعيين البحث
                                                        </a>
                                                    @else
                                                        <p>لم يقم أي عميل بالشراء من فرعك بعد</p>
                                                        <a href="{{ route('seller.customers.create') }}" class="btn btn-primary">
                                                            <i class="fas fa-plus"></i> إضافة عميل جديد
                                                        </a>
                                                    @endif
                                                </div>
                                            </td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>

                    @if(method_exists($customers, 'hasPages') && $customers->hasPages())
                        <div class="card-footer bg-white border-top">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="text-muted">
                                    عرض {{ $customers->firstItem() }} إلى {{ $customers->lastItem() }} من أصل {{ $customers->total() }} عميل
                                </div>
                                {{ $customers->appends(request()->query())->links() }}
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Outstanding Balances Alert -->
        @if(($customerSummary['total_owed'] ?? 0) > 0)
            <div class="row mt-4">
                <div class="col-12">
                    <div class="alert alert-warning border-0 shadow-sm">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <i class="fas fa-exclamation-triangle fa-2x"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h5 class="alert-heading mb-1">تنبيه مستحقات العملاء</h5>
                                <p class="mb-2">
                                    إجمالي المبلغ المستحق من العملاء: 
                                    <strong>{{ number_format($customerSummary['total_owed'], 2) }} ج.م</strong>
                                    من <strong>{{ $customerSummary['customers_owing'] ?? 0 }}</strong> عميل
                                </p>
                                <a href="{{ route('seller.customers.index', ['balance_status' => 'owing']) }}" 
                                   class="btn btn-warning btn-sm">
                                    <i class="fas fa-eye"></i> عرض العملاء المدينين
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @endif
    </div>
</x-app-layout>
