<x-app-layout>
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h4 class="mb-1">استلام المنتجات - {{ $branch->name }}</h4>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">الرئيسية</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.branches.index') }}">الفروع</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.branches.show', $branch) }}">{{ $branch->name }}</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.branches.sales-inventory', $branch) }}">مخزون المبيعات</a></li>
                        <li class="breadcrumb-item active">استلام المنتجات</li>
                    </ol>
                </nav>
            </div>
            <div>
                <a href="{{ route('admin.branches.sales-inventory', $branch) }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right"></i> عودة
                </a>
            </div>
        </div>

        @if($pendingTransfers->count() > 0)
            <!-- Pending Transfers -->
            <div class="row">
                @foreach($pendingTransfers as $transfer)
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-0">طلب نقل #{{ $transfer->transfer_number }}</h6>
                                    <small class="text-muted">من: {{ $transfer->source->name }}</small>
                                </div>
                                <div>
                                    @if($transfer->status === 'approved')
                                        <span class="badge bg-warning">معتمد</span>
                                    @elseif($transfer->status === 'in_transit')
                                        <span class="badge bg-info">في الطريق</span>
                                    @endif
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <strong>تاريخ الطلب:</strong> {{ $transfer->created_at->format('Y-m-d H:i') }}<br>
                                    <strong>المطلوب بواسطة:</strong> {{ $transfer->requestedBy->name ?? 'غير محدد' }}<br>
                                    @if($transfer->notes)
                                        <strong>ملاحظات:</strong> {{ $transfer->notes }}
                                    @endif
                                </div>

                                <!-- Transfer Items -->
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>المنتج</th>
                                                <th>الكمية المطلوبة</th>
                                                <th>الكمية المرسلة</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($transfer->items as $item)
                                                <tr>
                                                    <td>
                                                        <div>
                                                            <strong>{{ $item->product->name }}</strong><br>
                                                            <small class="text-muted">{{ $item->product->barcode }}</small>
                                                        </div>
                                                    </td>
                                                    <td>{{ number_format($item->requested_quantity) }}</td>
                                                    <td>
                                                        @if($item->shipped_quantity)
                                                            <span class="text-success fw-bold">{{ number_format($item->shipped_quantity) }}</span>
                                                        @else
                                                            <span class="text-muted">لم يتم الشحن</span>
                                                        @endif
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>

                                <!-- Action Buttons -->
                                <div class="d-flex gap-2 mt-3">
                                    @if($transfer->status === 'in_transit')
                                        <form method="POST" action="{{ route('admin.inventory-transfers.receive', $transfer) }}" class="d-inline">
                                            @csrf
                                            <button type="submit" class="btn btn-success btn-sm" 
                                                    onclick="return confirm('هل أنت متأكد من استلام هذه المنتجات؟')">
                                                <i class="fas fa-check"></i> تأكيد الاستلام
                                            </button>
                                        </form>
                                    @endif
                                    
                                    <a href="{{ route('admin.inventory-transfers.show', $transfer) }}" class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-eye"></i> عرض التفاصيل
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        @else
            <!-- No Pending Transfers -->
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="fas fa-truck fa-4x text-muted mb-4"></i>
                    <h5 class="text-muted">لا توجد منتجات في انتظار الاستلام</h5>
                    <p class="text-muted mb-4">
                        لا توجد طلبات نقل معتمدة أو في الطريق لهذا الفرع حالياً.
                    </p>
                    <div class="d-flex gap-2 justify-content-center">
                        <a href="{{ route('admin.inventory-transfers.create') }}?destination_type=branch&destination_id={{ $branch->id }}" 
                           class="btn btn-primary">
                            <i class="fas fa-plus"></i> طلب نقل منتجات
                        </a>
                        <a href="{{ route('admin.inventory-transfers.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-list"></i> عرض جميع طلبات النقل
                        </a>
                    </div>
                </div>
            </div>
        @endif

        <!-- Quick Actions -->
        <div class="row mt-4">
            <div class="col-md-4">
                <div class="card bg-light">
                    <div class="card-body text-center">
                        <i class="fas fa-plus-circle fa-2x text-primary mb-3"></i>
                        <h6>طلب منتجات جديدة</h6>
                        <p class="text-muted small">إنشاء طلب نقل منتجات من المخازن</p>
                        <a href="{{ route('admin.inventory-transfers.create') }}?destination_type=branch&destination_id={{ $branch->id }}" 
                           class="btn btn-primary btn-sm">
                            إنشاء طلب
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-light">
                    <div class="card-body text-center">
                        <i class="fas fa-history fa-2x text-info mb-3"></i>
                        <h6>سجل الاستلام</h6>
                        <p class="text-muted small">عرض تاريخ المنتجات المستلمة</p>
                        <a href="{{ route('admin.inventory-transfers.index') }}?destination_type=branch&destination_id={{ $branch->id }}&status=completed" 
                           class="btn btn-info btn-sm">
                            عرض السجل
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-light">
                    <div class="card-body text-center">
                        <i class="fas fa-chart-line fa-2x text-success mb-3"></i>
                        <h6>تقارير المخزون</h6>
                        <p class="text-muted small">تحليل حركة المنتجات والمبيعات</p>
                        <a href="{{ route('admin.branches.analytics', $branch) }}" class="btn btn-success btn-sm">
                            عرض التقارير
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Instructions -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-info-circle"></i> تعليمات الاستلام</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary">خطوات الاستلام:</h6>
                        <ol class="small">
                            <li>تحقق من وصول المنتجات المطلوبة</li>
                            <li>قم بفحص الكميات والجودة</li>
                            <li>اضغط على "تأكيد الاستلام" عند التأكد</li>
                            <li>سيتم تحديث مخزون الفرع تلقائياً</li>
                        </ol>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-warning">ملاحظات مهمة:</h6>
                        <ul class="small">
                            <li>تأكد من مطابقة الكميات المستلمة مع المرسلة</li>
                            <li>في حالة وجود نقص، تواصل مع المخزن المرسل</li>
                            <li>لا يمكن التراجع عن تأكيد الاستلام</li>
                            <li>سيتم إشعار المخزن المرسل بتأكيد الاستلام</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
