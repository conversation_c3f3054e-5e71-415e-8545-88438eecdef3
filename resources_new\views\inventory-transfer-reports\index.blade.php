@extends('layouts.app')

@section('title', 'تقارير نقل المخزون')

@section('content')
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-0 text-gray-800">تقارير نقل المخزون</h1>
                <p class="text-muted mb-0">تحليلات شاملة لحركة المنتجات بين المخازن والفروع</p>
            </div>
            <div>
                <a href="{{ user_route('inventory-transfer-reports.export', ['type' => 'transfers', 'date_from' => $dateFrom, 'date_to' => $dateTo]) }}"
                    class="btn btn-success">
                    <i class="fas fa-file-excel me-2"></i>تصدير التقرير
                </a>
            </div>
        </div>

        <!-- Filters -->
        <div class="card shadow mb-4">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-2">
                        <label class="form-label">من تاريخ</label>
                        <input type="date" name="date_from" class="form-control" value="{{ $dateFrom }}">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">إلى تاريخ</label>
                        <input type="date" name="date_to" class="form-control" value="{{ $dateTo }}">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">الحالة</label>
                        <select name="status" class="form-select">
                            <option value="">جميع الحالات</option>
                            <option value="pending" {{ $status == 'pending' ? 'selected' : '' }}>قيد الانتظار</option>
                            <option value="approved" {{ $status == 'approved' ? 'selected' : '' }}>موافق عليه</option>
                            <option value="shipped" {{ $status == 'shipped' ? 'selected' : '' }}>تم الشحن</option>
                            <option value="received" {{ $status == 'received' ? 'selected' : '' }}>تم الاستلام</option>
                            <option value="cancelled" {{ $status == 'cancelled' ? 'selected' : '' }}>ملغي</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">من موقع</label>
                        <select name="from_location_id" class="form-select">
                            <option value="">جميع المواقع</option>
                            <optgroup label="المخازن">
                                @foreach ($stores as $store)
                                    <option value="{{ $store->id }}"
                                        {{ $fromLocationId == $store->id ? 'selected' : '' }}>
                                        {{ $store->name }}
                                    </option>
                                @endforeach
                            </optgroup>
                            <optgroup label="الفروع">
                                @foreach ($branches as $branch)
                                    <option value="{{ $branch->id }}"
                                        {{ $fromLocationId == $branch->id ? 'selected' : '' }}>
                                        {{ $branch->name }}
                                    </option>
                                @endforeach
                            </optgroup>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">إلى موقع</label>
                        <select name="to_location_id" class="form-select">
                            <option value="">جميع المواقع</option>
                            <optgroup label="المخازن">
                                @foreach ($stores as $store)
                                    <option value="{{ $store->id }}"
                                        {{ $toLocationId == $store->id ? 'selected' : '' }}>
                                        {{ $store->name }}
                                    </option>
                                @endforeach
                            </optgroup>
                            <optgroup label="الفروع">
                                @foreach ($branches as $branch)
                                    <option value="{{ $branch->id }}"
                                        {{ $toLocationId == $branch->id ? 'selected' : '' }}>
                                        {{ $branch->name }}
                                    </option>
                                @endforeach
                            </optgroup>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <button type="submit" class="btn btn-primary d-block w-100">
                            <i class="fas fa-search me-2"></i>بحث
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-xl-2 col-md-4 mb-4">
                <div class="card border-left-primary shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                    إجمالي النقل
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $totalTransfers }}</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-exchange-alt fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-2 col-md-4 mb-4">
                <div class="card border-left-warning shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                    قيد الانتظار
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $pendingTransfers }}</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-clock fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-2 col-md-4 mb-4">
                <div class="card border-left-info shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                    موافق عليه
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $approvedTransfers }}</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-check fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-2 col-md-4 mb-4">
                <div class="card border-left-secondary shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-secondary text-uppercase mb-1">
                                    تم الشحن
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $shippedTransfers }}</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-shipping-fast fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-2 col-md-4 mb-4">
                <div class="card border-left-success shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                    تم الاستلام
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $receivedTransfers }}</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-2 col-md-4 mb-4">
                <div class="card border-left-danger shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                    ملغي
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $cancelledTransfers }}</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-times-circle fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Recent Transfers -->
            <div class="col-lg-8 mb-4">
                <div class="card shadow">
                    <div class="card-header py-3 d-flex justify-content-between align-items-center">
                        <h6 class="m-0 font-weight-bold text-primary">آخر عمليات النقل</h6>
                        <a href="{{ user_route('inventory-transfer-reports.transfer-history') }}"
                            class="btn btn-sm btn-outline-primary">
                            عرض الكل
                        </a>
                    </div>
                    <div class="card-body">
                        @if ($recentTransfers->count() > 0)
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th class="border-0 fw-bold">رقم النقل</th>
                                            <th class="border-0 fw-bold">من</th>
                                            <th class="border-0 fw-bold">إلى</th>
                                            <th class="border-0 fw-bold">الحالة</th>
                                            <th class="border-0 fw-bold">التاريخ</th>
                                            <th class="border-0 fw-bold">المستخدم</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach ($recentTransfers as $transfer)
                                            <tr>
                                                <td>
                                                    <a href="{{ user_route('inventory-transfers.show', $transfer) }}"
                                                        class="fw-bold text-primary text-decoration-none">
                                                        #{{ $transfer->id }}
                                                    </a>
                                                </td>
                                                <td>
                                                    <span class="badge bg-info">
                                                        {{ $transfer->fromStore ? $transfer->fromStore->name : $transfer->fromBranch->name }}
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="badge bg-success">
                                                        {{ $transfer->toStore ? $transfer->toStore->name : $transfer->toBranch->name }}
                                                    </span>
                                                </td>
                                                <td>
                                                    @php
                                                        $statusColors = [
                                                            'pending' => 'warning',
                                                            'approved' => 'info',
                                                            'shipped' => 'secondary',
                                                            'received' => 'success',
                                                            'cancelled' => 'danger',
                                                        ];
                                                        $statusLabels = [
                                                            'pending' => 'قيد الانتظار',
                                                            'approved' => 'موافق عليه',
                                                            'shipped' => 'تم الشحن',
                                                            'received' => 'تم الاستلام',
                                                            'cancelled' => 'ملغي',
                                                        ];
                                                    @endphp
                                                    <span
                                                        class="badge bg-{{ $statusColors[$transfer->status] ?? 'secondary' }}">
                                                        {{ $statusLabels[$transfer->status] ?? $transfer->status }}
                                                    </span>
                                                </td>
                                                <td>
                                                    <div class="fw-bold">{{ $transfer->created_at->format('Y-m-d') }}
                                                    </div>
                                                    <small
                                                        class="text-muted">{{ $transfer->created_at->format('H:i') }}</small>
                                                </td>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <div class="avatar-circle bg-primary text-white me-2">
                                                            {{ substr($transfer->user->name ?? 'غير محدد', 0, 1) }}
                                                        </div>
                                                        <span
                                                            class="small">{{ $transfer->user->name ?? 'غير محدد' }}</span>
                                                    </div>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @else
                            <div class="text-center py-3">
                                <i class="fas fa-exchange-alt fa-2x text-muted mb-2"></i>
                                <p class="text-muted">لا توجد عمليات نقل حديثة</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Top Products -->
            <div class="col-lg-4 mb-4">
                <div class="card shadow">
                    <div class="card-header py-3 d-flex justify-content-between align-items-center">
                        <h6 class="m-0 font-weight-bold text-primary">أكثر المنتجات نقلاً</h6>
                        <a href="{{ user_route('inventory-transfer-reports.product-movements') }}"
                            class="btn btn-sm btn-outline-primary">
                            عرض الكل
                        </a>
                    </div>
                    <div class="card-body">
                        @if ($topProducts->count() > 0)
                            @foreach ($topProducts as $product)
                                <div class="d-flex align-items-center justify-content-between mb-3">
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-circle bg-success text-white me-2">
                                            {{ substr($product->name, 0, 1) }}
                                        </div>
                                        <div>
                                            <div class="fw-bold">{{ $product->name }}</div>
                                        </div>
                                    </div>
                                    <div class="text-end">
                                        <div class="fw-bold text-success">{{ $product->total_quantity }}</div>
                                        <small class="text-muted">قطعة</small>
                                    </div>
                                </div>
                            @endforeach
                        @else
                            <div class="text-center py-3">
                                <i class="fas fa-box fa-2x text-muted mb-2"></i>
                                <p class="text-muted">لا توجد منتجات منقولة</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        .avatar-circle {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 12px;
        }

        .border-left-primary {
            border-left: 0.25rem solid #4e73df !important;
        }

        .border-left-warning {
            border-left: 0.25rem solid #f6c23e !important;
        }

        .border-left-info {
            border-left: 0.25rem solid #36b9cc !important;
        }

        .border-left-secondary {
            border-left: 0.25rem solid #858796 !important;
        }

        .border-left-success {
            border-left: 0.25rem solid #1cc88a !important;
        }

        .border-left-danger {
            border-left: 0.25rem solid #e74a3b !important;
        }
    </style>
@endsection
