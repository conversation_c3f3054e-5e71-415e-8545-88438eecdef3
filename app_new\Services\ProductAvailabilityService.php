<?php

namespace App\Services;

use App\Models\Product;
use App\Models\User;
use App\Models\Branch;
use App\Models\Store;
use App\Models\BranchInventory;
use App\Models\StoreInventory;
use Illuminate\Database\Eloquent\Collection;

class ProductAvailabilityService
{
    protected $priceResolutionService;

    public function __construct(PriceResolutionService $priceResolutionService)
    {
        $this->priceResolutionService = $priceResolutionService;
    }
    /**
     * Get products with cross-location availability for a seller
     */
    public function getProductsWithAvailability(User $user, array $filters = []): Collection
    {
        $query = Product::with([
            'category',
            'branchInventories.branch',
            'storeInventories.store'
        ]);

        // Apply search filter
        if (!empty($filters['search'])) {
            $search = $filters['search'];
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                    ->orWhere('sku', 'like', "%{$search}%");
            });
        }

        // Apply category filter
        if (!empty($filters['category_id'])) {
            $query->where('category_id', $filters['category_id']);
        }

        // Apply stock status filter
        if (!empty($filters['stock_status'])) {
            switch ($filters['stock_status']) {
                case 'available':
                    // Only products available in user's branch
                    $query->whereHas('branchInventories', function ($q) use ($user) {
                        $q->where('branch_id', $user->branch_id)
                            ->where('quantity', '>', 0);
                    });
                    break;
                case 'low':
                    // Products with low stock in user's branch (1-10 quantity)
                    $query->whereHas('branchInventories', function ($q) use ($user) {
                        $q->where('branch_id', $user->branch_id)
                            ->where('quantity', '>', 0)
                            ->where('quantity', '<=', 10);
                    });
                    break;
                case 'out':
                    // Products not available in user's branch
                    $query->whereDoesntHave('branchInventories', function ($q) use ($user) {
                        $q->where('branch_id', $user->branch_id)
                            ->where('quantity', '>', 0);
                    });
                    break;
            }
        }

        // Keep the original availability filter for backward compatibility
        if (!empty($filters['availability'])) {
            switch ($filters['availability']) {
                case 'local':
                    // Only products available in user's branch
                    $query->whereHas('branchInventories', function ($q) use ($user) {
                        $q->where('branch_id', $user->branch_id)
                            ->where('quantity', '>', 0);
                    });
                    break;
                case 'other_branches':
                    // Products available in other branches but not in user's branch
                    $query->whereHas('branchInventories', function ($q) use ($user) {
                        $q->where('branch_id', '!=', $user->branch_id)
                            ->where('quantity', '>', 0);
                    })->whereDoesntHave('branchInventories', function ($q) use ($user) {
                        $q->where('branch_id', $user->branch_id)
                            ->where('quantity', '>', 0);
                    });
                    break;
                case 'stores':
                    // Products available in stores
                    $query->whereHas('storeInventories', function ($q) {
                        $q->where('quantity', '>', 0);
                    });
                    break;
                case 'out_of_stock':
                    // Products with no stock anywhere
                    $query->whereDoesntHave('branchInventories', function ($q) {
                        $q->where('quantity', '>', 0);
                    })->whereDoesntHave('storeInventories', function ($q) {
                        $q->where('quantity', '>', 0);
                    });
                    break;
            }
        }

        $products = $query->get();

        // Enhance each product with availability data
        return $products->map(function ($product) use ($user) {
            return $this->enhanceProductWithAvailability($product, $user);
        });
    }

    /**
     * Enhance a product with availability information across locations
     */
    public function enhanceProductWithAvailability(Product $product, User $user): Product
    {
        // Get availability in user's branch
        $userBranchInventory = $product->branchInventories
            ->where('branch_id', $user->branch_id)
            ->first();

        $product->local_quantity = $userBranchInventory ? $userBranchInventory->quantity : 0;
        $product->local_available = $product->local_quantity > 0;

        // Use PriceResolutionService to get the correct selling price
        $priceInfo = $this->priceResolutionService->getSellingPrice($product, $user->branch_id);
        $product->setAttribute('selling_price', $priceInfo['price']);
        $product->setAttribute('price_source', $priceInfo['source']);
        $product->setAttribute('cost_price_resolved', $priceInfo['cost_price']);
        $product->setAttribute('profit_margin', $priceInfo['profit_margin']);

        // Add all sale prices from branch inventory
        if ($userBranchInventory) {
            $product->setAttribute('sale_price_1', $userBranchInventory->sale_price_1);
            $product->setAttribute('sale_price_2', $userBranchInventory->sale_price_2);
            $product->setAttribute('sale_price_3', $userBranchInventory->sale_price_3);
        } else {
            // If no branch inventory, set sale prices to null
            $product->setAttribute('sale_price_1', null);
            $product->setAttribute('sale_price_2', null);
            $product->setAttribute('sale_price_3', null);
        }

        // Get availability in other branches
        $otherBranchesInventory = $product->branchInventories
            ->where('branch_id', '!=', $user->branch_id)
            ->filter(function ($inventory) {
                return $inventory->quantity > 0;
            });

        $product->other_branches_availability = $otherBranchesInventory->map(function ($inventory) {
            return [
                'branch_id' => $inventory->branch_id,
                'branch_name' => $inventory->branch->name,
                'branch_code' => $inventory->branch->code ?? '',
                'quantity' => $inventory->quantity,
                'location_type' => 'branch',
                'location_display' => "فرع {$inventory->branch->name}",
            ];
        })->values();

        // Get availability in accessible stores
        // For sellers, allow viewing all stores for inventory visibility
        if ($user->hasRole('seller')) {
            $storesInventory = $product->storeInventories
                ->filter(function ($inventory) {
                    return $inventory->quantity > 0;
                });
        } else {
            $accessibleStoreIds = $user->getAccessibleStoreIds();
            $storesInventory = $product->storeInventories
                ->whereIn('store_id', $accessibleStoreIds)
                ->filter(function ($inventory) {
                    return $inventory->quantity > 0;
                });
        }

        $product->stores_availability = $storesInventory->map(function ($inventory) {
            return [
                'store_id' => $inventory->store_id,
                'store_name' => $inventory->store->name,
                'store_code' => $inventory->store->code ?? '',
                'quantity' => $inventory->quantity,
                'location_type' => 'store',
                'location_display' => "مخزن {$inventory->store->name}",
            ];
        })->values();

        // Calculate total available quantity across all locations
        $product->total_quantity = $product->local_quantity +
            $product->other_branches_availability->sum('quantity') +
            $product->stores_availability->sum('quantity');

        // Combine all available locations for easy display
        $allLocations = collect();

        // Add other branches
        foreach ($product->other_branches_availability as $branch) {
            $allLocations->push($branch);
        }

        // Add stores
        foreach ($product->stores_availability as $store) {
            $allLocations->push($store);
        }

        $product->all_available_locations = $allLocations;

        // Determine availability status
        if ($product->local_available) {
            $product->availability_status = 'local';
            $product->availability_text = 'متوفر محلياً';
            $product->availability_class = 'success';
        } elseif ($product->other_branches_availability->count() > 0 || $product->stores_availability->count() > 0) {
            $product->availability_status = 'transferable';

            // Create detailed availability text with location names
            $locationNames = $allLocations->pluck('location_display')->take(2)->implode('، ');
            $remainingCount = $allLocations->count() - 2;

            if ($remainingCount > 0) {
                $product->availability_text = "متوفر في {$locationNames} و {$remainingCount} موقع آخر";
            } else {
                $product->availability_text = "متوفر في {$locationNames}";
            }

            $product->availability_class = 'warning';
        } else {
            $product->availability_status = 'unavailable';
            $product->availability_text = 'غير متوفر';
            $product->availability_class = 'danger';
        }

        return $product;
    }

    /**
     * Get the best source location for transferring a product to user's branch
     */
    public function getBestTransferSource(Product $product, User $user, int $requestedQuantity): ?array
    {
        // First check other branches (usually faster transfers)
        foreach ($product->other_branches_availability as $branchAvailability) {
            if ($branchAvailability['quantity'] >= $requestedQuantity) {
                return [
                    'type' => 'branch',
                    'id' => $branchAvailability['branch_id'],
                    'name' => $branchAvailability['branch_name'],
                    'available_quantity' => $branchAvailability['quantity'],
                ];
            }
        }

        // Then check accessible stores
        foreach ($product->stores_availability as $storeAvailability) {
            if ($storeAvailability['quantity'] >= $requestedQuantity) {
                return [
                    'type' => 'store',
                    'id' => $storeAvailability['store_id'],
                    'name' => $storeAvailability['store_name'],
                    'available_quantity' => $storeAvailability['quantity'],
                ];
            }
        }

        return null;
    }

    /**
     * Check if a product can be transferred to user's branch
     */
    public function canTransferProduct(Product $product, User $user, int $requestedQuantity): bool
    {
        return $this->getBestTransferSource($product, $user, $requestedQuantity) !== null;
    }
}
