<?php

namespace App\Http\Controllers;

use App\Models\Purchase;
use App\Models\Supplier;
use App\Models\AccountTransaction;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class PaymentReportController extends Controller
{
    /**
     * Display payment analytics dashboard.
     */
    public function index(Request $request)
    {
        $dateFrom = $request->get('date_from', now()->startOfMonth()->format('Y-m-d'));
        $dateTo = $request->get('date_to', now()->format('Y-m-d'));
        $supplierId = $request->get('supplier_id');

        // Outstanding balances summary
        $outstandingQuery = Purchase::where('status', 'completed')
            ->where('remaining_amount', '>', 0);

        if ($supplierId) {
            $outstandingQuery->where('supplier_id', $supplierId);
        }

        $totalOutstanding = $outstandingQuery->sum('remaining_amount');
        $outstandingCount = $outstandingQuery->count();

        // Payment transactions in date range
        $paymentsQuery = AccountTransaction::where('type', 'deposit')
            ->where('transactionable_type', Purchase::class)
            ->whereBetween('created_at', [$dateFrom . ' 00:00:00', $dateTo . ' 23:59:59']);

        if ($supplierId) {
            $paymentsQuery->whereHas('transactionable', function ($query) use ($supplierId) {
                $query->where('supplier_id', $supplierId);
            });
        }

        $totalPayments = $paymentsQuery->sum('amount');
        $paymentsCount = $paymentsQuery->count();

        // Top suppliers by outstanding amount
        $topOutstandingSuppliers = Supplier::select('suppliers.*')
            ->selectRaw('SUM(purchases.remaining_amount) as total_outstanding')
            ->join('purchases', 'suppliers.id', '=', 'purchases.supplier_id')
            ->where('purchases.status', 'completed')
            ->where('purchases.remaining_amount', '>', 0)
            ->groupBy('suppliers.id', 'suppliers.name', 'suppliers.phone', 'suppliers.email', 'suppliers.address', 'suppliers.created_at', 'suppliers.updated_at')
            ->orderByDesc('total_outstanding')
            ->limit(10)
            ->get();

        // Recent payments
        $recentPayments = AccountTransaction::with(['transactionable.supplier', 'user'])
            ->where('type', 'deposit')
            ->where('transactionable_type', Purchase::class)
            ->orderByDesc('created_at')
            ->limit(10)
            ->get();

        // Monthly payment trends (last 6 months)
        $monthlyTrends = AccountTransaction::select(
                DB::raw('DATE_FORMAT(created_at, "%Y-%m") as month'),
                DB::raw('SUM(amount) as total_amount'),
                DB::raw('COUNT(*) as payment_count')
            )
            ->where('type', 'deposit')
            ->where('transactionable_type', Purchase::class)
            ->where('created_at', '>=', now()->subMonths(6))
            ->groupBy('month')
            ->orderBy('month')
            ->get();

        // Suppliers for filter dropdown
        $suppliers = Supplier::orderBy('name')->get();

        return view('payment-reports.index', compact(
            'totalOutstanding',
            'outstandingCount',
            'totalPayments',
            'paymentsCount',
            'topOutstandingSuppliers',
            'recentPayments',
            'monthlyTrends',
            'suppliers',
            'dateFrom',
            'dateTo',
            'supplierId'
        ));
    }

    /**
     * Display detailed outstanding balances report.
     */
    public function outstandingBalances(Request $request)
    {
        $supplierId = $request->get('supplier_id');
        $sortBy = $request->get('sort_by', 'remaining_amount');
        $sortOrder = $request->get('sort_order', 'desc');

        $query = Purchase::with(['supplier', 'branch'])
            ->where('status', 'completed')
            ->where('remaining_amount', '>', 0);

        if ($supplierId) {
            $query->where('supplier_id', $supplierId);
        }

        $purchases = $query->orderBy($sortBy, $sortOrder)->paginate(20);

        $totalOutstanding = $query->sum('remaining_amount');
        $suppliers = Supplier::orderBy('name')->get();

        return view('payment-reports.outstanding-balances', compact(
            'purchases',
            'totalOutstanding',
            'suppliers',
            'supplierId',
            'sortBy',
            'sortOrder'
        ));
    }

    /**
     * Display payment history report.
     */
    public function paymentHistory(Request $request)
    {
        $dateFrom = $request->get('date_from', now()->startOfMonth()->format('Y-m-d'));
        $dateTo = $request->get('date_to', now()->format('Y-m-d'));
        $supplierId = $request->get('supplier_id');

        $query = AccountTransaction::with(['transactionable.supplier', 'user'])
            ->where('type', 'deposit')
            ->where('transactionable_type', Purchase::class)
            ->whereBetween('created_at', [$dateFrom . ' 00:00:00', $dateTo . ' 23:59:59']);

        if ($supplierId) {
            $query->whereHas('transactionable', function ($q) use ($supplierId) {
                $q->where('supplier_id', $supplierId);
            });
        }

        $payments = $query->orderByDesc('created_at')->paginate(20);
        $totalAmount = $query->sum('amount');
        $suppliers = Supplier::orderBy('name')->get();

        return view('payment-reports.payment-history', compact(
            'payments',
            'totalAmount',
            'suppliers',
            'dateFrom',
            'dateTo',
            'supplierId'
        ));
    }

    /**
     * Display supplier payment summary.
     */
    public function supplierSummary(Request $request, Supplier $supplier)
    {
        $dateFrom = $request->get('date_from', now()->startOfYear()->format('Y-m-d'));
        $dateTo = $request->get('date_to', now()->format('Y-m-d'));

        // Supplier statistics
        $totalPurchases = $supplier->purchases()
            ->whereBetween('purchase_date', [$dateFrom, $dateTo])
            ->sum('total_amount');

        $totalPaid = $supplier->purchases()
            ->whereBetween('purchase_date', [$dateFrom, $dateTo])
            ->sum('paid_amount');

        $totalOutstanding = $supplier->purchases()
            ->where('status', 'completed')
            ->sum('remaining_amount');

        // Recent purchases with outstanding balances
        $outstandingPurchases = $supplier->purchases()
            ->where('status', 'completed')
            ->where('remaining_amount', '>', 0)
            ->orderByDesc('purchase_date')
            ->limit(10)
            ->get();

        // Recent payments
        $recentPayments = AccountTransaction::with(['transactionable', 'user'])
            ->where('type', 'deposit')
            ->where('transactionable_type', Purchase::class)
            ->whereHas('transactionable', function ($query) use ($supplier) {
                $query->where('supplier_id', $supplier->id);
            })
            ->whereBetween('created_at', [$dateFrom . ' 00:00:00', $dateTo . ' 23:59:59'])
            ->orderByDesc('created_at')
            ->limit(10)
            ->get();

        return view('payment-reports.supplier-summary', compact(
            'supplier',
            'totalPurchases',
            'totalPaid',
            'totalOutstanding',
            'outstandingPurchases',
            'recentPayments',
            'dateFrom',
            'dateTo'
        ));
    }

    /**
     * Export payment data to CSV.
     */
    public function export(Request $request)
    {
        $type = $request->get('type', 'outstanding');
        $dateFrom = $request->get('date_from', now()->startOfMonth()->format('Y-m-d'));
        $dateTo = $request->get('date_to', now()->format('Y-m-d'));

        $filename = "payment_report_{$type}_" . now()->format('Y_m_d_H_i_s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function () use ($type, $dateFrom, $dateTo) {
            $file = fopen('php://output', 'w');

            if ($type === 'outstanding') {
                fputcsv($file, ['رقم العملية', 'المورد', 'تاريخ الشراء', 'إجمالي المبلغ', 'المبلغ المدفوع', 'المبلغ المتبقي']);

                Purchase::with('supplier')
                    ->where('status', 'completed')
                    ->where('remaining_amount', '>', 0)
                    ->chunk(100, function ($purchases) use ($file) {
                        foreach ($purchases as $purchase) {
                            fputcsv($file, [
                                $purchase->id,
                                $purchase->supplier->name,
                                $purchase->purchase_date->format('Y-m-d'),
                                $purchase->total_amount,
                                $purchase->paid_amount,
                                $purchase->remaining_amount
                            ]);
                        }
                    });
            } else {
                fputcsv($file, ['رقم المرجع', 'رقم العملية', 'المورد', 'المبلغ', 'تاريخ الدفع', 'المستخدم']);

                AccountTransaction::with(['transactionable.supplier', 'user'])
                    ->where('type', 'deposit')
                    ->where('transactionable_type', Purchase::class)
                    ->whereBetween('created_at', [$dateFrom . ' 00:00:00', $dateTo . ' 23:59:59'])
                    ->chunk(100, function ($payments) use ($file) {
                        foreach ($payments as $payment) {
                            fputcsv($file, [
                                $payment->reference_number,
                                $payment->transactionable->id,
                                $payment->transactionable->supplier->name,
                                $payment->amount,
                                $payment->created_at->format('Y-m-d H:i'),
                                $payment->user->name ?? 'غير محدد'
                            ]);
                        }
                    });
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
