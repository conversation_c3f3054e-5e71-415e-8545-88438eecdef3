<?php

use App\Http\Controllers\AdminDashboardController;
use App\Http\Controllers\BranchController;
use App\Http\Controllers\StoreController;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\CustomerController;
use App\Http\Controllers\SupplierController;
use App\Http\Controllers\PurchaseController;
use App\Http\Controllers\PurchaseDistributionController;
use App\Http\Controllers\ExpenseController;
use App\Http\Controllers\ExpenseCategoryController;
use App\Http\Controllers\ReportController;
use App\Http\Controllers\SettingController;
use App\Http\Controllers\AccountController;
use App\Http\Controllers\AccountTransactionController;
use App\Http\Controllers\SaleController;
use App\Http\Controllers\InventoryTransferController;
use App\Http\Controllers\SupplierPaymentController;
use App\Http\Controllers\PaymentReportController;
use App\Http\Controllers\InventoryTransferReportController;
use App\Http\Controllers\PurchaseReturnController;
use App\Http\Controllers\PriceManagementController;
use App\Http\Controllers\InventoryController;
use App\Http\Controllers\TransferController;
use App\Http\Controllers\DirectTransferController;
use App\Http\Controllers\CustomerPaymentController;
use App\Http\Controllers\SaleReturnController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Admin Routes
|--------------------------------------------------------------------------
|
| Here are the routes that are only accessible by admin users.
| These routes are automatically prefixed with 'admin' and protected
| by the 'role:admin' middleware.
|
*/

Route::middleware(['auth', 'verified', 'role:admin'])->prefix('admin')->name('admin.')->group(function () {

    // Admin Dashboard
    Route::get('/dashboard', [AdminDashboardController::class, 'index'])->name('dashboard');
    Route::get('/stores', [AdminDashboardController::class, 'stores'])->name('stores');
    Route::get('/financial-reports', [AdminDashboardController::class, 'financialReports'])->name('financial-reports');

    // Branch Management
    // Specific routes must come before resource routes to avoid conflicts
    Route::get('branches/analytics-dashboard', [AdminDashboardController::class, 'branchAnalytics'])->name('branches.analytics-dashboard');
    Route::get('branches/analytics/data', [AdminDashboardController::class, 'getAnalyticsData'])->name('branches.analytics.data');
    Route::get('branches/analytics/monthly-trends', [AdminDashboardController::class, 'getMonthlyTrendsData'])->name('branches.analytics.monthly-trends');
    Route::get('branches/analytics/branch-comparison', [AdminDashboardController::class, 'getBranchComparisonData'])->name('branches.analytics.branch-comparison');
    Route::get('branches/performance/comparison', [BranchController::class, 'performance'])->name('branches.performance');
    Route::get('branches/{branch}/analytics', [BranchController::class, 'analytics'])->name('branches.analytics');
    Route::post('branches/{branch}/toggle-status', [BranchController::class, 'toggleStatus'])->name('branches.toggle-status');
    Route::resource('branches', BranchController::class);

    // Branch Sales Inventory Viewing (Sales Operations - View Access)
    Route::middleware(['store.vs.branch:branch-sales-view'])->group(function () {
        Route::get('branches/{branch}/sales-inventory', [BranchController::class, 'salesInventory'])->name('branches.sales-inventory');
    });

    // Branch Sales Inventory Adding Products (Sales Operations - Add Products Access)
    Route::middleware(['store.vs.branch:branch-sales-add-products'])->group(function () {
        Route::get('branches/{branch}/add-product', [BranchController::class, 'addProduct'])->name('branches.add-product');
        Route::post('branches/{branch}/store-product', [BranchController::class, 'storeProduct'])->name('branches.store-product');
    });

    // Branch Sales Inventory Management (Sales Operations - Full Management Access)
    Route::middleware(['store.vs.branch:branch-sales-inventory'])->group(function () {
        Route::get('branches/{branch}/receive-products', [BranchController::class, 'receiveProducts'])->name('branches.receive-products');
        Route::put('branches/{branch}/inventory/{inventory}/sales-prices', [BranchController::class, 'updateSalesPrices'])->name('branches.update-sales-prices');
        Route::put('branches/{branch}/inventory/{inventory}/min-stock', [BranchController::class, 'updateMinStock'])->name('branches.update-min-stock');
        Route::post('branches/{branch}/inventory/{inventory}/adjust', [BranchController::class, 'adjustInventory'])->name('branches.adjust-inventory');
    });

    // Store Management (Warehouse Operations)
    Route::middleware(['store.vs.branch:store'])->group(function () {
        Route::resource('stores', StoreController::class);
        Route::post('stores/{store}/toggle-status', [StoreController::class, 'toggleStatus'])->name('stores.toggle-status');
    });

    // Store Inventory Management (Warehouse Operations)
    Route::middleware(['store.vs.branch:store-inventory'])->group(function () {
        Route::get('stores/{store}/inventory', [StoreController::class, 'inventory'])->name('stores.inventory');
        Route::get('stores/{store}/add-product', [StoreController::class, 'addProduct'])->name('stores.add-product');
        Route::post('stores/{store}/store-product', [StoreController::class, 'storeProduct'])->name('stores.store-product');
        Route::put('stores/{store}/inventory/{inventory}', [StoreController::class, 'updateInventory'])->name('stores.update-inventory');
        Route::delete('stores/{store}/inventory/{inventory}', [StoreController::class, 'removeProduct'])->name('stores.remove-product');
    });

    // Inventory Transfer Management
    Route::resource('inventory-transfers', InventoryTransferController::class);

    // Transfer Approval (Warehouse Operations)
    Route::middleware(['store.vs.branch:transfer-approve'])->group(function () {
        Route::post('inventory-transfers/{inventoryTransfer}/approve', [InventoryTransferController::class, 'approve'])->name('inventory-transfers.approve');
        Route::post('inventory-transfers/{inventoryTransfer}/ship', [InventoryTransferController::class, 'ship'])->name('inventory-transfers.ship');
    });

    // Transfer Receiving (Both Store and Branch Operations)
    Route::post('inventory-transfers/{inventoryTransfer}/receive', [InventoryTransferController::class, 'receive'])->name('inventory-transfers.receive');
    Route::post('inventory-transfers/{inventoryTransfer}/cancel', [InventoryTransferController::class, 'cancel'])->name('inventory-transfers.cancel');
    Route::get('inventory/available-stock', [InventoryTransferController::class, 'getAvailableStock'])->name('inventory.available-stock');

    // Product Management
    Route::resource('products', ProductController::class);
    Route::get('products/bulk/create', [ProductController::class, 'bulkCreate'])->name('products.bulk.create');
    Route::post('products/bulk/store', [ProductController::class, 'bulkStore'])->name('products.bulk.store');
    Route::post('products/{product}/toggle-status', [ProductController::class, 'toggleStatus'])->name('products.toggle-status');
    Route::post('products/bulk/activate', [ProductController::class, 'bulkActivate'])->name('products.bulk.activate');
    Route::post('products/bulk/deactivate', [ProductController::class, 'bulkDeactivate'])->name('products.bulk.deactivate');
    Route::post('products/bulk/delete', [ProductController::class, 'bulkDelete'])->name('products.bulk.delete');
    Route::post('products/export', [ProductController::class, 'export'])->name('products.export');
    Route::post('products/{product}/sale-prices', [ProductController::class, 'getSalePrices'])->name('products.sale-prices');

    // Category Management
    Route::resource('categories', CategoryController::class);

    // Inventory Management Routes
    Route::prefix('inventory')->name('inventory.')->group(function () {
        Route::get('/overview', [InventoryController::class, 'overview'])->name('overview');
        Route::get('/branches', [InventoryController::class, 'branches'])->name('branches');
        Route::get('/stores', [InventoryController::class, 'stores'])->name('stores');
        Route::get('/low-stock', [InventoryController::class, 'lowStock'])->name('low-stock');
        Route::get('/branch/{branch}', [InventoryController::class, 'branchDetails'])->name('branch-details');
        Route::get('/store/{store}', [InventoryController::class, 'storeDetails'])->name('store-details');
    });

    // Direct Transfer Routes (Simplified)
    Route::prefix('transfers')->name('transfers.')->group(function () {
        Route::get('/direct/create', [DirectTransferController::class, 'create'])->name('direct.create');
        Route::post('/direct', [DirectTransferController::class, 'store'])->name('direct.store');
        Route::get('/history', [TransferController::class, 'history'])->name('history');
        Route::post('/available-quantity', [DirectTransferController::class, 'getAvailableQuantity'])->name('available-quantity');
        Route::post('/batch-available-quantities', [DirectTransferController::class, 'getBatchAvailableQuantities'])->name('batch-available-quantities');
        Route::get('/{transfer}', [DirectTransferController::class, 'show'])->name('show');
    });

    // Customer Management
    Route::resource('customers', CustomerController::class);

    // Supplier Management
    Route::resource('suppliers', SupplierController::class);

    // Purchase Management
    Route::resource('purchases', PurchaseController::class);
    Route::post('purchases/{purchase}/complete', [PurchaseController::class, 'complete'])->name('purchases.complete');
    Route::post('purchases/{purchase}/cancel', [PurchaseController::class, 'cancel'])->name('purchases.cancel');
    Route::get('purchases/{purchase}/print', [PurchaseController::class, 'print'])->name('purchases.print');

    // Purchase Distribution Management
    Route::get('purchases/{purchase}/distribute', [PurchaseDistributionController::class, 'show'])->name('purchases.distribute');
    Route::post('purchases/{purchase}/distribute', [PurchaseDistributionController::class, 'store'])->name('purchases.distribute.store');

    // Purchase Return Management
    Route::resource('purchase-returns', PurchaseReturnController::class);
    Route::post('purchase-returns/{purchaseReturn}/approve', [PurchaseReturnController::class, 'approve'])->name('purchase-returns.approve');
    Route::post('purchase-returns/{purchaseReturn}/complete', [PurchaseReturnController::class, 'complete'])->name('purchase-returns.complete');
    Route::post('purchase-returns/{purchaseReturn}/cancel', [PurchaseReturnController::class, 'cancel'])->name('purchase-returns.cancel');
    Route::get('purchases/{purchase}/details', [PurchaseReturnController::class, 'getPurchaseDetails'])->name('purchases.details');

    // Supplier Payment Management
    Route::prefix('supplier-payments')->name('supplier-payments.')->group(function () {
        Route::get('/', [SupplierPaymentController::class, 'index'])->name('index');
        Route::get('/purchase/{purchase}/pay', [SupplierPaymentController::class, 'create'])->name('create');
        Route::post('/purchase/{purchase}/pay', [SupplierPaymentController::class, 'store'])->name('store');
        Route::get('/purchase/{purchase}/history', [SupplierPaymentController::class, 'show'])->name('show');
        Route::get('/supplier/{supplier}/summary', [SupplierPaymentController::class, 'supplierSummary'])->name('supplier-summary');
    });

    // Payment Reports and Analytics
    Route::prefix('payment-reports')->name('payment-reports.')->group(function () {
        Route::get('/', [PaymentReportController::class, 'index'])->name('index');
        Route::get('/outstanding-balances', [PaymentReportController::class, 'outstandingBalances'])->name('outstanding-balances');
        Route::get('/payment-history', [PaymentReportController::class, 'paymentHistory'])->name('payment-history');
        Route::get('/supplier/{supplier}/summary', [PaymentReportController::class, 'supplierSummary'])->name('supplier-summary');
        Route::get('/export', [PaymentReportController::class, 'export'])->name('export');
    });

    // Inventory Transfer Reports and Analytics
    Route::prefix('inventory-transfer-reports')->name('inventory-transfer-reports.')->group(function () {
        Route::get('/', [InventoryTransferReportController::class, 'index'])->name('index');
        Route::get('/transfer-history', [InventoryTransferReportController::class, 'transferHistory'])->name('transfer-history');
        Route::get('/product-movements', [InventoryTransferReportController::class, 'productMovements'])->name('product-movements');
        Route::get('/location-performance', [InventoryTransferReportController::class, 'locationPerformance'])->name('location-performance');
        Route::get('/export', [InventoryTransferReportController::class, 'export'])->name('export');
    });

    // Sales Management (Admin can view all sales)
    Route::resource('sales', SaleController::class);
    Route::post('sales/{sale}/complete', [SaleController::class, 'complete'])->name('sales.complete');
    Route::post('sales/{sale}/cancel', [SaleController::class, 'cancel'])->name('sales.cancel');
    Route::get('sales/{sale}/print', [SaleController::class, 'print'])->name('sales.print');

    // Price-related endpoints
    Route::get('sales/product-price', [SaleController::class, 'getProductPrice'])->name('sales.product-price');
    Route::post('sales/validate-price', [SaleController::class, 'validatePrice'])->name('sales.validate-price');

    // Customer Payments Management
    Route::resource('customer-payments', CustomerPaymentController::class);
    Route::get('customer-payments/sale-details', [CustomerPaymentController::class, 'getSaleDetails'])->name('customer-payments.sale-details');

    // Sale Returns Management
    Route::get('sale-returns/sale-details', [SaleReturnController::class, 'getSaleDetails'])->name('sale-returns.sale-details');
    Route::resource('sale-returns', SaleReturnController::class);
    Route::post('sale-returns/{saleReturn}/approve', [SaleReturnController::class, 'approve'])->name('sale-returns.approve');
    Route::post('sale-returns/{saleReturn}/complete', [SaleReturnController::class, 'complete'])->name('sale-returns.complete');

    // Price Management
    Route::prefix('price-management')->name('price-management.')->group(function () {
        Route::get('/', [PriceManagementController::class, 'index'])->name('index');
        Route::get('/bulk-update', [PriceManagementController::class, 'bulkUpdate'])->name('bulk-update');
        Route::post('/bulk-update', [PriceManagementController::class, 'processBulkUpdate']);
        Route::get('/history/{product}', [PriceManagementController::class, 'priceHistory'])->name('history');
        Route::get('/suggestions', [PriceManagementController::class, 'getPriceSuggestions'])->name('suggestions');
        Route::get('/analytics', [PriceManagementController::class, 'analytics'])->name('analytics');
        Route::get('/export', [PriceManagementController::class, 'export'])->name('export');
    });

    // Expense Management
    Route::resource('expenses', ExpenseController::class);
    Route::get('expenses/export', [ExpenseController::class, 'export'])->name('expenses.export');
    Route::resource('expense-categories', ExpenseCategoryController::class);
    Route::get('expense-categories/export', [ExpenseCategoryController::class, 'export'])->name('expense-categories.export');
    Route::post('expense-categories/bulk-action', [ExpenseCategoryController::class, 'bulkAction'])->name('expense-categories.bulk-action');

    // Account Management
    Route::resource('accounts', AccountController::class);
    Route::resource('account-transactions', AccountTransactionController::class);

    // Reports (Admin has access to all reports)
    Route::prefix('reports')->name('reports.')->group(function () {
        Route::get('/sales', [ReportController::class, 'sales'])->name('sales');
        Route::get('/purchases', [ReportController::class, 'purchases'])->name('purchases');
        Route::get('/inventory', [ReportController::class, 'inventory'])->name('inventory');
        Route::get('/profit', [ReportController::class, 'profit'])->name('profit');
        Route::get('/financial', [ReportController::class, 'financial'])->name('financial');
        Route::get('/branch-performance', [ReportController::class, 'branchPerformance'])->name('branch-performance');
        Route::get('/user-activity', [ReportController::class, 'userActivity'])->name('user-activity');
    });

    // Settings (Admin only)
    Route::prefix('settings')->name('settings.')->group(function () {
        // User Management
        Route::get('/users', [SettingController::class, 'users'])->name('users');
        Route::get('/users/create', [SettingController::class, 'createUser'])->name('users.create');
        Route::post('/users', [SettingController::class, 'storeUser'])->name('users.store');
        Route::get('/users/{user}', [SettingController::class, 'showUser'])->name('users.show');
        Route::get('/users/{user}/edit', [SettingController::class, 'editUser'])->name('users.edit');
        Route::put('/users/{user}', [SettingController::class, 'updateUser'])->name('users.update');
        Route::delete('/users/{user}', [SettingController::class, 'destroyUser'])->name('users.destroy');

        // Role Management
        Route::get('/roles', [SettingController::class, 'roles'])->name('roles');
        Route::get('/roles/create', [SettingController::class, 'createRole'])->name('roles.create');
        Route::post('/roles', [SettingController::class, 'storeRole'])->name('roles.store');
        Route::get('/roles/{role}', [SettingController::class, 'showRole'])->name('roles.show');
        Route::get('/roles/{role}/edit', [SettingController::class, 'editRole'])->name('roles.edit');
        Route::put('/roles/{role}', [SettingController::class, 'updateRole'])->name('roles.update');
        Route::delete('/roles/{role}', [SettingController::class, 'destroyRole'])->name('roles.destroy');

        // Company Settings
        Route::get('/company', [SettingController::class, 'company'])->name('company');
        Route::post('/company', [SettingController::class, 'updateCompany'])->name('company.update');

        // System Settings
        Route::get('/system', [SettingController::class, 'system'])->name('system');
        Route::post('/system', [SettingController::class, 'updateSystem'])->name('system.update');

        // Backup & Restore
        Route::get('/backup', [SettingController::class, 'backup'])->name('backup');
        Route::post('/backup/create', [SettingController::class, 'createBackup'])->name('backup.create');
        Route::post('/backup/restore', [SettingController::class, 'restoreBackup'])->name('backup.restore');
    });
});
