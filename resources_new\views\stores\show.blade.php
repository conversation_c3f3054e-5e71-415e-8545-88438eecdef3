<x-app-layout>
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h2 class="h3 mb-0">تفاصيل المخزن: {{ $store->name }}</h2>
                    <div class="btn-group">
                        <a href="{{ user_route('stores.edit', $store) }}" class="btn btn-primary">
                            <i class="fas fa-edit"></i> تعديل
                        </a>
                        <a href="{{ user_route('stores.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> العودة للقائمة
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Store Information -->
            <div class="col-lg-8">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-info-circle text-primary"></i> معلومات المتجر
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td class="fw-bold text-muted">اسم المتجر:</td>
                                        <td>{{ $store->name }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold text-muted">كود المتجر:</td>
                                        <td><span class="badge bg-secondary">{{ $store->code }}</span></td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold text-muted">الفرع:</td>
                                        <td><span class="badge bg-info">{{ $store->branch->name }}</span></td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold text-muted">المدير:</td>
                                        <td>
                                            @if ($store->manager)
                                                <i class="fas fa-user-tie text-primary"></i> {{ $store->manager->name }}
                                            @else
                                                <span class="text-muted">غير محدد</span>
                                            @endif
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold text-muted">الحالة:</td>
                                        <td>
                                            @if ($store->is_active)
                                                <span class="badge bg-success">نشط</span>
                                            @else
                                                <span class="badge bg-danger">غير نشط</span>
                                            @endif
                                        </td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td class="fw-bold text-muted">العنوان:</td>
                                        <td>{{ $store->address ?: 'غير محدد' }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold text-muted">الهاتف:</td>
                                        <td>
                                            @if ($store->phone)
                                                <a href="tel:{{ $store->phone }}" class="text-decoration-none">
                                                    <i class="fas fa-phone text-success"></i> {{ $store->phone }}
                                                </a>
                                            @else
                                                <span class="text-muted">غير محدد</span>
                                            @endif
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold text-muted">البريد الإلكتروني:</td>
                                        <td>
                                            @if ($store->email)
                                                <a href="mailto:{{ $store->email }}" class="text-decoration-none">
                                                    <i class="fas fa-envelope text-info"></i> {{ $store->email }}
                                                </a>
                                            @else
                                                <span class="text-muted">غير محدد</span>
                                            @endif
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold text-muted">تاريخ الإنشاء:</td>
                                        <td>{{ $store->created_at }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold text-muted">آخر تحديث:</td>
                                        <td>{{ $store->updated_at }}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>

                        @if ($store->description)
                            <div class="mt-3">
                                <h6 class="fw-bold text-muted">الوصف:</h6>
                                <p class="text-muted">{{ $store->description }}</p>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Store Users -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-users text-primary"></i> موظفو المتجر
                        </h5>
                    </div>
                    <div class="card-body">
                        @if ($store->users->count() > 0)
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead class="table-light">
                                        <tr>
                                            <th>الاسم</th>
                                            <th>البريد الإلكتروني</th>
                                            <th>الدور</th>
                                            <th>الحالة</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach ($store->users as $user)
                                            <tr>
                                                <td>{{ $user->name }}</td>
                                                <td>{{ $user->email }}</td>
                                                <td><span class="badge bg-info">{{ $user->role }}</span></td>
                                                <td>
                                                    @if ($user->is_active)
                                                        <span class="badge bg-success">نشط</span>
                                                    @else
                                                        <span class="badge bg-danger">غير نشط</span>
                                                    @endif
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @else
                            <div class="text-center py-3">
                                <i class="fas fa-users fa-2x text-muted mb-2"></i>
                                <p class="text-muted">لا يوجد موظفون مسجلون في هذا المتجر</p>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Store Inventory Overview -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-warehouse text-primary"></i> نظرة عامة على المخزون
                        </h5>
                    </div>
                    <div class="card-body">
                        @if ($store->storeInventories->count() > 0)
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>المنتج</th>
                                            <th>الكمية المتاحة</th>
                                            <th>الحد الأدنى</th>
                                            <th>الحد الأقصى</th>
                                            <th>حالة المخزون</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach ($store->storeInventories->take(5) as $inventory)
                                            <tr
                                                class="{{ $inventory->isLowStock() ? 'table-warning' : ($inventory->quantity <= 0 ? 'table-danger' : '') }}">
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <div>
                                                            <div class="fw-bold">{{ $inventory->product->name }}</div>
                                                            @if ($inventory->product->sku)
                                                                <small
                                                                    class="text-muted">{{ $inventory->product->sku }}</small>
                                                            @endif
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <span
                                                        class="fw-bold">{{ number_format($inventory->quantity, 2) }}</span>
                                                </td>
                                                <td>{{ number_format($inventory->minimum_stock, 2) }}</td>
                                                <td>{{ $inventory->maximum_stock ? number_format($inventory->maximum_stock, 2) : 'غير محدد' }}
                                                </td>
                                                <td>
                                                    <span class="badge {{ $inventory->getStockStatusBadgeClass() }}">
                                                        {{ $inventory->getStockStatusText() }}
                                                    </span>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                            @if ($store->storeInventories->count() > 5)
                                <div class="text-center mt-3">
                                    <a href="{{ route('admin.stores.inventory', $store) }}"
                                        class="btn btn-outline-primary">
                                        <i class="fas fa-eye"></i> عرض جميع المنتجات
                                        ({{ $store->storeInventories->count() }})
                                    </a>
                                </div>
                            @endif
                        @else
                            <div class="text-center py-4">
                                <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">لا يوجد منتجات في المخزون</h5>
                                <p class="text-muted">ابدأ بإضافة منتجات إلى هذا المخزن</p>
                                <a href="{{ route('admin.stores.add-product', $store) }}" class="btn btn-primary">
                                    <i class="fas fa-plus"></i> إضافة منتجات
                                </a>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Statistics Sidebar -->
            <div class="col-lg-4">
                <!-- Statistics Cards -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-chart-bar text-primary"></i> إحصائيات المتجر
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6 mb-3">
                                <div class="border rounded p-3">
                                    <h4 class="text-primary mb-1">{{ $stats['total_products'] }}</h4>
                                    <small class="text-muted">إجمالي المنتجات</small>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="border rounded p-3">
                                    <h4 class="text-warning mb-1">{{ $stats['low_stock_count'] }}</h4>
                                    <small class="text-muted">مخزون منخفض</small>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="border rounded p-3">
                                    <h4 class="text-danger mb-1">{{ $stats['out_of_stock_count'] }}</h4>
                                    <small class="text-muted">نفد المخزون</small>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="border rounded p-3">
                                    <h4 class="text-info mb-1">{{ $stats['total_users'] }}</h4>
                                    <small class="text-muted">الموظفون</small>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="border rounded p-3">
                                    <h4 class="text-success mb-1">
                                        {{ format_currency($stats['total_inventory_value']) }}</h4>
                                    <small class="text-muted">قيمة المخزون الإجمالية</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-bolt text-primary"></i> إجراءات سريعة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="{{ user_route('stores.edit', $store) }}" class="btn btn-outline-primary">
                                <i class="fas fa-edit"></i> تعديل المخزن
                            </a>
                            <a href="{{ route('admin.stores.inventory', $store) }}" class="btn btn-outline-info">
                                <i class="fas fa-warehouse"></i> إدارة المخزون
                            </a>
                            <a href="{{ route('admin.stores.add-product', $store) }}"
                                class="btn btn-outline-success">
                                <i class="fas fa-plus"></i> إضافة منتجات
                            </a>
                            <a href="{{ route('admin.inventory-transfers.create') }}?source_type=store&source_id={{ $store->id }}"
                                class="btn btn-outline-warning">
                                <i class="fas fa-exchange-alt"></i> نقل منتجات
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
