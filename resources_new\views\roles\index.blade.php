<x-app-layout>
    <x-slot name="header">
        <div class="d-flex justify-content-between align-items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('الأدوار') }}
            </h2>
            <div>
                <a href="{{ route('settings.roles.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> {{ __('إضافة دور') }}
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="container-fluid">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    <!-- Filters -->
                    <form action="{{ route('settings.roles') }}" method="GET" class="mb-4">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="search">{{ __('بحث') }}</label>
                                    <input type="text" name="search" id="search" class="form-control" value="{{ request('search') }}" placeholder="{{ __('اسم الدور') }}">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="sort">{{ __('ترتيب حسب') }}</label>
                                    <select name="sort" id="sort" class="form-select">
                                        <option value="name_asc" {{ request('sort') == 'name_asc' ? 'selected' : '' }}>{{ __('الاسم (أ-ي)') }}</option>
                                        <option value="name_desc" {{ request('sort') == 'name_desc' ? 'selected' : '' }}>{{ __('الاسم (ي-أ)') }}</option>
                                        <option value="users_count_asc" {{ request('sort') == 'users_count_asc' ? 'selected' : '' }}>{{ __('عدد المستخدمين (تصاعدي)') }}</option>
                                        <option value="users_count_desc" {{ request('sort') == 'users_count_desc' ? 'selected' : '' }}>{{ __('عدد المستخدمين (تنازلي)') }}</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="permission">{{ __('الصلاحية') }}</label>
                                    <select name="permission" id="permission" class="form-select">
                                        <option value="">{{ __('الكل') }}</option>
                                        @foreach($permissions as $permission)
                                            <option value="{{ $permission->id }}" {{ request('permission') == $permission->id ? 'selected' : '' }}>
                                                {{ $permission->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12 text-start">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i> {{ __('بحث') }}
                                </button>
                                <a href="{{ route('settings.roles') }}" class="btn btn-secondary">
                                    <i class="fas fa-redo"></i> {{ __('إعادة تعيين') }}
                                </a>
                            </div>
                        </div>
                    </form>

                    <!-- Roles Table -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead>
                                <tr>
                                    <th>{{ __('اسم الدور') }}</th>
                                    <th>{{ __('الوصف') }}</th>
                                    <th>{{ __('الصلاحيات') }}</th>
                                    <th>{{ __('عدد المستخدمين') }}</th>
                                    <th>{{ __('تاريخ الإنشاء') }}</th>
                                    <th>{{ __('الإجراءات') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($roles as $role)
                                    <tr>
                                        <td>{{ $role->name }}</td>
                                        <td>{{ $role->description }}</td>
                                        <td>
                                            @foreach($role->permissions as $permission)
                                                <span class="badge bg-info">{{ $permission->name }}</span>
                                            @endforeach
                                        </td>
                                        <td>{{ $role->users_count }}</td>
                                        <td>{{ $role->created_at->format('Y-m-d') }}</td>
                                        <td>
                                            <div class="btn-group">
                                                <a href="{{ route('roles.show', $role) }}" class="btn btn-info btn-sm">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{{ route('roles.edit', $role) }}" class="btn btn-primary btn-sm">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                @if($role->users_count == 0)
                                                    <button type="button" class="btn btn-danger btn-sm" onclick="confirmDelete('{{ route('roles.destroy', $role) }}')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="6" class="text-center">{{ __('لا توجد بيانات') }}</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="mt-4">
                        {{ $roles->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>

    @push('styles')
    <style>
        .rtl {
            direction: rtl;
            text-align: right;
        }
        .rtl .form-select {
            text-align: right;
        }
        .rtl .table th,
        .rtl .table td {
            text-align: right;
        }
        .rtl .text-start {
            text-align: right !important;
        }
        .rtl .text-end {
            text-align: left !important;
        }
        .rtl .form-control {
            text-align: right;
        }
        .rtl .form-control::placeholder {
            text-align: right;
        }
        .rtl .btn-group {
            direction: ltr;
        }
    </style>
    @endpush

    @push('scripts')
    <script>
        function confirmDelete(url) {
            if (confirm('{{ __('هل أنت متأكد من حذف هذا الدور؟') }}')) {
                const form = document.createElement('form');
                form.action = url;
                form.method = 'POST';
                form.innerHTML = `
                    @csrf
                    @method('DELETE')
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }
    </script>
    @endpush
</x-app-layout>
