<?php

namespace Tests\Feature;

use App\Models\Branch;
use App\Models\BranchInventory;
use App\Models\Customer;
use App\Models\Product;
use App\Models\Sale;
use App\Models\SaleItem;
use App\Models\SaleReturn;
use App\Models\SaleReturnItem;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SalesStatisticsWithReturnsTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $branch;
    protected $customer;
    protected $product;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test data
        $this->user = User::factory()->create();
        $this->branch = Branch::factory()->create();
        $this->customer = Customer::factory()->create();
        $this->product = Product::factory()->create();

        // Create branch inventory
        BranchInventory::create([
            'branch_id' => $this->branch->id,
            'product_id' => $this->product->id,
            'quantity' => 100,
            'min_quantity' => 10,
        ]);
    }

    /** @test */
    public function it_calculates_daily_sales_correctly_with_returns()
    {
        // Create a sale for today
        $sale = Sale::create([
            'invoice_number' => 'INV-001',
            'customer_id' => $this->customer->id,
            'branch_id' => $this->branch->id,
            'user_id' => $this->user->id,
            'total_amount' => 1000.00,
            'discount_amount' => 0,
            'status' => 'completed',
            'created_at' => today(),
        ]);

        SaleItem::create([
            'sale_id' => $sale->id,
            'product_id' => $this->product->id,
            'quantity' => 20,
            'price' => 50.00,
            'subtotal' => 1000.00,
        ]);

        // Check initial daily sales
        $initialDailySales = Sale::whereDate('created_at', today())->sum('total_amount');
        $this->assertEquals(1000.00, $initialDailySales);

        // Create a return for today
        $saleReturn = SaleReturn::create([
            'sale_id' => $sale->id,
            'customer_id' => $this->customer->id,
            'branch_id' => $this->branch->id,
            'user_id' => $this->user->id,
            'total_amount' => 300.00,
            'refund_amount' => 300.00,
            'status' => 'completed',
            'return_type' => 'partial',
            'reason' => 'customer_request',
            'return_date' => today(),
            'created_at' => today(),
        ]);

        SaleReturnItem::create([
            'sale_return_id' => $saleReturn->id,
            'sale_item_id' => $sale->items->first()->id,
            'product_id' => $this->product->id,
            'quantity_returned' => 6,
            'original_quantity' => 20,
            'sale_price' => 50.00,
            'total_amount' => 300.00,
            'condition' => 'good',
        ]);

        // Calculate net daily sales (should be 1000 - 300 = 700)
        $grossSales = Sale::whereDate('created_at', today())->sum('total_amount');
        $dailyReturns = SaleReturn::whereDate('created_at', today())
            ->where('status', 'completed')
            ->sum('total_amount');
        $netDailySales = $grossSales - $dailyReturns;

        $this->assertEquals(1000.00, $grossSales);
        $this->assertEquals(300.00, $dailyReturns);
        $this->assertEquals(700.00, $netDailySales);
    }

    /** @test */
    public function it_calculates_monthly_sales_correctly_with_returns()
    {
        // Create a sale for this month
        $sale = Sale::create([
            'invoice_number' => 'INV-002',
            'customer_id' => $this->customer->id,
            'branch_id' => $this->branch->id,
            'user_id' => $this->user->id,
            'total_amount' => 2000.00,
            'discount_amount' => 0,
            'status' => 'completed',
            'created_at' => now()->startOfMonth(),
        ]);

        SaleItem::create([
            'sale_id' => $sale->id,
            'product_id' => $this->product->id,
            'quantity' => 40,
            'price' => 50.00,
            'subtotal' => 2000.00,
        ]);

        // Create a return for this month
        $saleReturn = SaleReturn::create([
            'sale_id' => $sale->id,
            'customer_id' => $this->customer->id,
            'branch_id' => $this->branch->id,
            'user_id' => $this->user->id,
            'total_amount' => 500.00,
            'refund_amount' => 500.00,
            'status' => 'completed',
            'return_type' => 'partial',
            'reason' => 'customer_request',
            'return_date' => now()->startOfMonth(),
            'created_at' => now()->startOfMonth(),
        ]);

        SaleReturnItem::create([
            'sale_return_id' => $saleReturn->id,
            'sale_item_id' => $sale->items->first()->id,
            'product_id' => $this->product->id,
            'quantity_returned' => 10,
            'original_quantity' => 40,
            'sale_price' => 50.00,
            'total_amount' => 500.00,
            'condition' => 'good',
        ]);

        // Calculate net monthly sales (should be 2000 - 500 = 1500)
        $grossMonthlySales = Sale::whereMonth('created_at', now()->month)
            ->whereYear('created_at', now()->year)
            ->sum('total_amount');
            
        $monthlyReturns = SaleReturn::whereMonth('created_at', now()->month)
            ->whereYear('created_at', now()->year)
            ->where('status', 'completed')
            ->sum('total_amount');
            
        $netMonthlySales = $grossMonthlySales - $monthlyReturns;

        $this->assertEquals(2000.00, $grossMonthlySales);
        $this->assertEquals(500.00, $monthlyReturns);
        $this->assertEquals(1500.00, $netMonthlySales);
    }

    /** @test */
    public function branch_model_calculates_sales_with_returns_correctly()
    {
        // Create a sale for the branch
        $sale = Sale::create([
            'invoice_number' => 'INV-003',
            'customer_id' => $this->customer->id,
            'branch_id' => $this->branch->id,
            'user_id' => $this->user->id,
            'total_amount' => 1500.00,
            'discount_amount' => 0,
            'status' => 'completed',
            'created_at' => today(),
        ]);

        SaleItem::create([
            'sale_id' => $sale->id,
            'product_id' => $this->product->id,
            'quantity' => 30,
            'price' => 50.00,
            'subtotal' => 1500.00,
        ]);

        // Create a return for the branch
        $saleReturn = SaleReturn::create([
            'sale_id' => $sale->id,
            'customer_id' => $this->customer->id,
            'branch_id' => $this->branch->id,
            'user_id' => $this->user->id,
            'total_amount' => 250.00,
            'refund_amount' => 250.00,
            'status' => 'completed',
            'return_type' => 'partial',
            'reason' => 'customer_request',
            'return_date' => today(),
            'created_at' => today(),
        ]);

        SaleReturnItem::create([
            'sale_return_id' => $saleReturn->id,
            'sale_item_id' => $sale->items->first()->id,
            'product_id' => $this->product->id,
            'quantity_returned' => 5,
            'original_quantity' => 30,
            'sale_price' => 50.00,
            'total_amount' => 250.00,
            'condition' => 'good',
        ]);

        // Refresh the branch to load relationships
        $this->branch->load(['sales', 'saleReturns']);

        // Test branch computed properties
        $this->assertEquals(1250.00, $this->branch->total_sales); // 1500 - 250
        $this->assertEquals(1250.00, $this->branch->today_sales); // 1500 - 250
        $this->assertEquals(1250.00, $this->branch->month_sales); // 1500 - 250
    }

    /** @test */
    public function cancelled_returns_do_not_affect_sales_statistics()
    {
        // Create a sale
        $sale = Sale::create([
            'invoice_number' => 'INV-004',
            'customer_id' => $this->customer->id,
            'branch_id' => $this->branch->id,
            'user_id' => $this->user->id,
            'total_amount' => 800.00,
            'discount_amount' => 0,
            'status' => 'completed',
            'created_at' => today(),
        ]);

        SaleItem::create([
            'sale_id' => $sale->id,
            'product_id' => $this->product->id,
            'quantity' => 16,
            'price' => 50.00,
            'subtotal' => 800.00,
        ]);

        // Create a cancelled return
        $saleReturn = SaleReturn::create([
            'sale_id' => $sale->id,
            'customer_id' => $this->customer->id,
            'branch_id' => $this->branch->id,
            'user_id' => $this->user->id,
            'total_amount' => 200.00,
            'refund_amount' => 200.00,
            'status' => 'cancelled', // Cancelled status
            'return_type' => 'partial',
            'reason' => 'customer_request',
            'return_date' => today(),
            'created_at' => today(),
        ]);

        // Calculate net daily sales (should be 800 - 0 = 800, since return is cancelled)
        $grossSales = Sale::whereDate('created_at', today())->sum('total_amount');
        $dailyReturns = SaleReturn::whereDate('created_at', today())
            ->where('status', 'completed') // Only completed returns
            ->sum('total_amount');
        $netDailySales = $grossSales - $dailyReturns;

        $this->assertEquals(800.00, $grossSales);
        $this->assertEquals(0.00, $dailyReturns); // No completed returns
        $this->assertEquals(800.00, $netDailySales);
    }
}
