<x-app-layout>
    <div class="container-fluid">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">تفاصيل الفرع: {{ $branch->name }}</h5>
                <div>
                    @if (auth()->user()->isAdmin())
                        <a href="{{ user_route('branches.edit', $branch) }}" class="btn btn-primary">
                            <i class="fas fa-edit"></i> تعديل
                        </a>
                        <a href="{{ user_route('branches.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-right"></i> عودة
                        </a>
                    @else
                        <a href="{{ user_route('dashboard') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-right"></i> عودة للوحة التحكم
                        </a>
                    @endif
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-bordered">
                            <tr>
                                <th>الكود</th>
                                <td>{{ $branch->code }}</td>
                            </tr>
                            <tr>
                                <th>الاسم</th>
                                <td>{{ $branch->name }}</td>
                            </tr>
                            <tr>
                                <th>العنوان</th>
                                <td>{{ $branch->address ?? '-' }}</td>
                            </tr>
                            <tr>
                                <th>الهاتف</th>
                                <td>{{ $branch->phone ?? '-' }}</td>
                            </tr>
                            <tr>
                                <th>البريد الإلكتروني</th>
                                <td>{{ $branch->email ?? '-' }}</td>
                            </tr>
                            <tr>
                                <th>الحالة</th>
                                <td>
                                    <span class="badge bg-{{ $branch->is_active ? 'success' : 'danger' }}">
                                        {{ $branch->is_active ? 'نشط' : 'غير نشط' }}
                                    </span>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>

                <!-- Quick Actions for Sales Management -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="card-title mb-3">
                                    <i class="fas fa-store"></i> إدارة مبيعات الفرع
                                </h6>
                                <div class="row g-2">
                                    <div class="col-md-3">
                                        <a href="{{ route('admin.branches.sales-inventory', $branch) }}"
                                            class="btn btn-outline-primary w-100">
                                            <i class="fas fa-boxes"></i> مخزون المبيعات
                                        </a>
                                    </div>
                                    <div class="col-md-3">
                                        <a href="{{ route('admin.branches.receive-products', $branch) }}"
                                            class="btn btn-outline-success w-100">
                                            <i class="fas fa-truck"></i> استلام منتجات
                                        </a>
                                    </div>
                                    <div class="col-md-3">
                                        <a href="{{ route('admin.inventory-transfers.create') }}?destination_type=branch&destination_id={{ $branch->id }}"
                                            class="btn btn-outline-warning w-100">
                                            <i class="fas fa-exchange-alt"></i> طلب نقل
                                        </a>
                                    </div>
                                    <div class="col-md-3">
                                        <a href="{{ route('admin.branches.analytics', $branch) }}"
                                            class="btn btn-outline-info w-100">
                                            <i class="fas fa-chart-line"></i> تحليلات المبيعات
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-12">
                        <ul class="nav nav-tabs" id="branchTabs" role="tablist">
                            <li class="nav-item">
                                <a class="nav-link active" id="inventory-tab" data-bs-toggle="tab" href="#inventory"
                                    role="tab">
                                    المخزون
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="sales-tab" data-bs-toggle="tab" href="#sales" role="tab">
                                    المبيعات
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="purchases-tab" data-bs-toggle="tab" href="#purchases"
                                    role="tab">
                                    المشتريات
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="expenses-tab" data-bs-toggle="tab" href="#expenses"
                                    role="tab">
                                    المصروفات
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="cash-tab" data-bs-toggle="tab" href="#cash" role="tab">
                                    المعاملات النقدية
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="users-tab" data-bs-toggle="tab" href="#users" role="tab">
                                    المستخدمين
                                </a>
                            </li>
                        </ul>

                        <div class="tab-content mt-3" id="branchTabsContent">
                            <div class="tab-pane fade show active" id="inventory" role="tabpanel">
                                <div class="row mb-3">
                                    <div class="col-md-3">
                                        <div class="card bg-primary text-white">
                                            <div class="card-body text-center">
                                                <h5>{{ $branch->branchInventories->count() }}</h5>
                                                <small>إجمالي المنتجات</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card bg-warning text-white">
                                            <div class="card-body text-center">
                                                <h5>{{ $branch->branchInventories->where('quantity', '<=', 10)->count() }}
                                                </h5>
                                                <small>منتجات قليلة المخزون</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card bg-danger text-white">
                                            <div class="card-body text-center">
                                                <h5>{{ $branch->branchInventories->where('quantity', '<=', 0)->count() }}
                                                </h5>
                                                <small>منتجات نفدت</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card bg-success text-white">
                                            <div class="card-body text-center">
                                                <h5>{{ format_currency($branch->branchInventories->sum(function ($item) {return $item->quantity * ($item->cost_price ?? 0);})) }}
                                                </h5>
                                                <small>قيمة المخزون</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="table-responsive">
                                    <table class="table table-bordered table-hover">
                                        <thead class="table-light">
                                            <tr>
                                                <th>المنتج</th>
                                                <th>الكمية</th>
                                                <th>سعر التكلفة</th>
                                                <th>سعر البيع 1</th>
                                                <th>سعر البيع 2</th>
                                                <th>سعر البيع 3</th>
                                                <th>إجمالي القيمة</th>
                                                <th>الحالة</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @forelse($branch->branchInventories as $inventory)
                                                <tr
                                                    class="{{ $inventory->quantity <= 0 ? 'table-danger' : ($inventory->quantity <= 10 ? 'table-warning' : '') }}">
                                                    <td>
                                                        <strong>{{ $inventory->product->name }}</strong>
                                                        <br><small
                                                            class="text-muted">{{ $inventory->product->sku }}</small>
                                                    </td>
                                                    <td>
                                                        <span
                                                            class="badge bg-{{ $inventory->quantity > 10 ? 'success' : ($inventory->quantity > 0 ? 'warning' : 'danger') }}">
                                                            {{ $inventory->quantity }}
                                                        </span>
                                                    </td>
                                                    <td>{{ format_currency($inventory->cost_price ?? 0) }}</td>
                                                    <td>{{ format_currency($inventory->sale_price_1 ?? 0) }}</td>
                                                    <td>{{ $inventory->sale_price_2 ? format_currency($inventory->sale_price_2) : '-' }}
                                                    </td>
                                                    <td>{{ $inventory->sale_price_3 ? format_currency($inventory->sale_price_3) : '-' }}
                                                    </td>
                                                    <td>{{ format_currency(($inventory->cost_price ?? 0) * $inventory->quantity) }}
                                                    </td>
                                                    <td>
                                                        @if ($inventory->quantity <= 0)
                                                            <span class="badge bg-danger">نفد المخزون</span>
                                                        @elseif($inventory->quantity <= 10)
                                                            <span class="badge bg-warning">مخزون قليل</span>
                                                        @else
                                                            <span class="badge bg-success">متوفر</span>
                                                        @endif
                                                    </td>
                                                </tr>
                                            @empty
                                                <tr>
                                                    <td colspan="8" class="text-center py-4">
                                                        <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                                                        <p class="text-muted">لا يوجد مخزون في هذا الفرع</p>
                                                        <a href="{{ route('admin.branches.receive-products', $branch) }}"
                                                            class="btn btn-primary">
                                                            <i class="fas fa-plus"></i> إضافة منتجات
                                                        </a>
                                                    </td>
                                                </tr>
                                            @endforelse
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <div class="tab-pane fade" id="sales" role="tabpanel">
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <thead>
                                            <tr>
                                                <th>رقم الفاتورة</th>
                                                <th>التاريخ</th>
                                                <th>المبلغ</th>
                                                <th>الحالة</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @forelse($branch->sales as $sale)
                                                <tr>
                                                    <td>{{ $sale->invoice_number }}</td>
                                                    <td>{{ $sale->sale_date }}</td>
                                                    <td>{{ number_format($sale->total_amount, 2) }}</td>
                                                    <td>
                                                        <span
                                                            class="badge bg-{{ $sale->status === 'completed' ? 'success' : ($sale->status === 'pending' ? 'warning' : 'danger') }}">
                                                            {{ $sale->status === 'completed' ? 'مكتمل' : ($sale->status === 'pending' ? 'قيد الانتظار' : 'ملغي') }}
                                                        </span>
                                                    </td>
                                                </tr>
                                            @empty
                                                <tr>
                                                    <td colspan="4" class="text-center">لا توجد مبيعات</td>
                                                </tr>
                                            @endforelse
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <div class="tab-pane fade" id="purchases" role="tabpanel">
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <thead>
                                            <tr>
                                                <th>رقم الفاتورة</th>
                                                <th>المورد</th>
                                                <th>التاريخ</th>
                                                <th>المبلغ</th>
                                                <th>الحالة</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @forelse($branch->purchases as $purchase)
                                                <tr>
                                                    <td>{{ $purchase->invoice_number }}</td>
                                                    <td>{{ $purchase->supplier->name }}</td>
                                                    <td>{{ $purchase->purchase_date }}</td>
                                                    <td>{{ number_format($purchase->total_amount, 2) }}</td>
                                                    <td>
                                                        <span
                                                            class="badge bg-{{ $purchase->status === 'completed' ? 'success' : ($purchase->status === 'pending' ? 'warning' : 'danger') }}">
                                                            {{ $purchase->status === 'completed' ? 'مكتمل' : ($purchase->status === 'pending' ? 'قيد الانتظار' : 'ملغي') }}
                                                        </span>
                                                    </td>
                                                </tr>
                                            @empty
                                                <tr>
                                                    <td colspan="5" class="text-center">لا توجد مشتريات</td>
                                                </tr>
                                            @endforelse
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <div class="tab-pane fade" id="expenses" role="tabpanel">
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <thead>
                                            <tr>
                                                <th>رقم المرجع</th>
                                                <th>التاريخ</th>
                                                <th>الفئة</th>
                                                <th>المبلغ</th>
                                                <th>طريقة الدفع</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @forelse($branch->expenses as $expense)
                                                <tr>
                                                    <td>{{ $expense->reference_number }}</td>
                                                    <td>{{ $expense->expense_date->format('Y-m-d') }}</td>
                                                    <td>{{ $expense->category->name }}</td>
                                                    <td>{{ number_format($expense->amount, 2) }}</td>
                                                    <td>{{ $expense->payment_method === 'cash' ? 'نقدي' : 'بنكي' }}
                                                    </td>
                                                </tr>
                                            @empty
                                                <tr>
                                                    <td colspan="5" class="text-center">لا توجد مصروفات</td>
                                                </tr>
                                            @endforelse
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <div class="tab-pane fade" id="cash" role="tabpanel">
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <thead>
                                            <tr>
                                                <th>التاريخ</th>
                                                <th>النوع</th>
                                                <th>المبلغ</th>
                                                <th>الملاحظات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @forelse($branch->cashTransactions as $transaction)
                                                <tr>
                                                    <td>{{ $transaction->created_at->format('Y-m-d H:i') }}</td>
                                                    <td>
                                                        @switch($transaction->type)
                                                            @case('opening')
                                                                رصيد افتتاحي
                                                            @break

                                                            @case('closing')
                                                                رصيد إغلاق
                                                            @break

                                                            @case('sale')
                                                                مبيعات
                                                            @break

                                                            @case('purchase')
                                                                مشتريات
                                                            @break

                                                            @case('expense')
                                                                مصروفات
                                                            @break

                                                            @case('transfer')
                                                                تحويل
                                                            @break
                                                        @endswitch
                                                    </td>
                                                    <td
                                                        class="{{ $transaction->amount >= 0 ? 'text-success' : 'text-danger' }}">
                                                        {{ number_format($transaction->amount, 2) }}
                                                    </td>
                                                    <td>{{ $transaction->notes }}</td>
                                                </tr>
                                                @empty
                                                    <tr>
                                                        <td colspan="4" class="text-center">لا توجد معاملات نقدية</td>
                                                    </tr>
                                                @endforelse
                                            </tbody>
                                        </table>
                                    </div>
                                </div>

                                <div class="tab-pane fade" id="users" role="tabpanel">
                                    <div class="table-responsive">
                                        <table class="table table-bordered">
                                            <thead>
                                                <tr>
                                                    <th>الاسم</th>
                                                    <th>البريد الإلكتروني</th>
                                                    <th>الدور</th>
                                                    <th>الحالة</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @forelse($branch->users as $user)
                                                    <tr>
                                                        <td>{{ $user->name }}</td>
                                                        <td>{{ $user->email }}</td>
                                                        <td>
                                                            @switch($user->role)
                                                                @case('admin')
                                                                    مدير النظام
                                                                @break

                                                                @case('manager')
                                                                    مدير فرع
                                                                @break

                                                                @case('staff')
                                                                    موظف
                                                                @break
                                                            @endswitch
                                                        </td>
                                                        <td>
                                                            <span
                                                                class="badge bg-{{ $user->is_active ? 'success' : 'danger' }}">
                                                                {{ $user->is_active ? 'نشط' : 'غير نشط' }}
                                                            </span>
                                                        </td>
                                                    </tr>
                                                    @empty
                                                        <tr>
                                                            <td colspan="4" class="text-center">لا يوجد مستخدمين</td>
                                                        </tr>
                                                    @endforelse
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </x-app-layout>
