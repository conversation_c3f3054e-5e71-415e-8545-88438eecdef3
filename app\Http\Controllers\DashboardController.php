<?php

namespace App\Http\Controllers;

use App\Models\Branch;
use App\Models\Category;
use App\Models\Customer;
use App\Models\Product;
use App\Models\Sale;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class DashboardController extends Controller
{
    public function index(Request $request)
    {
        $user = $request->user()->load('role');

        // Debug: Log user role information
        Log::info('Dashboard access attempt', [
            'user_id' => $user->id,
            'user_name' => $user->name,
            'role_id' => $user->role_id,
            'role_name' => $user->role ? $user->role->name : 'no role',
            'isAdmin' => $user->isAdmin(),
            'isSeller' => $user->isSeller(),
        ]);

        // Redirect users to their role-specific dashboards
        if ($user->isAdmin()) {
            return redirect()->route('admin.dashboard');
        } elseif ($user->isSeller()) {
            return redirect()->route('seller.dashboard');
        }

        // Fallback for users without specific roles - show general dashboard
        // Get total counts
        $totalProducts = Product::count();
        $totalCategories = Category::count();
        $totalCustomers = Customer::count();
        $totalSales = Sale::count();
        $totalBranches = Branch::count();

        // Calculate total returns
        $totalReturns = \App\Models\SaleReturn::where('status', 'completed')->sum('total_amount');
        $todayReturns = \App\Models\SaleReturn::whereDate('created_at', today())
            ->where('status', 'completed')
            ->sum('total_amount');

        // Get total sales amount (minus returns)
        $totalSalesAmount = Sale::where('status', 'completed')->sum('total_amount') - $totalReturns;

        // Get today's sales (minus returns)
        $todaySales = Sale::where('status', 'completed')
            ->whereDate('created_at', today())
            ->sum('total_amount') - $todayReturns;

        // Get recent sales
        $recentSales = Sale::with(['customer', 'products'])
            ->latest()
            ->take(5)
            ->get();

        // Get low stock products
        $lowStockProducts = Product::whereHas('branchInventories', function ($query) {
            $query->where('quantity', '<', 10);
        })
            ->with(['branchInventories' => function ($query) {
                $query->where('quantity', '<', 10);
            }])
            ->take(5)
            ->get();

        // Get top selling products
        $topSellingProducts = Product::withCount(['sales' => function ($query) {
            $query->where('status', 'completed');
        }])
            ->orderByDesc('sales_count')
            ->take(5)
            ->get();

        // Get sales by status
        $salesByStatus = Sale::select('status', DB::raw('count(*) as count'))
            ->groupBy('status')
            ->get();

        // Get monthly sales data for the last 6 months (including returns adjustment)
        $monthlySales = Sale::where('status', 'completed')
            ->where('created_at', '>=', now()->subMonths(6))
            ->select(
                DB::raw('DATE_FORMAT(created_at, "%Y-%m") as month'),
                DB::raw('SUM(total_amount) as total')
            )
            ->groupBy('month')
            ->orderBy('month')
            ->get();

        // Get monthly returns data for the last 6 months
        $monthlyReturns = \App\Models\SaleReturn::where('status', 'completed')
            ->where('created_at', '>=', now()->subMonths(6))
            ->select(
                DB::raw('DATE_FORMAT(created_at, "%Y-%m") as month'),
                DB::raw('SUM(total_amount) as total')
            )
            ->groupBy('month')
            ->orderBy('month')
            ->get()
            ->keyBy('month');

        // Adjust monthly sales by subtracting returns
        $monthlySales = $monthlySales->map(function ($sale) use ($monthlyReturns) {
            $returnAmount = $monthlyReturns->get($sale->month)?->total ?? 0;
            $sale->total = $sale->total - $returnAmount;
            return $sale;
        });

        // Get user's branch name
        $user = $request->user();
        $branchName = $user->branch ? $user->branch->name : 'غير محدد';

        // Get recent activities (using recent sales as placeholder)
        $recentActivities = $recentSales->map(function ($sale) {
            return (object) [
                'description' => "Sale #{$sale->id} - " . ($sale->customer ? $sale->customer->name : 'Walk-in Customer'),
                'created_at' => $sale->created_at
            ];
        });

        return view('dashboard', compact(
            'totalProducts',
            'totalCategories',
            'totalCustomers',
            'totalSales',
            'totalBranches',
            'totalSalesAmount',
            'todaySales',
            'branchName',
            'recentSales',
            'recentActivities',
            'lowStockProducts',
            'topSellingProducts',
            'salesByStatus',
            'monthlySales'
        ));
    }
}
