<x-app-layout>
    <x-slot name="header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                    <i class="fas fa-exchange-alt text-primary"></i> {{ __('تفاصيل عملية النقل') }}
                    #{{ $transfer->transfer_number }}
                </h2>
                <p class="text-muted small mb-0">تفاصيل عملية النقل المباشر</p>
            </div>
            <div>
                <a href="{{ user_route('transfers.direct.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> نقل جديد
                </a>
                <a href="{{ user_route('transfers.history') }}" class="btn btn-secondary">
                    <i class="fas fa-history"></i> سجل النقل
                </a>
            </div>
        </div>
    </x-slot>

    <div class="container-fluid">
        <!-- Transfer Info -->
        <div class="row mb-4">
            <div class="col-lg-8">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle"></i> معلومات النقل
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">رقم النقل:</label>
                                    <span class="badge bg-primary fs-6">{{ $transfer->transfer_number }}</span>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label fw-bold">نوع النقل:</label>
                                    <span
                                        class="badge bg-info">{{ ucfirst(str_replace('_', ' إلى ', $transfer->type)) }}</span>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label fw-bold">الحالة:</label>
                                    @if ($transfer->status === 'completed')
                                        <span class="badge bg-success">مكتمل</span>
                                    @elseif($transfer->status === 'pending')
                                        <span class="badge bg-warning">معلق</span>
                                    @else
                                        <span class="badge bg-danger">ملغي</span>
                                    @endif
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">تاريخ الطلب:</label>
                                    <p class="mb-0">{{ $transfer->requested_at->format('Y-m-d H:i') }}</p>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label fw-bold">تاريخ التنفيذ:</label>
                                    <p class="mb-0">
                                        {{ $transfer->received_at ? $transfer->received_at->format('Y-m-d H:i') : '-' }}
                                    </p>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label fw-bold">نفذ بواسطة:</label>
                                    <p class="mb-0">{{ $transfer->requestedBy->name }}</p>
                                </div>
                            </div>
                        </div>

                        @if ($transfer->notes)
                            <div class="mt-3">
                                <label class="form-label fw-bold">ملاحظات:</label>
                                <p class="mb-0 text-muted">{{ $transfer->notes }}</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="card shadow-sm">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-route"></i> مسار النقل
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- Source -->
                        <div class="mb-3">
                            <label class="form-label fw-bold text-danger">من:</label>
                            <div class="d-flex align-items-center">
                                @if ($transfer->source_type === 'branch')
                                    <i class="fas fa-building text-primary me-2"></i>
                                    <span>{{ $transfer->sourceBranch->name ?? 'غير محدد' }}</span>
                                @else
                                    <i class="fas fa-store text-success me-2"></i>
                                    <span>{{ $transfer->sourceStore->name ?? 'غير محدد' }}</span>
                                @endif
                            </div>
                        </div>

                        <!-- Arrow -->
                        <div class="text-center mb-3">
                            <i class="fas fa-arrow-down fa-2x text-muted"></i>
                        </div>

                        <!-- Destination -->
                        <div class="mb-3">
                            <label class="form-label fw-bold text-success">إلى:</label>
                            <div class="d-flex align-items-center">
                                @if ($transfer->destination_type === 'branch')
                                    <i class="fas fa-building text-primary me-2"></i>
                                    <span>{{ $transfer->destinationBranch->name ?? 'غير محدد' }}</span>
                                @else
                                    <i class="fas fa-store text-success me-2"></i>
                                    <span>{{ $transfer->destinationStore->name ?? 'غير محدد' }}</span>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Transfer Items -->
        <div class="card shadow-sm">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="fas fa-boxes"></i> المنتجات المنقولة
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>المنتج</th>
                                <th>الكمية المطلوبة</th>
                                <th>الكمية المنقولة</th>
                                <th>{{ auth()->user()->isSeller() ? 'سعر البيع' : 'سعر الوحدة' }}</th>
                                <th>إجمالي القيمة</th>
                                <th>الحالة</th>
                                <th>ملاحظات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($transfer->items as $item)
                                @php
                                    // Get the appropriate price based on user role
                                    $displayPrice = $item->unit_cost ?? 0;
                                    if (auth()->user()->isSeller()) {
                                        // For sellers, try to get sale price from source branch inventory
                                        if ($transfer->source_type === 'branch') {
                                            $branchInventory = $item->product->branchInventories
                                                ->where('branch_id', $transfer->source_id)
                                                ->first();
                                            if ($branchInventory && $branchInventory->sale_price_1) {
                                                $displayPrice = $branchInventory->sale_price_1;
                                            }
                                        }
                                    }
                                    $quantity = $item->received_quantity ?? ($item->approved_quantity ?? 0);
                                    $totalValue = $quantity * $displayPrice;
                                @endphp
                                <tr>
                                    <td class="fw-bold">{{ $item->product->name }}</td>
                                    <td class="text-center">{{ number_format($item->requested_quantity, 2) }}</td>
                                    <td class="text-center">
                                        <span class="badge bg-success">{{ number_format($quantity, 2) }}</span>
                                    </td>
                                    <td>{{ number_format($displayPrice, 2) }} ج.م</td>
                                    <td class="fw-bold">
                                        {{ number_format($totalValue, 2) }}
                                        ج.م</td>
                                    <td>
                                        @if ($item->received_quantity !== null)
                                            @if ($item->received_quantity == $item->requested_quantity)
                                                <span class="badge bg-success">مطابق</span>
                                            @elseif($item->received_quantity < $item->requested_quantity)
                                                <span class="badge bg-warning">نقص</span>
                                            @else
                                                <span class="badge bg-info">زيادة</span>
                                            @endif
                                        @else
                                            <span class="badge bg-secondary">معلق</span>
                                        @endif
                                    </td>
                                    <td>{{ $item->notes ?? '-' }}</td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="7" class="text-center py-4">
                                        <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">لا توجد منتجات في هذا النقل</p>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                        @if ($transfer->items->count() > 0)
                            <tfoot class="table-light">
                                <tr>
                                    <th colspan="4" class="text-end">إجمالي القيمة:</th>
                                    <th>
                                        @php
                                            $totalValue = 0;
                                            foreach ($transfer->items as $item) {
                                                $displayPrice = $item->unit_cost ?? 0;
                                                if (auth()->user()->isSeller()) {
                                                    // For sellers, try to get sale price from source branch inventory
                                                    if ($transfer->source_type === 'branch') {
                                                        $branchInventory = $item->product->branchInventories
                                                            ->where('branch_id', $transfer->source_id)
                                                            ->first();
                                                        if ($branchInventory && $branchInventory->sale_price_1) {
                                                            $displayPrice = $branchInventory->sale_price_1;
                                                        }
                                                    }
                                                }
                                                $quantity = $item->received_quantity ?? ($item->approved_quantity ?? 0);
                                                $totalValue += $quantity * $displayPrice;
                                            }
                                        @endphp
                                        {{ number_format($totalValue, 2) }} ج.م
                                    </th>
                                    <th colspan="2"></th>
                                </tr>
                            </tfoot>
                        @endif
                    </table>
                </div>
            </div>
        </div>

        <!-- Timeline -->
        @if ($transfer->status === 'completed')
            <div class="card shadow-sm mt-4">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-clock"></i> الجدول الزمني
                    </h5>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-marker bg-primary"></div>
                            <div class="timeline-content">
                                <h6 class="timeline-title">طلب النقل</h6>
                                <p class="timeline-text">{{ $transfer->requested_at->format('Y-m-d H:i') }}</p>
                                <small class="text-muted">بواسطة: {{ $transfer->requestedBy->name }}</small>
                            </div>
                        </div>

                        @if ($transfer->approved_at)
                            <div class="timeline-item">
                                <div class="timeline-marker bg-success"></div>
                                <div class="timeline-content">
                                    <h6 class="timeline-title">تم التنفيذ</h6>
                                    <p class="timeline-text">{{ $transfer->approved_at->format('Y-m-d H:i') }}</p>
                                    <small class="text-muted">بواسطة:
                                        {{ $transfer->approvedBy->name ?? 'النظام' }}</small>
                                </div>
                            </div>
                        @endif

                        @if ($transfer->received_at)
                            <div class="timeline-item">
                                <div class="timeline-marker bg-info"></div>
                                <div class="timeline-content">
                                    <h6 class="timeline-title">تم الاستلام</h6>
                                    <p class="timeline-text">{{ $transfer->received_at->format('Y-m-d H:i') }}</p>
                                    <small class="text-muted">بواسطة:
                                        {{ $transfer->receivedBy->name ?? 'النظام' }}</small>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        @endif
    </div>

    @push('styles')
        <style>
            .timeline {
                position: relative;
                padding-left: 30px;
            }

            .timeline::before {
                content: '';
                position: absolute;
                left: 15px;
                top: 0;
                bottom: 0;
                width: 2px;
                background: #dee2e6;
            }

            .timeline-item {
                position: relative;
                margin-bottom: 30px;
            }

            .timeline-marker {
                position: absolute;
                left: -22px;
                top: 0;
                width: 15px;
                height: 15px;
                border-radius: 50%;
                border: 3px solid #fff;
                box-shadow: 0 0 0 2px #dee2e6;
            }

            .timeline-title {
                margin-bottom: 5px;
                font-weight: 600;
            }

            .timeline-text {
                margin-bottom: 5px;
                color: #6c757d;
            }
        </style>
    @endpush
</x-app-layout>
