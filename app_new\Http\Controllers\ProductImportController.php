<?php

namespace App\Http\Controllers;

use App\Models\Category;
use App\Helpers\AlertHelper;
use App\Imports\ProductsImport;
use App\Exports\ProductsTemplateExport;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;

class ProductImportController extends Controller
{
    /**
     * Show import form
     */
    public function showImport()
    {
        $categories = Category::all();
        return view('products.import', compact('categories'));
    }

    /**
     * Download import template
     */
    public function downloadTemplate()
    {
        return Excel::download(new ProductsTemplateExport, 'products_template.xlsx');
    }

    /**
     * Import products from Excel/CSV
     */
    public function import(Request $request)
    {
        $request->validate([
            'file' => 'required|mimes:xlsx,xls,csv|max:10240', // 10MB max
        ]);

        try {
            $import = new ProductsImport();
            Excel::import($import, $request->file('file'));

            $successCount = $import->getSuccessCount();
            $errors = $import->getErrors();

            if ($successCount > 0) {
                AlertHelper::success("تم استيراد {$successCount} منتج بنجاح");
            }

            if (!empty($errors)) {
                $errorMessage = 'بعض المنتجات لم يتم استيرادها: ' . implode(', ', array_slice($errors, 0, 3));
                if (count($errors) > 3) {
                    $errorMessage .= ' و ' . (count($errors) - 3) . ' أخطاء أخرى';
                }
                AlertHelper::warning($errorMessage);
            }

            if ($successCount == 0 && !empty($errors)) {
                AlertHelper::error('فشل في استيراد المنتجات. يرجى التحقق من تنسيق الملف.');
            }

            return redirect()->route('admin.products.index');
        } catch (\Exception $e) {
            AlertHelper::error('حدث خطأ أثناء الاستيراد: ' . $e->getMessage());
            return back();
        }
    }
}
