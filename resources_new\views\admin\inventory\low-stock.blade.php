<x-app-layout>
    <x-slot name="header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                    <i class="fas fa-exclamation-triangle text-warning"></i> {{ __('المخزون المنخفض') }}
                </h2>
                <p class="text-muted small mb-0">المنتجات التي تحتاج إلى إعادة تموين</p>
            </div>
            <div>
                <a href="{{ route('admin.inventory.overview') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right"></i> العودة للنظرة العامة
                </a>
            </div>
        </div>
    </x-slot>

    <div class="container-fluid">
        @if($lowStockItems->count() > 0)
            <!-- Alert -->
            <div class="alert alert-warning" role="alert">
                <i class="fas fa-exclamation-triangle"></i>
                <strong>تنبيه!</strong> يوجد {{ $lowStockItems->count() }} منتج بحاجة إلى إعادة تموين.
            </div>

            <!-- Low Stock Items Table -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-warning">
                        <i class="fas fa-table"></i> المنتجات ذات المخزون المنخفض
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>المنتج</th>
                                    <th>الكود</th>
                                    <th>التصنيف</th>
                                    <th>نوع الموقع</th>
                                    <th>الموقع</th>
                                    <th>الكمية الحالية</th>
                                    <th>الحد الأدنى</th>
                                    <th>سعر التكلفة</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($lowStockItems as $item)
                                    <tr>
                                        <td class="fw-bold">{{ $item->product_name }}</td>
                                        <td>{{ $item->sku ?? '-' }}</td>
                                        <td>{{ $item->category }}</td>
                                        <td>
                                            @if($item->location_type == 'فرع')
                                                <span class="badge bg-primary">{{ $item->location_type }}</span>
                                            @else
                                                <span class="badge bg-success">{{ $item->location_type }}</span>
                                            @endif
                                        </td>
                                        <td>{{ $item->location_name }}</td>
                                        <td class="text-center">
                                            <span class="badge bg-warning text-dark">{{ number_format($item->current_quantity, 2) }}</span>
                                        </td>
                                        <td class="text-center">{{ number_format($item->threshold, 2) }}</td>
                                        <td>{{ number_format($item->cost_price, 2) }} ج.م</td>
                                        <td>
                                            @if($item->current_quantity <= 0)
                                                <span class="badge bg-danger">نفد المخزون</span>
                                            @elseif($item->current_quantity <= $item->threshold)
                                                <span class="badge bg-warning">مخزون منخفض</span>
                                            @else
                                                <span class="badge bg-success">متوفر</span>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                @if($item->location_type == 'فرع')
                                                    <a href="{{ route('admin.inventory-transfers.create') }}?source_type=store&destination_type=branch&product_id={{ $item->product_name }}" 
                                                       class="btn btn-outline-primary" title="نقل من مخزن">
                                                        <i class="fas fa-exchange-alt"></i>
                                                    </a>
                                                @else
                                                    <a href="{{ route('admin.inventory-transfers.create') }}?source_type=branch&destination_type=store&product_id={{ $item->product_name }}" 
                                                       class="btn btn-outline-success" title="نقل من فرع">
                                                        <i class="fas fa-exchange-alt"></i>
                                                    </a>
                                                @endif
                                                <a href="{{ route('admin.purchases.create') }}?product={{ $item->product_name }}" 
                                                   class="btn btn-outline-info" title="شراء جديد">
                                                    <i class="fas fa-shopping-cart"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="card border-left-info shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-info">
                                <i class="fas fa-bolt"></i> إجراءات سريعة
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a href="{{ route('admin.purchases.create') }}" class="btn btn-info">
                                    <i class="fas fa-shopping-cart"></i> إنشاء طلب شراء جديد
                                </a>
                                <a href="{{ route('admin.inventory-transfers.create') }}" class="btn btn-primary">
                                    <i class="fas fa-exchange-alt"></i> إنشاء عملية نقل
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card border-left-warning shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-warning">
                                <i class="fas fa-chart-pie"></i> إحصائيات سريعة
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-6">
                                    <div class="text-center">
                                        <div class="h4 text-warning">{{ $lowStockItems->where('location_type', 'فرع')->count() }}</div>
                                        <div class="small text-muted">فروع</div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="text-center">
                                        <div class="h4 text-success">{{ $lowStockItems->where('location_type', 'مخزن')->count() }}</div>
                                        <div class="small text-muted">مخازن</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @else
            <!-- No Low Stock Items -->
            <div class="card shadow">
                <div class="card-body text-center py-5">
                    <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                    <h5 class="text-success">ممتاز! لا توجد منتجات بمخزون منخفض</h5>
                    <p class="text-muted">جميع المنتجات متوفرة بكميات كافية</p>
                    <a href="{{ route('admin.inventory.overview') }}" class="btn btn-primary">
                        <i class="fas fa-eye"></i> عرض النظرة العامة
                    </a>
                </div>
            </div>
        @endif
    </div>
</x-app-layout>
