<x-app-layout>
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h2 class="h3 mb-0">مخزون جميع المواقع</h2>
                    <div class="text-muted">
                        <i class="fas fa-store"></i> {{ auth()->user()->branch->name ?? 'غير محدد' }} (فرعي)
                        <span class="ms-2">
                            <i class="fas fa-warehouse"></i> عرض شامل لجميع الفروع والمخازن
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Inventory Summary Cards -->
        <div class="row mb-4">
            <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">إجمالي المنتجات</h6>
                                <h3 class="mb-0">{{ $inventorySummary['total_products'] ?? 0 }}</h3>
                            </div>
                            <div class="text-white-50">
                                <i class="fas fa-boxes fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">متوفرة محلياً</h6>
                                <h3 class="mb-0">{{ $inventorySummary['available_products'] ?? 0 }}</h3>
                            </div>
                            <div class="text-white-50">
                                <i class="fas fa-check-circle fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">قابلة للنقل</h6>
                                <h3 class="mb-0">{{ $inventorySummary['transferable_products'] ?? 0 }}</h3>
                                <small class="text-white-50">من مواقع أخرى</small>
                            </div>
                            <div class="text-white-50">
                                <i class="fas fa-exchange-alt fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">منتجات قليلة</h6>
                                <h3 class="mb-0">{{ $inventorySummary['low_stock_products'] ?? 0 }}</h3>
                                <small class="text-white-50">أقل من 10 قطع</small>
                            </div>
                            <div class="text-white-50">
                                <i class="fas fa-exclamation-triangle fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                <div class="card bg-danger text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">منتجات نفدت</h6>
                                <h3 class="mb-0">{{ $inventorySummary['out_of_stock_products'] ?? 0 }}</h3>
                            </div>
                            <div class="text-white-50">
                                <i class="fas fa-times-circle fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">متوفرة في مواقع أخرى</h6>
                                <h3 class="mb-0">{{ $inventorySummary['transferable_products'] ?? 0 }}</h3>
                            </div>
                            <div class="text-white-50">
                                <i class="fas fa-exchange-alt fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters and Search -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <form method="GET" action="{{ route('seller.inventory') }}">
                            <div class="row align-items-end">
                                <div class="col-md-4">
                                    <label for="search" class="form-label">البحث</label>
                                    <div class="input-group">
                                        <input type="text" name="search" id="search" class="form-control"
                                            placeholder="اسم المنتج أو الباركود..." value="{{ request('search') }}">
                                        <button class="btn btn-primary" type="submit">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <label for="category_id" class="form-label">الفئة</label>
                                    <select name="category_id" id="category_id" class="form-select">
                                        <option value="">جميع الفئات</option>
                                        @foreach ($categories as $category)
                                            <option value="{{ $category->id }}"
                                                {{ request('category_id') == $category->id ? 'selected' : '' }}>
                                                {{ $category->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label for="stock_status" class="form-label">حالة المخزون</label>
                                    <select name="stock_status" id="stock_status" class="form-select">
                                        <option value="">جميع الحالات</option>
                                        <option value="available"
                                            {{ request('stock_status') == 'available' ? 'selected' : '' }}>متوفر محلياً
                                        </option>
                                        <option value="transferable"
                                            {{ request('stock_status') == 'transferable' ? 'selected' : '' }}>متوفر في
                                            مواقع أخرى
                                        </option>
                                        <option value="low"
                                            {{ request('stock_status') == 'low' ? 'selected' : '' }}>قليل محلياً
                                        </option>
                                        <option value="out"
                                            {{ request('stock_status') == 'out' ? 'selected' : '' }}>غير متوفر</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <a href="{{ route('seller.inventory') }}" class="btn btn-secondary w-100">
                                        <i class="fas fa-undo"></i> إعادة تعيين
                                    </a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Inventory Table -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-list"></i> قائمة المنتجات
                        </h5>
                        <div class="btn-group">
                            <button type="button" class="btn btn-outline-success btn-sm">
                                <i class="fas fa-file-excel"></i> تصدير Excel
                            </button>
                            <button type="button" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-print"></i> طباعة
                            </button>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>الصورة</th>
                                        <th>اسم المنتج</th>
                                        <th>الفئة</th>
                                        <th>الباركود</th>
                                        <th>الكمية المتوفرة</th>
                                        <th>السعر الأساسي</th>
                                        <th>السعر الثاني</th>
                                        <th>السعر الثالث</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse($products as $product)
                                        <tr>
                                            <td>
                                                @if ($product->image)
                                                    <img src="{{ asset('storage/' . $product->image) }}"
                                                        class="img-thumbnail"
                                                        style="width: 50px; height: 50px; object-fit: cover;">
                                                @else
                                                    <div class="bg-light d-flex align-items-center justify-content-center"
                                                        style="width: 50px; height: 50px;">
                                                        <i class="fas fa-box text-muted"></i>
                                                    </div>
                                                @endif
                                            </td>
                                            <td>
                                                <h6 class="mb-1">{{ $product->name }}</h6>
                                                @if ($product->description)
                                                    <small
                                                        class="text-muted">{{ Str::limit($product->description, 50) }}</small>
                                                @endif
                                            </td>
                                            <td>{{ $product->category->name ?? 'بدون فئة' }}</td>
                                            <td>
                                                @if ($product->sku)
                                                    <code>{{ $product->sku }}</code>
                                                @else
                                                    <span class="text-muted">-</span>
                                                @endif
                                            </td>
                                            <td>
                                                <div class="availability-info">
                                                    <!-- Local quantity -->
                                                    @if ($product->local_quantity > 0)
                                                        <div class="mb-1">
                                                            <span class="badge bg-success">
                                                                <i class="fas fa-check"></i> محلياً:
                                                                {{ $product->local_quantity }}
                                                            </span>
                                                        </div>
                                                    @endif

                                                    <!-- Other locations -->
                                                    @if ($product->all_available_locations && $product->all_available_locations->count() > 0)
                                                        <div class="other-locations">
                                                            @foreach ($product->all_available_locations as $location)
                                                                <div class="small mb-1">
                                                                    @if ($location['location_type'] === 'branch')
                                                                        <i class="fas fa-building text-primary"></i>
                                                                    @else
                                                                        <i class="fas fa-warehouse text-info"></i>
                                                                    @endif
                                                                    <span
                                                                        class="text-muted">{{ $location['location_display'] }}:</span>
                                                                    <span
                                                                        class="badge bg-warning text-dark">{{ $location['quantity'] }}</span>
                                                                </div>
                                                            @endforeach
                                                        </div>
                                                    @endif

                                                    <!-- No availability -->
                                                    @if (
                                                        $product->local_quantity == 0 &&
                                                            (!$product->all_available_locations || $product->all_available_locations->count() == 0))
                                                        <span class="badge bg-danger">
                                                            <i class="fas fa-times"></i> غير متوفر
                                                        </span>
                                                    @endif
                                                </div>
                                            </td>
                                            <td>
                                                @if ($product->sale_price_1)
                                                    <span
                                                        class="fw-bold text-success">{{ number_format($product->sale_price_1, 2) }}
                                                        ج.م</span>
                                                @else
                                                    <span class="text-muted">-</span>
                                                @endif
                                            </td>
                                            <td>
                                                @if ($product->sale_price_2)
                                                    <span
                                                        class="fw-bold text-info">{{ number_format($product->sale_price_2, 2) }}
                                                        ج.م</span>
                                                @else
                                                    <span class="text-muted">-</span>
                                                @endif
                                            </td>
                                            <td>
                                                @if ($product->sale_price_3)
                                                    <span
                                                        class="fw-bold text-warning">{{ number_format($product->sale_price_3, 2) }}
                                                        ج.م</span>
                                                @else
                                                    <span class="text-muted">-</span>
                                                @endif
                                            </td>
                                            <td>
                                                <span
                                                    class="badge bg-{{ $product->availability_class }}">{{ $product->availability_text }}</span>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="{{ route('seller.products.show', $product) }}"
                                                        class="btn btn-outline-primary" title="عرض التفاصيل">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    @if ($product->local_available)
                                                        <button type="button" class="btn btn-outline-success btn-sm"
                                                            onclick="addToQuickSale({{ $product->id }})"
                                                            title="إضافة للبيع السريع">
                                                            <i class="fas fa-plus"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-outline-primary btn-sm"
                                                            onclick="showDirectTransferModal({{ $product->id }}, '{{ $product->name }}', {{ $product->local_quantity }})"
                                                            title="نقل مباشر لفرع آخر">
                                                            <i class="fas fa-share"></i>
                                                        </button>
                                                    @elseif($product->availability_status === 'transferable')
                                                        <button type="button" class="btn btn-outline-info btn-sm"
                                                            onclick="showDetailedAvailability({{ $product->id }}, '{{ $product->name }}', {{ json_encode($product->all_available_locations) }})"
                                                            title="عرض المواقع المتوفرة">
                                                            <i class="fas fa-map-marker-alt"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-outline-warning btn-sm"
                                                            onclick="requestTransfer({{ $product->id }}, 1)"
                                                            title="طلب نقل من موقع آخر">
                                                            <i class="fas fa-exchange-alt"></i>
                                                        </button>
                                                    @else
                                                        <span class="text-muted small">غير متوفر</span>
                                                    @endif
                                                </div>
                                            </td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="9" class="text-center text-muted py-5">
                                                <i class="fas fa-box-open fa-3x mb-3"></i>
                                                <h5>لا توجد منتجات</h5>
                                                <p>لا توجد منتجات تطابق معايير البحث المحددة</p>
                                            </td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>

                    @if ($products->hasPages())
                        <div class="card-footer">
                            {{ $products->links() }}
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Low Stock Alert Modal -->
        @if ($inventorySummary['low_stock_products'] > 0)
            <div class="row mt-4">
                <div class="col-12">
                    <div class="alert alert-warning">
                        <h5 class="alert-heading">
                            <i class="fas fa-exclamation-triangle"></i> تنبيه مخزون منخفض
                        </h5>
                        <p class="mb-0">
                            يوجد {{ $inventorySummary['low_stock_products'] }} منتج بكمية قليلة في المخزون.
                            يُنصح بإعادة تموين هذه المنتجات قريباً.
                        </p>
                        <hr>
                        <a href="{{ route('seller.inventory', ['stock_status' => 'low']) }}"
                            class="btn btn-warning btn-sm">
                            عرض المنتجات القليلة
                        </a>
                    </div>
                </div>
            </div>
        @endif
    </div>

    <!-- Product Availability Modal -->
    <div class="modal fade" id="availabilityModal" tabindex="-1" aria-labelledby="availabilityModalLabel"
        aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="availabilityModalLabel">توفر المنتج في المواقع</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="modalProductName" class="mb-3">
                        <h6 class="text-primary"></h6>
                    </div>
                    <div id="modalAvailabilityList">
                        <!-- Locations will be populated here -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    <button type="button" class="btn btn-warning" id="modalRequestTransferBtn">
                        <i class="fas fa-exchange-alt"></i> نقل مباشر
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Direct Transfer Modal -->
    <div class="modal fade" id="directTransferModal" tabindex="-1" aria-labelledby="directTransferModalLabel"
        aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="directTransferModalLabel">نقل مباشر لفرع آخر</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="directTransferForm">
                        <input type="hidden" id="transferProductId">

                        <div class="mb-3">
                            <label class="form-label">المنتج</label>
                            <div id="transferProductName" class="form-control-plaintext fw-bold text-primary"></div>
                        </div>

                        <div class="mb-3">
                            <label for="destinationBranch" class="form-label">الفرع المستهدف</label>
                            <select class="form-select" id="destinationBranch" required>
                                <option value="">اختر الفرع...</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="transferQuantity" class="form-label">الكمية</label>
                            <input type="number" class="form-control" id="transferQuantity" min="1"
                                required>
                            <div class="form-text">الكمية المتوفرة: <span id="availableQuantity"></span></div>
                        </div>

                        <div class="mb-3">
                            <label for="transferNotes" class="form-label">ملاحظات (اختياري)</label>
                            <textarea class="form-control" id="transferNotes" rows="3" placeholder="أضف ملاحظات حول النقل..."></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="executeDirectTransfer()">
                        <i class="fas fa-share"></i> تنفيذ النقل
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript for Quick Sale Integration -->
    <script>
        function addToQuickSale(productId) {
            // Store product ID in session storage for quick sale
            let quickSaleItems = JSON.parse(sessionStorage.getItem('quickSaleItems') || '[]');

            if (!quickSaleItems.includes(productId)) {
                quickSaleItems.push(productId);
                sessionStorage.setItem('quickSaleItems', JSON.stringify(quickSaleItems));

                toastr.success('تم إضافة المنتج إلى البيع السريع');

                // Ask if user wants to go to quick sale
                if (confirm('هل تريد الانتقال إلى صفحة البيع السريع؟')) {
                    window.location.href = '{{ route('seller.quick-sale') }}';
                }
            } else {
                toastr.info('المنتج موجود بالفعل في البيع السريع');
            }
        }

        // Show detailed availability modal
        function showDetailedAvailability(productId, productName, locations) {
            // Set product name
            document.querySelector('#modalProductName h6').textContent = productName;

            // Build locations list
            let locationsHtml = '';
            if (locations && locations.length > 0) {
                locationsHtml = '<div class="list-group">';
                locations.forEach(location => {
                    const iconClass = location.location_type === 'branch' ? 'fa-building text-primary' :
                        'fa-warehouse text-info';
                    locationsHtml += `
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <i class="fas ${iconClass} me-2"></i>
                                <strong>${location.location_display}</strong>
                                ${location.branch_code || location.store_code ? `<small class="text-muted"> (${location.branch_code || location.store_code})</small>` : ''}
                            </div>
                            <span class="badge bg-primary rounded-pill">${location.quantity} قطعة</span>
                        </div>
                    `;
                });
                locationsHtml += '</div>';
            } else {
                locationsHtml = '<p class="text-muted">لا توجد مواقع متوفرة</p>';
            }

            document.getElementById('modalAvailabilityList').innerHTML = locationsHtml;

            // Set up transfer button
            const transferBtn = document.getElementById('modalRequestTransferBtn');
            transferBtn.onclick = function() {
                requestTransfer(productId, 1);
                bootstrap.Modal.getInstance(document.getElementById('availabilityModal')).hide();
            };

            // Show modal
            new bootstrap.Modal(document.getElementById('availabilityModal')).show();
        }

        // Show direct transfer modal
        function showDirectTransferModal(productId, productName, availableQuantity) {
            document.getElementById('transferProductId').value = productId;
            document.getElementById('transferProductName').textContent = productName;
            document.getElementById('availableQuantity').textContent = availableQuantity;
            document.getElementById('transferQuantity').max = availableQuantity;
            document.getElementById('transferQuantity').value = 1;

            // Load available branches
            loadAvailableBranches();

            // Show modal
            new bootstrap.Modal(document.getElementById('directTransferModal')).show();
        }

        // Load available branches for transfer
        function loadAvailableBranches() {
            fetch('{{ route('seller.transfer.branches') }}')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const select = document.getElementById('destinationBranch');
                        select.innerHTML = '<option value="">اختر الفرع...</option>';

                        data.branches.forEach(branch => {
                            const option = document.createElement('option');
                            option.value = branch.id;
                            option.textContent = `${branch.name} ${branch.code ? '(' + branch.code + ')' : ''}`;
                            select.appendChild(option);
                        });
                    }
                })
                .catch(error => {
                    console.error('Error loading branches:', error);
                    toastr.error('حدث خطأ في تحميل قائمة الفروع');
                });
        }

        // Execute direct transfer
        function executeDirectTransfer() {
            const productId = document.getElementById('transferProductId').value;
            const destinationBranchId = document.getElementById('destinationBranch').value;
            const quantity = parseInt(document.getElementById('transferQuantity').value);
            const notes = document.getElementById('transferNotes').value;

            if (!destinationBranchId) {
                toastr.error('يرجى اختيار الفرع المستهدف');
                return;
            }

            if (!quantity || quantity <= 0) {
                toastr.error('يرجى إدخال كمية صحيحة');
                return;
            }

            // Send direct transfer request
            fetch('{{ route('seller.transfer.direct') }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    body: JSON.stringify({
                        product_id: productId,
                        destination_branch_id: destinationBranchId,
                        quantity: quantity,
                        notes: notes
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        toastr.success(data.message);
                        bootstrap.Modal.getInstance(document.getElementById('directTransferModal')).hide();
                        // Refresh the page to update inventory
                        setTimeout(() => {
                            location.reload();
                        }, 1500);
                    } else {
                        toastr.error(data.message || 'حدث خطأ أثناء تنفيذ النقل');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    toastr.error('حدث خطأ أثناء تنفيذ النقل');
                });
        }

        // Auto transfer for a product (from best available location)
        function requestTransfer(productId, quantity = 1) {
            // Show quantity input dialog
            const requestedQuantity = prompt('كم قطعة تريد نقلها؟', quantity);

            if (requestedQuantity && parseInt(requestedQuantity) > 0) {
                // Execute auto transfer (from best available location)
                fetch('{{ route('seller.transfer.auto') }}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': '{{ csrf_token() }}'
                        },
                        body: JSON.stringify({
                            product_id: productId,
                            quantity: parseInt(requestedQuantity)
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            toastr.success('تم تنفيذ النقل بنجاح');
                            // Refresh the page to show updated inventory
                            setTimeout(() => {
                                location.reload();
                            }, 1500);
                        } else {
                            toastr.error(data.message || 'حدث خطأ أثناء تنفيذ النقل');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        toastr.error('حدث خطأ أثناء تنفيذ النقل');
                    });
            }
        }

        // Add to Quick Sale function
        function addToQuickSale(productId) {
            // Redirect to quick sale page with product pre-selected
            window.open(`{{ route('seller.quick-sale') }}?product_id=${productId}`, '_blank');
        }

        // Show Direct Transfer Modal
        function showDirectTransferModal(productId, productName, availableQuantity) {
            Swal.fire({
                title: 'نقل مباشر',
                html: `
                    <div class="text-start">
                        <p><strong>المنتج:</strong> ${productName}</p>
                        <p><strong>الكمية المتوفرة:</strong> ${availableQuantity}</p>
                        <hr>
                        <div class="mb-3">
                            <label class="form-label">الفرع المستهدف:</label>
                            <select id="targetBranch" class="form-select">
                                <option value="">اختر الفرع...</option>
                                @foreach (\App\Models\Branch::where('id', '!=', auth()->user()->branch_id)->get() as $branch)
                                    <option value="{{ $branch->id }}">{{ $branch->name }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الكمية:</label>
                            <input type="number" id="transferQuantity" class="form-control" min="1" max="${availableQuantity}" value="1">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">ملاحظات (اختياري):</label>
                            <textarea id="transferNotes" class="form-control" rows="2"></textarea>
                        </div>
                    </div>
                `,
                showCancelButton: true,
                confirmButtonText: 'تنفيذ النقل',
                cancelButtonText: 'إلغاء',
                preConfirm: () => {
                    const targetBranch = document.getElementById('targetBranch').value;
                    const quantity = document.getElementById('transferQuantity').value;
                    const notes = document.getElementById('transferNotes').value;

                    if (!targetBranch) {
                        Swal.showValidationMessage('يرجى اختيار الفرع المستهدف');
                        return false;
                    }
                    if (!quantity || quantity <= 0) {
                        Swal.showValidationMessage('يرجى إدخال كمية صحيحة');
                        return false;
                    }
                    if (quantity > availableQuantity) {
                        Swal.showValidationMessage('الكمية المطلوبة أكبر من المتوفر');
                        return false;
                    }

                    return {
                        targetBranch,
                        quantity,
                        notes
                    };
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    executeDirectTransfer(productId, result.value.targetBranch, result.value.quantity, result.value
                        .notes);
                }
            });
        }

        // Execute Direct Transfer
        function executeDirectTransfer(productId, targetBranchId, quantity, notes) {
            fetch('{{ route('seller.transfers.direct.store') }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        from_type: 'branch',
                        from_id: {{ auth()->user()->branch_id }},
                        to_type: 'branch',
                        to_id: targetBranchId,
                        items: [{
                            product_id: productId,
                            quantity: quantity
                        }],
                        notes: notes
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        Swal.fire('نجح!', 'تم تنفيذ النقل بنجاح', 'success');
                        setTimeout(() => location.reload(), 1500);
                    } else {
                        Swal.fire('خطأ!', data.message || 'حدث خطأ أثناء تنفيذ النقل', 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    Swal.fire('خطأ!', 'حدث خطأ أثناء تنفيذ النقل', 'error');
                });
        }

        // Show Detailed Availability
        function showDetailedAvailability(productId, productName, availableLocations) {
            let locationsHtml = '';
            availableLocations.forEach(location => {
                locationsHtml += `
                    <div class="d-flex justify-content-between align-items-center mb-2 p-2 border rounded">
                        <div>
                            <strong>${location.location_display}</strong>
                            <br><small class="text-muted">${location.location_type === 'branch' ? 'فرع' : 'مخزن'}</small>
                        </div>
                        <div class="text-end">
                            <span class="badge bg-info">${location.quantity} قطعة</span>
                        </div>
                    </div>
                `;
            });

            Swal.fire({
                title: 'المواقع المتوفرة',
                html: `
                    <div class="text-start">
                        <p><strong>المنتج:</strong> ${productName}</p>
                        <hr>
                        <h6>متوفر في المواقع التالية:</h6>
                        ${locationsHtml}
                    </div>
                `,
                confirmButtonText: 'إغلاق'
            });
        }

        // Request Transfer from other location
        function requestTransfer(productId, quantity) {
            Swal.fire({
                title: 'طلب نقل',
                html: `
                    <div class="text-start">
                        <div class="mb-3">
                            <label class="form-label">الكمية المطلوبة:</label>
                            <input type="number" id="requestQuantity" class="form-control" min="1" value="${quantity}">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">ملاحظات (اختياري):</label>
                            <textarea id="requestNotes" class="form-control" rows="2" placeholder="سبب الطلب أو ملاحظات إضافية..."></textarea>
                        </div>
                    </div>
                `,
                showCancelButton: true,
                confirmButtonText: 'إرسال الطلب',
                cancelButtonText: 'إلغاء',
                preConfirm: () => {
                    const quantity = document.getElementById('requestQuantity').value;
                    const notes = document.getElementById('requestNotes').value;

                    if (!quantity || quantity <= 0) {
                        Swal.showValidationMessage('يرجى إدخال كمية صحيحة');
                        return false;
                    }

                    return {
                        quantity,
                        notes
                    };
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    // Here you would implement the transfer request functionality
                    Swal.fire('تم!', 'تم إرسال طلب النقل بنجاح', 'success');
                    // You can implement actual transfer request logic here
                }
            });
        }

        // Auto-submit form on filter change
        document.getElementById('category_id').addEventListener('change', function() {
            this.form.submit();
        });

        document.getElementById('stock_status').addEventListener('change', function() {
            this.form.submit();
        });
    </script>

    <style>
        .availability-info {
            min-width: 200px;
        }

        .availability-info .badge {
            font-size: 0.75rem;
            margin-right: 2px;
        }

        .other-locations .small {
            line-height: 1.4;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .other-locations .badge {
            font-size: 0.7rem;
            padding: 2px 6px;
        }

        .other-locations i {
            width: 12px;
            font-size: 0.8rem;
        }

        .table td {
            vertical-align: middle;
        }
    </style>
</x-app-layout>
