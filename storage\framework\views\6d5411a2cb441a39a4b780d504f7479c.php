<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="d-sm-flex align-items-center justify-content-between mb-4">
            <div>
                <h1 class="h3 mb-0 text-gray-800">
                    <i class="fas fa-undo text-warning me-2"></i>
                    تفاصيل مرتجع المبيعات
                </h1>
                <p class="mb-0 text-muted"><?php echo e($saleReturn->return_number); ?></p>
            </div>
            <div class="d-flex gap-2">
                <?php if($saleReturn->canBeEdited()): ?>
                    <a href="<?php echo e(user_route('sale-returns.edit', $saleReturn)); ?>" class="btn btn-warning">
                        <i class="fas fa-edit me-2"></i>تعديل
                    </a>
                <?php endif; ?>
                <?php if(auth()->user()->hasRole('admin') && $saleReturn->canBeApproved()): ?>
                    <form method="POST" action="<?php echo e(user_route('sale-returns.approve', $saleReturn)); ?>" class="d-inline">
                        <?php echo csrf_field(); ?>
                        <button type="submit" class="btn btn-info" 
                                onclick="return confirm('هل أنت متأكد من الموافقة على هذا المرتجع؟')">
                            <i class="fas fa-check me-2"></i>موافقة
                        </button>
                    </form>
                <?php endif; ?>
                <?php if(auth()->user()->hasRole('admin') && $saleReturn->canBeCompleted()): ?>
                    <form method="POST" action="<?php echo e(user_route('sale-returns.complete', $saleReturn)); ?>" class="d-inline">
                        <?php echo csrf_field(); ?>
                        <button type="submit" class="btn btn-success" 
                                onclick="return confirm('هل أنت متأكد من إكمال هذا المرتجع؟ سيتم تعديل المخزون.')">
                            <i class="fas fa-check-circle me-2"></i>إكمال
                        </button>
                    </form>
                <?php endif; ?>
                <a href="<?php echo e(user_route('sale-returns.index')); ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-2"></i>العودة إلى المرتجعات
                </a>
            </div>
        </div>

        <div class="row">
            <!-- Return Details -->
            <div class="col-lg-8">
                <!-- Return Information -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-info-circle me-2"></i>معلومات المرتجع
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold text-muted">رقم المرتجع</label>
                                <div class="fw-bold text-primary"><?php echo e($saleReturn->return_number); ?></div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold text-muted">رقم الفاتورة الأصلية</label>
                                <div>
                                    <a href="<?php echo e(user_route('sales.show', $saleReturn->sale)); ?>" 
                                       class="text-decoration-none fw-bold text-info">
                                        <?php echo e($saleReturn->sale->invoice_number); ?>

                                    </a>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold text-muted">العميل</label>
                                <div class="fw-bold"><?php echo e($saleReturn->customer?->name ?? 'عميل نقدي'); ?></div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold text-muted">تاريخ المرتجع</label>
                                <div class="fw-bold"><?php echo e($saleReturn->return_date->format('d/m/Y')); ?></div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold text-muted">نوع المرتجع</label>
                                <div>
                                    <span class="badge bg-info fs-6"><?php echo e($saleReturn->return_type_label); ?></span>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold text-muted">الحالة</label>
                                <div>
                                    <span class="badge bg-<?php echo e($saleReturn->status_color); ?> fs-6"><?php echo e($saleReturn->status_label); ?></span>
                                </div>
                            </div>
                            <?php if($saleReturn->reason): ?>
                            <div class="col-md-12 mb-3">
                                <label class="form-label fw-bold text-muted">سبب المرتجع</label>
                                <div class="fw-bold"><?php echo e($saleReturn->reason); ?></div>
                            </div>
                            <?php endif; ?>
                            <?php if($saleReturn->notes): ?>
                            <div class="col-md-12 mb-3">
                                <label class="form-label fw-bold text-muted">ملاحظات</label>
                                <div class="p-3 bg-light rounded"><?php echo e($saleReturn->notes); ?></div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Return Items -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-boxes me-2"></i>المنتجات المرتجعة
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead class="table-light">
                                    <tr>
                                        <th>المنتج</th>
                                        <th>الكمية الأصلية</th>
                                        <th>الكمية المرتجعة</th>
                                        <th>السعر</th>
                                        <th>المجموع</th>
                                        <th>الحالة</th>
                                        <th>ملاحظات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $saleReturn->items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td>
                                            <div class="fw-bold"><?php echo e($item->product->name); ?></div>
                                            <small class="text-muted">كود: <?php echo e($item->product->code); ?></small>
                                        </td>
                                        <td>
                                            <span class="badge bg-info"><?php echo e($item->original_quantity); ?></span>
                                        </td>
                                        <td>
                                            <span class="badge bg-warning"><?php echo e($item->quantity_returned); ?></span>
                                        </td>
                                        <td><?php echo e(number_format($item->sale_price, 2)); ?> ج.م</td>
                                        <td class="fw-bold text-success"><?php echo e(number_format($item->total_amount, 2)); ?> ج.م</td>
                                        <td>
                                            <span class="badge bg-<?php echo e($item->condition_color); ?>">
                                                <?php echo e($item->condition_label); ?>

                                            </span>
                                        </td>
                                        <td>
                                            <small class="text-muted"><?php echo e($item->item_notes ?? '-'); ?></small>
                                        </td>
                                    </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                                <tfoot class="table-light">
                                    <tr>
                                        <th colspan="4" class="text-end">إجمالي المرتجع:</th>
                                        <th class="text-success"><?php echo e(number_format($saleReturn->total_amount, 2)); ?> ج.م</th>
                                        <th colspan="2"></th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Summary Card -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-calculator me-2"></i>ملخص المرتجع
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-12 mb-3">
                                <h5 class="text-muted">عدد المنتجات</h5>
                                <h3 class="text-primary"><?php echo e($saleReturn->items->count()); ?></h3>
                            </div>
                            <div class="col-12 mb-3">
                                <h5 class="text-muted">إجمالي الكمية</h5>
                                <h3 class="text-warning"><?php echo e($saleReturn->items->sum('quantity_returned')); ?></h3>
                            </div>
                            <div class="col-12 mb-3">
                                <h5 class="text-muted">إجمالي المبلغ</h5>
                                <h3 class="text-success"><?php echo e(number_format($saleReturn->total_amount, 2)); ?> ج.م</h3>
                            </div>
                            <?php if($saleReturn->refund_amount > 0): ?>
                            <div class="col-12 mb-3">
                                <h5 class="text-muted">مبلغ الاسترداد</h5>
                                <h3 class="text-info"><?php echo e(number_format($saleReturn->refund_amount, 2)); ?> ج.م</h3>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- User Information -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-user me-2"></i>معلومات المستخدم
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label fw-bold text-muted">تم الإنشاء بواسطة</label>
                            <div class="fw-bold"><?php echo e($saleReturn->user->name); ?></div>
                            <small class="text-muted"><?php echo e($saleReturn->created_at->format('d/m/Y h:i A')); ?></small>
                        </div>
                        <?php if($saleReturn->approved_by): ?>
                        <div class="mb-3">
                            <label class="form-label fw-bold text-muted">تمت الموافقة بواسطة</label>
                            <div class="fw-bold"><?php echo e($saleReturn->approvedBy->name); ?></div>
                            <small class="text-muted"><?php echo e($saleReturn->approved_at->format('d/m/Y h:i A')); ?></small>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Location Information -->
                <?php if($saleReturn->branch || $saleReturn->store): ?>
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-map-marker-alt me-2"></i>معلومات الموقع
                        </h6>
                    </div>
                    <div class="card-body">
                        <?php if($saleReturn->branch): ?>
                        <div class="mb-3">
                            <label class="form-label fw-bold text-muted">الفرع</label>
                            <div class="fw-bold"><?php echo e($saleReturn->branch->name); ?></div>
                        </div>
                        <?php endif; ?>
                        <?php if($saleReturn->store): ?>
                        <div class="mb-3">
                            <label class="form-label fw-bold text-muted">المخزن</label>
                            <div class="fw-bold"><?php echo e($saleReturn->store->name); ?></div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH D:\pos-app\resources\views/sale-returns/show.blade.php ENDPATH**/ ?>