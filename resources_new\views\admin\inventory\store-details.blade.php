<x-app-layout>
    <x-slot name="header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                    <i class="fas fa-store text-success"></i> {{ __('مخزون مخزن') }} - {{ $store->name }}
                </h2>
                <p class="text-muted small mb-0">تفاصيل مخزون مخزن {{ $store->name }} ({{ $store->code }})</p>
            </div>
            <div>
                <a href="{{ route('admin.inventory.stores') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right"></i> العودة للمخازن
                </a>
                <a href="{{ route('admin.inventory-transfers.create') }}?source_type=store&source_id={{ $store->id }}" class="btn btn-success">
                    <i class="fas fa-exchange-alt"></i> نقل من هذا المخزن
                </a>
            </div>
        </div>
    </x-slot>

    <div class="container-fluid">
        <!-- Store Info Card -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-left-success shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-success">
                            <i class="fas fa-info-circle"></i> معلومات المخزن
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-2">
                                <div class="text-center">
                                    <div class="h4 text-success">{{ $inventory->total() }}</div>
                                    <div class="small text-muted">إجمالي المنتجات</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <div class="h4 text-primary">
                                        {{ number_format($inventory->sum(function($item) { return $item->quantity * ($item->product->price ?? 0); }), 2) }} ج.م
                                    </div>
                                    <div class="small text-muted">إجمالي القيمة</div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="text-center">
                                    <div class="h4 text-warning">
                                        {{ $inventory->filter(function($item) { return $item->quantity <= $item->minimum_stock; })->count() }}
                                    </div>
                                    <div class="small text-muted">مخزون منخفض</div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="text-center">
                                    <div class="h4 text-info">{{ $store->code }}</div>
                                    <div class="small text-muted">كود المخزن</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <div class="h4 text-secondary">{{ $store->branch->name ?? 'مستقل' }}</div>
                                    <div class="small text-muted">الفرع التابع له</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Search -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-success">
                    <i class="fas fa-search"></i> البحث في المخزون
                </h6>
            </div>
            <div class="card-body">
                <form method="GET" action="{{ route('admin.inventory.store-details', $store) }}">
                    <div class="row">
                        <div class="col-md-8 mb-3">
                            <input type="text" class="form-control" name="search" 
                                   value="{{ request('search') }}" placeholder="البحث عن منتج بالاسم أو الكود">
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="d-grid">
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-search"></i> بحث
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Inventory Table -->
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-success">
                    <i class="fas fa-table"></i> تفاصيل المخزون
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>المنتج</th>
                                <th>الكود</th>
                                <th>التصنيف</th>
                                <th>الكمية الحالية</th>
                                <th>الحد الأدنى</th>
                                <th>الحد الأقصى</th>
                                <th>سعر المنتج</th>
                                <th>إجمالي القيمة</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($inventory as $item)
                                <tr>
                                    <td class="fw-bold">{{ $item->product->name }}</td>
                                    <td>{{ $item->product->sku ?? '-' }}</td>
                                    <td>{{ $item->product->category->name ?? 'غير محدد' }}</td>
                                    <td class="text-center">
                                        <span class="badge {{ $item->quantity <= $item->minimum_stock ? 'bg-warning' : 'bg-success' }}">
                                            {{ number_format($item->quantity, 2) }}
                                        </span>
                                    </td>
                                    <td class="text-center">{{ number_format($item->minimum_stock, 2) }}</td>
                                    <td class="text-center">{{ number_format($item->maximum_stock ?? 0, 2) }}</td>
                                    <td>{{ number_format($item->product->price ?? 0, 2) }} ج.م</td>
                                    <td class="fw-bold">{{ number_format($item->quantity * ($item->product->price ?? 0), 2) }} ج.م</td>
                                    <td>
                                        @if($item->quantity <= 0)
                                            <span class="badge bg-danger">نفد المخزون</span>
                                        @elseif($item->quantity <= $item->minimum_stock)
                                            <span class="badge bg-warning">مخزون منخفض</span>
                                        @elseif($item->maximum_stock && $item->quantity >= $item->maximum_stock)
                                            <span class="badge bg-info">مخزون مرتفع</span>
                                        @else
                                            <span class="badge bg-success">متوفر</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ route('admin.products.show', $item->product) }}" 
                                               class="btn btn-outline-info" title="عرض المنتج">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('admin.inventory-transfers.create') }}?source_type=store&source_id={{ $store->id }}&product_id={{ $item->product->id }}" 
                                               class="btn btn-outline-success" title="نقل">
                                                <i class="fas fa-exchange-alt"></i>
                                            </a>
                                            @if($item->quantity <= $item->minimum_stock)
                                                <a href="{{ route('admin.inventory-transfers.create') }}?destination_type=store&destination_id={{ $store->id }}&product_id={{ $item->product->id }}" 
                                                   class="btn btn-outline-primary" title="تموين">
                                                    <i class="fas fa-plus"></i>
                                                </a>
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="10" class="text-center py-4">
                                        <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">لا توجد منتجات في هذا المخزن</p>
                                        <a href="{{ route('admin.inventory-transfers.create') }}?destination_type=store&destination_id={{ $store->id }}" 
                                           class="btn btn-success">
                                            <i class="fas fa-plus"></i> نقل منتجات إلى هذا المخزن
                                        </a>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                @if($inventory->hasPages())
                    <div class="mt-4">
                        {{ $inventory->appends(request()->query())->links() }}
                    </div>
                @endif
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card border-left-info shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-info">
                            <i class="fas fa-bolt"></i> إجراءات سريعة
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="{{ route('admin.inventory-transfers.create') }}?source_type=store&source_id={{ $store->id }}" 
                               class="btn btn-success">
                                <i class="fas fa-arrow-right"></i> نقل من هذا المخزن
                            </a>
                            <a href="{{ route('admin.inventory-transfers.create') }}?destination_type=store&destination_id={{ $store->id }}" 
                               class="btn btn-primary">
                                <i class="fas fa-arrow-left"></i> نقل إلى هذا المخزن
                            </a>
                            <a href="{{ route('admin.stores.inventory', $store->id) }}" 
                               class="btn btn-info">
                                <i class="fas fa-cog"></i> إدارة المخزون
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card border-left-warning shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-warning">
                            <i class="fas fa-exclamation-triangle"></i> تنبيهات المخزون
                        </h6>
                    </div>
                    <div class="card-body">
                        @php
                            $lowStockCount = $inventory->filter(function($item) { return $item->quantity <= $item->minimum_stock; })->count();
                            $outOfStockCount = $inventory->where('quantity', '<=', 0)->count();
                            $highStockCount = $inventory->filter(function($item) { return $item->maximum_stock && $item->quantity >= $item->maximum_stock; })->count();
                        @endphp
                        
                        @if($lowStockCount > 0)
                            <div class="alert alert-warning mb-2">
                                <i class="fas fa-exclamation-triangle"></i>
                                {{ $lowStockCount }} منتج بمخزون منخفض
                            </div>
                        @endif
                        
                        @if($outOfStockCount > 0)
                            <div class="alert alert-danger mb-2">
                                <i class="fas fa-times-circle"></i>
                                {{ $outOfStockCount }} منتج نفد مخزونه
                            </div>
                        @endif
                        
                        @if($highStockCount > 0)
                            <div class="alert alert-info mb-2">
                                <i class="fas fa-arrow-up"></i>
                                {{ $highStockCount }} منتج بمخزون مرتفع
                            </div>
                        @endif
                        
                        @if($lowStockCount == 0 && $outOfStockCount == 0)
                            <div class="alert alert-success mb-0">
                                <i class="fas fa-check-circle"></i>
                                جميع المنتجات ضمن المستويات المطلوبة
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
