<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['user' => auth()->user()]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['user' => auth()->user()]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<div class="sidebar" id="sidebar">
    <!-- Sidebar Header -->
    <div class="sidebar-header">
        <div class="d-flex align-items-center">
            
            <div>
                <h5 class="mb-0">شركة الاتحاد لتجارة و توزيع الأدوات الصحية </h5>
                
            </div>
        </div>
    </div>

    <!-- User Info -->
    <div class="user-info">
        <div class="d-flex align-items-center p-3">
            <div class="user-avatar me-3">
                <i class="fas fa-user-circle fa-2x text-primary"></i>
            </div>
            <div class="user-details">
                <h6 class="mb-0"><?php echo e($user->name); ?></h6>
                <small class="">
                    <?php if($user->isAdmin()): ?>
                        <i class="fas fa-crown text-warning"></i> مدير النظام
                    <?php elseif($user->isSeller()): ?>
                        <i class="fas fa-cash-register text-success"></i> بائع
                    <?php else: ?>
                        <i class="fas fa-user text-info"></i> مستخدم
                    <?php endif; ?>
                </small>
            </div>
        </div>
    </div>

    <!-- Navigation Menu -->
    <nav class="sidebar-nav">
        <ul class="nav flex-column">

            <?php if($user->isAdmin()): ?>
                
                <!-- Dashboard -->
                <li class="nav-item">
                    <a class="nav-link <?php echo e(request()->routeIs('admin.dashboard') ? 'active' : ''); ?>"
                        href="<?php echo e(route('admin.dashboard')); ?>">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>لوحة التحكم الرئيسية</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link collapsed" data-bs-toggle="collapse" href="#productsMenu" aria-expanded="false"
                        aria-controls="productsMenu">
                        <i class="fas fa-boxes"></i>
                        <span>المنتجات والأصناف</span>
                        <i class="fas fa-chevron-down ms-auto"></i>
                    </a>
                    <div class="collapse <?php echo e(request()->routeIs('admin.products*', 'admin.categories*') ? 'show' : ''); ?>"
                        id="productsMenu">
                        <ul class="nav flex-column ms-3">
                            <li class="nav-item">
                                <a class="nav-link <?php echo e(request()->routeIs('admin.categories*') ? 'active' : ''); ?>"
                                    href="<?php echo e(route('admin.categories.index')); ?>">
                                    <i class="fas fa-tags"></i> الفئات
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link <?php echo e(request()->routeIs('admin.products*') ? 'active' : ''); ?>"
                                    href="<?php echo e(route('admin.products.index')); ?>">
                                    <i class="fas fa-box"></i> المنتجات
                                </a>
                            </li>
                        </ul>
                    </div>
                </li>
                <!-- Branch Management -->
                
                <!-- Branches & Analytics -->
                <li class="nav-item">
                    <a class="nav-link collapsed" data-bs-toggle="collapse" href="#branchAnalyticsMenu"
                        aria-expanded="false" aria-controls="branchAnalyticsMenu">
                        <i class="fas fa-building"></i>
                        <span>الفروع والمخازن</span>
                        <i class="fas fa-chevron-down ms-auto"></i>
                    </a>
                    <div class="collapse <?php echo e(request()->routeIs('admin.branches*', 'admin.stores*', 'admin.inventory.low-stock', 'admin.inventory.overview') ? 'show' : ''); ?>"
                        id="branchAnalyticsMenu">
                        <ul class="nav flex-column ms-3">
                            <li class="nav-item">
                                <a class="nav-link <?php echo e(request()->routeIs('admin.branches*') ? 'active' : ''); ?>"
                                    href="<?php echo e(route('admin.branches.index')); ?>">
                                    <i class="fas fa-list"></i> الفروع
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link <?php echo e(request()->routeIs('admin.stores*') ? 'active' : ''); ?>"
                                    href="<?php echo e(route('admin.stores.index')); ?>">
                                    <i class="fas fa-store"></i> المخازن
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link <?php echo e(request()->routeIs('admin.inventory.overview') ? 'active' : ''); ?>"
                                    href="<?php echo e(route('admin.inventory.overview')); ?>">
                                    <i class="fas fa-eye"></i> نظرة عامة على المخزون
                                </a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link <?php echo e(request()->routeIs('admin.inventory.low-stock') ? 'active' : ''); ?>"
                                    href="<?php echo e(route('admin.inventory.low-stock')); ?>">
                                    <i class="fas fa-exclamation-triangle text-warning"></i> المخزون المنخفض
                                </a>
                            </li>
                            
                            
                        </ul>
                    </div>
                </li>

                <!-- Store Management -->
                

                <!-- Products & Inventory -->


                <!-- Inventory Management -->
                

                <!-- Transfer Management -->
                <li class="nav-item">
                    <a class="nav-link collapsed" data-bs-toggle="collapse" href="#transferMenu" aria-expanded="false"
                        aria-controls="transferMenu">
                        <i class="fas fa-exchange-alt"></i>
                        <span>إدارة النقل</span>
                        <i class="fas fa-chevron-down ms-auto"></i>
                    </a>
                    <div class="collapse <?php echo e(request()->routeIs('admin.transfers*', 'admin.inventory-transfers*') ? 'show' : ''); ?>"
                        id="transferMenu">
                        <ul class="nav flex-column ms-3">
                            <li class="nav-item">
                                <a class="nav-link <?php echo e(request()->routeIs('admin.transfers.direct.create') ? 'active' : ''); ?>"
                                    href="<?php echo e(route('admin.transfers.direct.create')); ?>">
                                    <i class="fas fa-plus"></i> نقل مباشر
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link <?php echo e(request()->routeIs('admin.transfers.history') ? 'active' : ''); ?>"
                                    href="<?php echo e(route('admin.transfers.history')); ?>">
                                    <i class="fas fa-history"></i> سجل النقل
                                </a>
                            </li>
                        </ul>
                    </div>
                </li>

                <!-- Sales & Customers Management -->
                <li class="nav-item">
                    <a class="nav-link collapsed" data-bs-toggle="collapse" href="#salesCustomersMenu"
                        aria-expanded="false" aria-controls="salesCustomersMenu">
                        <i class="fas fa-shopping-cart"></i>
                        <span>المبيعات والعملاء</span>
                        <i class="fas fa-chevron-down ms-auto"></i>
                    </a>
                    <div class="collapse <?php echo e(request()->routeIs('admin.sales*', 'admin.customers*', 'admin.customer-payments*', 'admin.sale-returns*') ? 'show' : ''); ?>"
                        id="salesCustomersMenu">
                        <ul class="nav flex-column ms-3">
                            <li class="nav-item">
                                <a class="nav-link <?php echo e(request()->routeIs('admin.sales*') ? 'active' : ''); ?>"
                                    href="<?php echo e(route('admin.sales.index')); ?>">
                                    <i class="fas fa-cash-register"></i> المبيعات
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link <?php echo e(request()->routeIs('admin.customers*') ? 'active' : ''); ?>"
                                    href="<?php echo e(route('admin.customers.index')); ?>">
                                    <i class="fas fa-users"></i> العملاء
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link <?php echo e(request()->routeIs('admin.sale-returns*') ? 'active' : ''); ?>"
                                    href="<?php echo e(route('admin.sale-returns.index')); ?>">
                                    <i class="fas fa-undo"></i> مرتجعات المبيعات
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link <?php echo e(request()->routeIs('admin.customer-payments*') ? 'active' : ''); ?>"
                                    href="<?php echo e(route('admin.customer-payments.index')); ?>">
                                    <i class="fas fa-money-bill-wave"></i> دفعات العملاء
                                </a>
                            </li>
                        </ul>
                    </div>
                </li>

                <!-- Purchase Management -->
                <li class="nav-item">
                    <a class="nav-link collapsed" data-bs-toggle="collapse" href="#purchaseMenu"
                        aria-expanded="false" aria-controls="purchaseMenu">
                        <i class="fas fa-truck"></i>
                        <span>المشتريات والموردين</span>
                        <i class="fas fa-chevron-down ms-auto"></i>
                    </a>
                    <div class="collapse <?php echo e(request()->routeIs('admin.purchases*', 'admin.suppliers*', 'admin.supplier-payments*', 'admin.purchase-returns*') ? 'show' : ''); ?>"
                        id="purchaseMenu">
                        <ul class="nav flex-column ms-3">
                            <li class="nav-item">
                                <a class="nav-link <?php echo e(request()->routeIs('admin.purchases*') ? 'active' : ''); ?>"
                                    href="<?php echo e(route('admin.purchases.index')); ?>">
                                    <i class="fas fa-shopping-bag"></i> المشتريات
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link <?php echo e(request()->routeIs('admin.suppliers*') ? 'active' : ''); ?>"
                                    href="<?php echo e(route('admin.suppliers.index')); ?>">
                                    <i class="fas fa-truck-loading"></i> الموردين
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link <?php echo e(request()->routeIs('admin.purchase-returns*') ? 'active' : ''); ?>"
                                    href="<?php echo e(route('admin.purchase-returns.index')); ?>">
                                    <i class="fas fa-undo"></i> مرتجعات المشتريات
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link <?php echo e(request()->routeIs('admin.supplier-payments*') ? 'active' : ''); ?>"
                                    href="<?php echo e(route('admin.supplier-payments.index')); ?>">
                                    <i class="fas fa-money-bill-wave"></i> مدفوعات الموردين
                                </a>
                            </li>
                            
                            
                        </ul>
                    </div>
                </li>

                <!-- Financial Management -->
                

                <!-- Reports -->
                

                <!-- Settings -->
                <li class="nav-item">
                    <a class="nav-link collapsed" data-bs-toggle="collapse" href="#settingsMenu"
                        aria-expanded="false" aria-controls="settingsMenu">
                        <i class="fas fa-cogs"></i>
                        <span>إعدادات النظام</span>
                        <i class="fas fa-chevron-down ms-auto"></i>
                    </a>
                    <div class="collapse <?php echo e(request()->routeIs('admin.settings*') ? 'show' : ''); ?>"
                        id="settingsMenu">
                        <ul class="nav flex-column ms-3">
                            <li class="nav-item">
                                <a class="nav-link <?php echo e(request()->routeIs('admin.settings.users*') ? 'active' : ''); ?>"
                                    href="<?php echo e(route('admin.settings.users')); ?>">
                                    <i class="fas fa-users-cog"></i> إدارة المستخدمين
                                </a>
                            </li>
                            
                            
                        </ul>
                    </div>
                </li>
            <?php elseif($user->isSeller()): ?>
                
                <!-- Dashboard -->
                <li class="nav-item">
                    <a class="nav-link <?php echo e(request()->routeIs('seller.dashboard') ? 'active' : ''); ?>"
                        href="<?php echo e(route('seller.dashboard')); ?>">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>لوحة التحكم</span>
                    </a>
                </li>

                <!-- Quick Sale -->
                <li class="nav-item">
                    <a class="nav-link <?php echo e(request()->routeIs('seller.quick-sale*') ? 'active' : ''); ?>"
                        href="<?php echo e(route('seller.quick-sale')); ?>">
                        <i class="fas fa-cash-register"></i>
                        <span>البيع السريع</span>
                        
                    </a>
                </li>

                <!-- Sales -->
                <li class="nav-item">
                    <a class="nav-link <?php echo e(request()->routeIs('seller.sales*') ? 'active' : ''); ?>"
                        href="<?php echo e(route('seller.sales.index')); ?>">
                        <i class="fas fa-cash-register"></i>
                        <span>المبيعات</span>
                    </a>
                </li>

                <!-- Sale Returns -->
                <li class="nav-item">
                    <a class="nav-link <?php echo e(request()->routeIs('seller.sale-returns*') ? 'active' : ''); ?>"
                        href="<?php echo e(route('seller.sale-returns.index')); ?>">
                        <i class="fas fa-undo"></i>
                        <span>مرتجعات المبيعات</span>
                    </a>
                </li>

                <!-- Customer Payments -->
                <li class="nav-item">
                    <a class="nav-link <?php echo e(request()->routeIs('seller.customer-payments*') ? 'active' : ''); ?>"
                        href="<?php echo e(route('seller.customer-payments.index')); ?>">
                        <i class="fas fa-money-bill-wave"></i>
                        <span>دفعات العملاء</span>
                    </a>
                </li>

                <!-- Customers -->
                <li class="nav-item">
                    <a class="nav-link <?php echo e(request()->routeIs('seller.customers*') ? 'active' : ''); ?>"
                        href="<?php echo e(route('seller.customers.index')); ?>">
                        <i class="fas fa-users"></i>
                        <span>العملاء</span>
                    </a>
                </li>

                <!-- Products -->
                

                <!-- Inventory -->
                <li class="nav-item">
                    <a class="nav-link <?php echo e(request()->routeIs('seller.inventory*') ? 'active' : ''); ?>"
                        href="<?php echo e(route('seller.inventory')); ?>">
                        <i class="fas fa-warehouse"></i>
                        <span>المخزون</span>
                    </a>
                </li>

                <!-- Direct Transfer -->
                <li class="nav-item">
                    <a class="nav-link <?php echo e(request()->routeIs('seller.transfers.direct.create') ? 'active' : ''); ?>"
                        href="<?php echo e(route('seller.transfers.direct.create')); ?>">
                        <i class="fas fa-plus"></i>
                        <span>نقل مباشر</span>
                    </a>
                </li>

                <!-- Transfer History -->
                <li class="nav-item">
                    <a class="nav-link <?php echo e(request()->routeIs('seller.transfers.history') ? 'active' : ''); ?>"
                        href="<?php echo e(route('seller.transfers.history')); ?>">
                        <i class="fas fa-history"></i>
                        <span>سجل النقل</span>
                    </a>
                </li>

                <!-- Cash Management -->
                

                <!-- Reports -->
                
            <?php endif; ?>

            <!-- Profile (Available to all users) -->
            

            <!-- Logout -->
            <li class="nav-item">
                <form method="POST" action="<?php echo e(route('logout')); ?>" class="d-inline">
                    <?php echo csrf_field(); ?>
                    <a class="nav-link text-danger" href="#"
                        onclick="event.preventDefault(); this.closest('form').submit();">
                        <i class="fas fa-sign-out-alt"></i>
                        <span>تسجيل الخروج</span>
                    </a>
                </form>
            </li>
        </ul>
    </nav>
</div>
<?php /**PATH D:\pos-app\resources\views/components/sidebar.blade.php ENDPATH**/ ?>