<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Lara<PERSON>\Sanctum\HasApiTokens;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, HasApiTokens, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'phone',
        'password',
        'role_id',
        'branch_id',
        'store_id',
        'is_active',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'is_active' => 'boolean',
        ];
    }

    public function role(): BelongsTo
    {
        return $this->belongsTo(Role::class);
    }

    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    public function store(): BelongsTo
    {
        return $this->belongsTo(Store::class);
    }

    public function invoices(): HasMany
    {
        return $this->hasMany(Invoice::class);
    }

    public function sales(): HasMany
    {
        return $this->hasMany(Sale::class);
    }

    public function roles(): BelongsToMany
    {
        return $this->belongsToMany(Role::class)
            ->withPivot('branch_id')
            ->withTimestamps();
    }

    public function hasRole(string $roleName): bool
    {
        // Ensure role is loaded
        if (!$this->relationLoaded('role')) {
            $this->load('role');
        }

        return $this->role && $this->role->name === $roleName;
    }

    public function hasPermission(string $permission): bool
    {
        if (!$this->role) {
            return false;
        }

        return in_array($permission, $this->role->permissions ?? []);
    }

    public function isAdmin(): bool
    {
        return $this->hasRole('admin');
    }

    public function isManager(): bool
    {
        return $this->hasRole('manager');
    }

    public function isSeller(): bool
    {
        return $this->hasRole('seller') || $this->hasRole('employee');
    }

    public function isWarehouseStaff(): bool
    {
        return $this->hasRole('warehouse_staff');
    }

    public function canAccessStoreOperations(): bool
    {
        return $this->isAdmin() || $this->isWarehouseStaff() || $this->hasPermission('stores.inventory.manage');
    }

    public function canAccessBranchSalesOperations(): bool
    {
        return $this->isAdmin() || $this->isManager() || $this->isSeller() || $this->hasPermission('branches.sales-inventory.view');
    }

    public function canManageStoreInventory(): bool
    {
        return $this->hasPermission('stores.inventory.manage');
    }

    public function canManageBranchSalesInventory(): bool
    {
        return $this->hasPermission('branches.sales-inventory.manage');
    }

    public function canAddProductsToBranchInventory(): bool
    {
        return $this->hasPermission('branches.sales-inventory.add-products') || $this->hasPermission('branches.sales-inventory.manage');
    }

    public function canApproveTransfers(): bool
    {
        return $this->hasPermission('inventory-transfers.approve');
    }

    public function canRequestTransfers(): bool
    {
        return $this->hasPermission('inventory-transfers.create') || $this->hasPermission('branches.sales-inventory.request-transfer');
    }

    public function canAccessAllBranches(): bool
    {
        return $this->isAdmin();
    }

    public function canAccessBranch(int $branchId): bool
    {
        if ($this->canAccessAllBranches()) {
            return true;
        }

        return $this->branch_id === $branchId;
    }

    public function canAccessStore(Store $store): bool
    {
        if ($this->canAccessAllBranches()) {
            return true;
        }

        // If store is independent, check if user can access independent stores
        if ($store->isIndependent()) {
            return $this->canAccessIndependentStores();
        }

        // If store belongs to a branch, check branch access
        return $this->canAccessBranch($store->branch_id);
    }

    public function canAccessIndependentStores(): bool
    {
        // Admin users can access all independent stores
        if ($this->isAdmin()) {
            return true;
        }

        // Users with warehouse staff role can access independent stores
        if ($this->isWarehouseStaff()) {
            return true;
        }

        // Users assigned to an independent store can access it
        if ($this->store_id && $this->store && $this->store->isIndependent()) {
            return true;
        }

        // Users with specific permission can access independent stores
        return $this->hasPermission('stores.independent.access');
    }

    public function getAccessibleBranchIds(): array
    {
        if ($this->canAccessAllBranches()) {
            return Branch::pluck('id')->toArray();
        }

        return $this->branch_id ? [$this->branch_id] : [];
    }

    public function getAccessibleStoreIds(): array
    {
        if ($this->canAccessAllBranches()) {
            return Store::pluck('id')->toArray();
        }

        $storeIds = [];

        // Add stores from user's branch
        if ($this->branch_id) {
            $storeIds = Store::where('branch_id', $this->branch_id)->pluck('id')->toArray();
        }

        // Add independent stores if user can access them
        if ($this->canAccessIndependentStores()) {
            $independentStoreIds = Store::whereNull('branch_id')->pluck('id')->toArray();
            $storeIds = array_merge($storeIds, $independentStoreIds);
        }

        // Add user's specific store if assigned
        if ($this->store_id && !in_array($this->store_id, $storeIds)) {
            $storeIds[] = $this->store_id;
        }

        return array_unique($storeIds);
    }

    public function getBranchId(): ?int
    {
        return $this->branch_id;
    }

    public function scopeForBranch($query, int $branchId)
    {
        return $query->where('branch_id', $branchId);
    }

    public function scopeAccessibleBy($query, User $user)
    {
        if ($user->canAccessAllBranches()) {
            return $query;
        }

        return $query->where(function ($q) use ($user) {
            // Users from the same branch
            $q->where('branch_id', $user->branch_id);

            // If user can access independent stores, include users from independent stores
            if ($user->canAccessIndependentStores()) {
                $q->orWhereHas('store', function ($storeQuery) {
                    $storeQuery->whereNull('branch_id');
                });
            }
        });
    }
}
