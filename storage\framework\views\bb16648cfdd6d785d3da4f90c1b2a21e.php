<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="d-flex justify-content-between align-items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                <?php echo e(__('تفاصيل العميل')); ?> - <?php echo e($customer->name); ?>

            </h2>
            <div>
                <a href="<?php echo e(user_route('customers.index')); ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-right"></i> <?php echo e(__('العودة إلى العملاء')); ?>

                </a>
                <a href="<?php echo e(user_route('customers.edit', $customer)); ?>" class="btn btn-primary">
                    <i class="fas fa-edit"></i> <?php echo e(__('تعديل')); ?>

                </a>
            </div>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="py-12">
        <div class="container-fluid">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    <!-- Customer Details -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0"><?php echo e(__('معلومات العميل')); ?></h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-bordered">
                                        <tr>
                                            <th><?php echo e(__('اسم العميل')); ?></th>
                                            <td><?php echo e($customer->name); ?></td>
                                        </tr>
                                        <tr>
                                            <th><?php echo e(__('البريد الإلكتروني')); ?></th>
                                            <td><?php echo e($customer->email ?: 'غير متوفر'); ?></td>
                                        </tr>
                                        <tr>
                                            <th><?php echo e(__('رقم الهاتف')); ?></th>
                                            <td><?php echo e($customer->phone ?: 'غير متوفر'); ?></td>
                                        </tr>
                                        <tr>
                                            <th><?php echo e(__('العنوان')); ?></th>
                                            <td><?php echo e($customer->address ?: 'غير متوفر'); ?></td>
                                        </tr>
                                        <tr>
                                            <th><?php echo e(__('تاريخ التسجيل')); ?></th>
                                            <td><?php echo e($customer->created_at->format('Y-m-d H:i')); ?></td>
                                        </tr>
                                        <tr>
                                            <th><?php echo e(__('آخر تحديث')); ?></th>
                                            <td><?php echo e($customer->updated_at->format('Y-m-d H:i')); ?></td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0"><?php echo e(__('ملخص العميل')); ?></h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-bordered">
                                        <tr>
                                            <th><?php echo e(__('عدد المشتريات')); ?></th>
                                            <td><?php echo e($customer->sales->count()); ?></td>
                                        </tr>
                                        <tr>
                                            <th><?php echo e(__('إجمالي المشتريات')); ?></th>
                                            <td><?php echo e(number_format($customer->sales->sum('total_amount'), 2)); ?> ج.م</td>
                                        </tr>
                                        <tr>
                                            <th><?php echo e(__('إجمالي المدفوع')); ?></th>
                                            <td><?php echo e(number_format($customer->sales->sum('paid_amount'), 2)); ?> ج.م</td>
                                        </tr>
                                        <tr>
                                            <th><?php echo e(__('المبلغ المتبقي')); ?></th>
                                            <td>
                                                <?php
                                                    $totalRemaining = $customer->sales->sum(function ($sale) {
                                                        return $sale->total_amount -
                                                            $sale->discount_amount -
                                                            $sale->paid_amount;
                                                    });
                                                ?>
                                                <?php if($totalRemaining > 0): ?>
                                                    <span
                                                        class="badge bg-warning fs-6"><?php echo e(number_format($totalRemaining, 2)); ?>

                                                        ج.م</span>
                                                <?php else: ?>
                                                    <span class="badge bg-success fs-6">مسدد بالكامل</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                        <tr>
                                            <th><?php echo e(__('متوسط قيمة المشتريات')); ?></th>
                                            <td>
                                                <?php
                                                    $avg =
                                                        $customer->sales->count() > 0
                                                            ? $customer->sales->sum('total_amount') /
                                                                $customer->sales->count()
                                                            : 0;
                                                ?>
                                                <?php echo e(number_format($avg, 2)); ?>

                                            </td>
                                        </tr>
                                        <tr>
                                            <th><?php echo e(__('آخر عملية شراء')); ?></th>
                                            <td>
                                                <?php if($customer->sales->count() > 0): ?>
                                                    <?php echo e($customer->sales->sortByDesc('created_at')->first()->created_at->format('Y-m-d H:i')); ?>

                                                <?php else: ?>
                                                    -
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                        <tr>
                                            <th><?php echo e(__('الحالة')); ?></th>
                                            <td>
                                                <?php if($customer->sales->count() > 0): ?>
                                                    <span class="badge bg-success"><?php echo e(__('نشط')); ?></span>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary"><?php echo e(__('غير نشط')); ?></span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Product Search -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0"><?php echo e(__('البحث في المنتجات المشتراة')); ?></h5>
                        </div>
                        <div class="card-body">
                            <form method="GET" action="<?php echo e(user_route('customers.show', $customer)); ?>">
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="input-group">
                                            <input type="text" name="product_search" class="form-control"
                                                placeholder="ابحث في المنتجات التي اشتراها هذا العميل..."
                                                value="<?php echo e($productSearch); ?>">
                                            <button class="btn btn-primary" type="submit">
                                                <i class="fas fa-search"></i> بحث
                                            </button>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <?php if($productSearch): ?>
                                            <a href="<?php echo e(user_route('customers.show', $customer)); ?>"
                                                class="btn btn-secondary">
                                                <i class="fas fa-times"></i> مسح البحث
                                            </a>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </form>

                            <?php if($productSearch && $purchasedProducts->count() > 0): ?>
                                <div class="mt-4">
                                    <h6>نتائج البحث: <?php echo e($purchasedProducts->count()); ?> منتج</h6>
                                    <div class="table-responsive">
                                        <table class="table table-bordered table-hover">
                                            <thead class="table-light">
                                                <tr>
                                                    <th>المنتج</th>
                                                    <th>إجمالي الكمية</th>
                                                    <th>إجمالي المبلغ</th>
                                                    <th>متوسط السعر</th>
                                                    <th>عدد مرات الشراء</th>
                                                    <th>آخر شراء</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php $__currentLoopData = $purchasedProducts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <tr>
                                                        <td>
                                                            <strong><?php echo e($item->product->name); ?></strong>
                                                            <?php if($item->product->sku): ?>
                                                                <br><small
                                                                    class="text-muted"><?php echo e($item->product->sku); ?></small>
                                                            <?php endif; ?>
                                                        </td>
                                                        <td><?php echo e($item->total_quantity); ?></td>
                                                        <td><?php echo e(number_format($item->total_amount, 2)); ?> ج.م</td>
                                                        <td><?php echo e(number_format($item->average_price, 2)); ?> ج.م</td>
                                                        <td><?php echo e($item->purchase_count); ?></td>
                                                        <td><?php echo e($item->last_purchase_date->format('Y-m-d')); ?></td>
                                                    </tr>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            <?php elseif($productSearch): ?>
                                <div class="alert alert-info mt-3">
                                    <i class="fas fa-info-circle"></i>
                                    لم يتم العثور على منتجات تحتوي على "<?php echo e($productSearch); ?>" في مشتريات هذا العميل.
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Sales History -->
                    <?php if($customer->sales->count() > 0): ?>
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0"><?php echo e(__('سجل المشتريات')); ?></h5>
                                <span class="badge bg-primary"><?php echo e($customer->sales->count()); ?> فاتورة</span>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered table-hover">
                                        <thead>
                                            <tr>
                                                <th><?php echo e(__('رقم الفاتورة')); ?></th>
                                                <th><?php echo e(__('التاريخ')); ?></th>
                                                <th><?php echo e(__('الفرع')); ?></th>
                                                <th><?php echo e(__('عدد المنتجات')); ?></th>
                                                <th><?php echo e(__('المجموع')); ?></th>
                                                <th><?php echo e(__('الخصم')); ?></th>
                                                <th><?php echo e(__('المدفوع')); ?></th>
                                                <th><?php echo e(__('المتبقي')); ?></th>
                                                
                                                <th><?php echo e(__('الإجراءات')); ?></th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php $__currentLoopData = $customer->sales->sortByDesc('created_at'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sale): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <tr>
                                                    <td><?php echo e($sale->invoice_number); ?></td>
                                                    <td><?php echo e($sale->created_at->format('Y-m-d H:i')); ?></td>
                                                    <td><?php echo e($sale->branch->name); ?></td>
                                                    <td><?php echo e($sale->items->count()); ?></td>
                                                    <td><?php echo e(number_format($sale->total_amount, 2)); ?></td>
                                                    <td><?php echo e(number_format($sale->discount_amount, 2)); ?></td>
                                                    <td><?php echo e(number_format($sale->paid_amount, 2)); ?></td>
                                                    <td>
                                                        <?php
                                                            $remaining =
                                                                $sale->total_amount -
                                                                $sale->discount_amount -
                                                                $sale->paid_amount;
                                                        ?>
                                                        <?php if($remaining > 0): ?>
                                                            <span
                                                                class="badge bg-warning"><?php echo e(number_format($remaining, 2)); ?>

                                                                ج.م</span>
                                                        <?php else: ?>
                                                            <span class="badge bg-success">مسدد</span>
                                                        <?php endif; ?>
                                                    </td>
                                                    
                                                    <td>
                                                        <a href="<?php echo e(user_route('sales.show', $sale)); ?>"
                                                            class="btn btn-sm btn-info"
                                                            title="<?php echo e(__('عرض التفاصيل')); ?>">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        <a href="<?php echo e(user_route('sales.print', $sale)); ?>"
                                                            class="btn btn-sm btn-secondary" target="_blank"
                                                            title="<?php echo e(__('طباعة')); ?>">
                                                            <i class="fas fa-print"></i>
                                                        </a>
                                                    </td>
                                                </tr>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-info">
                            <?php echo e(__('لا يوجد سجل مشتريات لهذا العميل')); ?>

                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <?php $__env->startPush('styles'); ?>
        <style>
            .rtl {
                direction: rtl;
                text-align: right;
            }

            .rtl .table th,
            .rtl .table td {
                text-align: right;
            }

            .rtl .btn-group {
                flex-direction: row-reverse;
            }

            .rtl .btn-group .btn {
                margin-right: 0;
                margin-left: 0.25rem;
            }

            .rtl .text-start {
                text-align: right !important;
            }

            .rtl .text-end {
                text-align: left !important;
            }
        </style>
    <?php $__env->stopPush(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH D:\pos-app\resources\views/customers/show.blade.php ENDPATH**/ ?>