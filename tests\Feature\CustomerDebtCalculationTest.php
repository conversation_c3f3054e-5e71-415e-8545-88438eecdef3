<?php

namespace Tests\Feature;

use App\Models\Branch;
use App\Models\BranchInventory;
use App\Models\Customer;
use App\Models\Product;
use App\Models\Sale;
use App\Models\SaleItem;
use App\Models\SaleReturn;
use App\Models\SaleReturnItem;
use App\Models\User;
use App\Services\FinancialService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class CustomerDebtCalculationTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $branch;
    protected $customer;
    protected $product;
    protected $financialService;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test data
        $this->user = User::factory()->create();
        $this->branch = Branch::factory()->create();
        $this->customer = Customer::factory()->create();
        $this->product = Product::factory()->create();
        $this->financialService = new FinancialService();

        // Create branch inventory
        BranchInventory::create([
            'branch_id' => $this->branch->id,
            'product_id' => $this->product->id,
            'quantity' => 100,
            'min_quantity' => 10,
        ]);
    }

    /** @test */
    public function it_calculates_customer_debt_correctly_without_returns()
    {
        // Create a sale with partial payment
        $sale = Sale::create([
            'invoice_number' => 'INV-001',
            'customer_id' => $this->customer->id,
            'branch_id' => $this->branch->id,
            'user_id' => $this->user->id,
            'total_amount' => 1000.00,
            'discount_amount' => 100.00, // 10% discount
            'paid_amount' => 500.00, // Partial payment
            'status' => 'completed',
        ]);

        SaleItem::create([
            'sale_id' => $sale->id,
            'product_id' => $this->product->id,
            'quantity' => 20,
            'price' => 50.00,
            'subtotal' => 1000.00,
        ]);

        // Expected debt: (1000 - 100) - 500 = 400
        $expectedDebt = 400.00;
        $actualDebt = $this->customer->getTotalOutstandingAmount();

        $this->assertEquals($expectedDebt, $actualDebt);
    }

    /** @test */
    public function it_calculates_customer_debt_correctly_with_returns()
    {
        // Create a sale with partial payment
        $sale = Sale::create([
            'invoice_number' => 'INV-002',
            'customer_id' => $this->customer->id,
            'branch_id' => $this->branch->id,
            'user_id' => $this->user->id,
            'total_amount' => 1000.00,
            'discount_amount' => 100.00, // 10% discount
            'paid_amount' => 300.00, // Partial payment
            'status' => 'completed',
        ]);

        SaleItem::create([
            'sale_id' => $sale->id,
            'product_id' => $this->product->id,
            'quantity' => 20,
            'price' => 50.00,
            'subtotal' => 1000.00,
        ]);

        // Create a return
        $saleReturn = SaleReturn::create([
            'sale_id' => $sale->id,
            'customer_id' => $this->customer->id,
            'branch_id' => $this->branch->id,
            'user_id' => $this->user->id,
            'total_amount' => 200.00,
            'refund_amount' => 200.00,
            'status' => 'completed',
            'return_type' => 'partial',
            'reason' => 'customer_request',
            'return_date' => now(),
        ]);

        SaleReturnItem::create([
            'sale_return_id' => $saleReturn->id,
            'sale_item_id' => $sale->items->first()->id,
            'product_id' => $this->product->id,
            'quantity_returned' => 4,
            'original_quantity' => 20,
            'sale_price' => 50.00,
            'total_amount' => 200.00,
            'condition' => 'good',
        ]);

        // Expected debt: (1000 - 100) - 300 - 200 = 400
        // Net amount after discount: 900
        // Paid: 300
        // Returned: 200
        // Remaining debt: 900 - 300 - 200 = 400
        $expectedDebt = 400.00;
        $actualDebt = $this->customer->getTotalOutstandingAmount();

        $this->assertEquals($expectedDebt, $actualDebt);
    }

    /** @test */
    public function it_calculates_net_sales_amount_correctly()
    {
        // Create a sale
        $sale = Sale::create([
            'invoice_number' => 'INV-003',
            'customer_id' => $this->customer->id,
            'branch_id' => $this->branch->id,
            'user_id' => $this->user->id,
            'total_amount' => 1500.00,
            'discount_amount' => 0,
            'paid_amount' => 1500.00, // Fully paid
            'status' => 'completed',
        ]);

        SaleItem::create([
            'sale_id' => $sale->id,
            'product_id' => $this->product->id,
            'quantity' => 30,
            'price' => 50.00,
            'subtotal' => 1500.00,
        ]);

        // Create a return
        $saleReturn = SaleReturn::create([
            'sale_id' => $sale->id,
            'customer_id' => $this->customer->id,
            'branch_id' => $this->branch->id,
            'user_id' => $this->user->id,
            'total_amount' => 300.00,
            'refund_amount' => 300.00,
            'status' => 'completed',
            'return_type' => 'partial',
            'reason' => 'customer_request',
            'return_date' => now(),
        ]);

        SaleReturnItem::create([
            'sale_return_id' => $saleReturn->id,
            'sale_item_id' => $sale->items->first()->id,
            'product_id' => $this->product->id,
            'quantity_returned' => 6,
            'original_quantity' => 30,
            'sale_price' => 50.00,
            'total_amount' => 300.00,
            'condition' => 'good',
        ]);

        // Expected net sales: 1500 - 300 = 1200
        $expectedNetSales = 1200.00;
        $actualNetSales = $this->customer->getNetSalesAmount();

        $this->assertEquals($expectedNetSales, $actualNetSales);

        // Since sale is fully paid and there's a return, debt should be 0
        $this->assertEquals(0.00, $this->customer->getTotalOutstandingAmount());
    }

    /** @test */
    public function it_handles_cancelled_returns_correctly()
    {
        // Create a sale with partial payment
        $sale = Sale::create([
            'invoice_number' => 'INV-004',
            'customer_id' => $this->customer->id,
            'branch_id' => $this->branch->id,
            'user_id' => $this->user->id,
            'total_amount' => 800.00,
            'discount_amount' => 0,
            'paid_amount' => 400.00, // Partial payment
            'status' => 'completed',
        ]);

        SaleItem::create([
            'sale_id' => $sale->id,
            'product_id' => $this->product->id,
            'quantity' => 16,
            'price' => 50.00,
            'subtotal' => 800.00,
        ]);

        // Create a cancelled return (should not affect debt)
        $saleReturn = SaleReturn::create([
            'sale_id' => $sale->id,
            'customer_id' => $this->customer->id,
            'branch_id' => $this->branch->id,
            'user_id' => $this->user->id,
            'total_amount' => 200.00,
            'refund_amount' => 200.00,
            'status' => 'cancelled', // Cancelled status
            'return_type' => 'partial',
            'reason' => 'customer_request',
            'return_date' => now(),
        ]);

        // Expected debt: 800 - 400 = 400 (cancelled return should not affect this)
        $expectedDebt = 400.00;
        $actualDebt = $this->customer->getTotalOutstandingAmount();

        $this->assertEquals($expectedDebt, $actualDebt);

        // Net sales should also not be affected by cancelled return
        $this->assertEquals(800.00, $this->customer->getNetSalesAmount());
    }

    /** @test */
    public function financial_service_calculates_customer_summary_correctly()
    {
        // Create multiple customers with different debt scenarios
        $customer2 = Customer::factory()->create();

        // Customer 1: Has debt
        $sale1 = Sale::create([
            'invoice_number' => 'INV-005',
            'customer_id' => $this->customer->id,
            'branch_id' => $this->branch->id,
            'user_id' => $this->user->id,
            'total_amount' => 1000.00,
            'discount_amount' => 0,
            'paid_amount' => 600.00,
            'status' => 'completed',
        ]);

        // Customer 2: Fully paid
        $sale2 = Sale::create([
            'invoice_number' => 'INV-006',
            'customer_id' => $customer2->id,
            'branch_id' => $this->branch->id,
            'user_id' => $this->user->id,
            'total_amount' => 500.00,
            'discount_amount' => 0,
            'paid_amount' => 500.00,
            'status' => 'completed',
        ]);

        $summary = $this->financialService->getCustomerFinancialSummary($this->user);

        $this->assertEquals(2, $summary['total_customers']);
        $this->assertEquals(400.00, $summary['total_owed_to_us']); // Only customer 1 owes money
        $this->assertEquals(1, $summary['customers_with_debt']);
        $this->assertEquals(1500.00, $summary['total_net_sales']); // 1000 + 500
    }
}
