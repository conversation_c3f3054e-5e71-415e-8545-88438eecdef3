<x-app-layout>
    <x-slot name="header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                    <i class="fas fa-history text-info"></i> {{ __('سجل عمليات النقل المباشر') }}
                </h2>
                <p class="text-muted small mb-0">سجل جميع عمليات النقل المباشر المنفذة فوريًا</p>
            </div>
            <div>
                <a href="{{ user_route('transfers.direct.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> نقل مباشر جديد
                </a>
            </div>
        </div>
    </x-slot>

    <div class="container-fluid">
        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-xl-4 col-md-6 mb-4">
                <div class="card border-left-success shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                    عمليات النقل المباشر
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['total_completed'] }}
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-4 col-md-6 mb-4">
                <div class="card border-left-primary shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                    نقل فوري ومباشر
                                </div>
                                <div class="h6 mb-0 font-weight-bold text-gray-800">بدون انتظار موافقة
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-bolt fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-4 col-md-6 mb-4">
                <div class="card border-left-info shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                    إجمالي الكمية المنقولة
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    {{ number_format($stats['total_quantity'], 2) }}</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-boxes fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-filter"></i> البحث والتصفية
                </h6>
            </div>
            <div class="card-body">
                <form method="GET" action="{{ user_route('transfers.history') }}">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label for="search" class="form-label">البحث</label>
                            <input type="text" class="form-control" id="search" name="search"
                                value="{{ request('search') }}" placeholder="اسم المنتج أو الكود">
                        </div>
                        <div class="col-md-2 mb-3">
                            <label for="status" class="form-label">الحالة</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">جميع الحالات</option>
                                <option value="completed" {{ request('status') == 'completed' ? 'selected' : '' }}>مكتمل
                                </option>
                                <option value="cancelled" {{ request('status') == 'cancelled' ? 'selected' : '' }}>ملغي
                                </option>
                            </select>
                        </div>
                        <div class="col-md-2 mb-3">
                            <label for="date_from" class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" id="date_from" name="date_from"
                                value="{{ request('date_from') }}">
                        </div>
                        <div class="col-md-2 mb-3">
                            <label for="date_to" class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" id="date_to" name="date_to"
                                value="{{ request('date_to') }}">
                        </div>
                        <div class="col-md-3 mb-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i> بحث
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Transfer History Table -->
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-info">
                    <i class="fas fa-table"></i> سجل عمليات النقل المباشر
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>رقم النقل</th>
                                <th>النوع</th>
                                <th>من</th>
                                <th>إلى</th>
                                <th>المنتجات</th>
                                <th>إجمالي الكمية</th>
                                <th>نفذ بواسطة</th>
                                <th>التاريخ</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($transfers as $transfer)
                                <tr>
                                    <td class="fw-bold">
                                        <a href="{{ user_route('transfers.show', $transfer) }}" class="text-primary">
                                            #{{ $transfer->transfer_number }}
                                        </a>
                                    </td>
                                    <td>
                                        <span
                                            class="badge bg-info">{{ ucfirst(str_replace('_', ' إلى ', $transfer->type)) }}</span>
                                    </td>
                                    <td>
                                        @if ($transfer->source_type == 'branch')
                                            <span class="badge bg-primary">فرع</span>
                                            {{ $transfer->sourceBranch->name ?? 'غير محدد' }}
                                        @else
                                            <span class="badge bg-success">مخزن</span>
                                            {{ $transfer->sourceStore->name ?? 'غير محدد' }}
                                        @endif
                                    </td>
                                    <td>
                                        @if ($transfer->destination_type == 'branch')
                                            <span class="badge bg-primary">فرع</span>
                                            {{ $transfer->destinationBranch->name ?? 'غير محدد' }}
                                        @else
                                            <span class="badge bg-success">مخزن</span>
                                            {{ $transfer->destinationStore->name ?? 'غير محدد' }}
                                        @endif
                                    </td>
                                    <td class="text-center">{{ $transfer->items->count() }}</td>
                                    <td class="text-center">
                                        {{ number_format($transfer->items->sum('received_quantity'), 2) }}
                                    </td>
                                    <td>{{ $transfer->requestedBy->name }}</td>
                                    <td>{{ $transfer->created_at->format('Y-m-d H:i') }}</td>
                                    <td>
                                        @if ($transfer->status == 'completed')
                                            <span class="badge bg-success">مكتمل</span>
                                        @elseif($transfer->status == 'cancelled')
                                            <span class="badge bg-danger">ملغي</span>
                                        @else
                                            <span class="badge bg-warning">معلق</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ user_route('transfers.show', $transfer) }}"
                                                class="btn btn-outline-info" title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="10" class="text-center py-4">
                                        <i class="fas fa-history fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">لا توجد عمليات نقل مباشر في السجل</p>
                                        <a href="{{ user_route('transfers.direct.create') }}"
                                            class="btn btn-primary">
                                            <i class="fas fa-plus"></i> إنشاء عملية نقل مباشر
                                        </a>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                @if ($transfers->hasPages())
                    <div class="mt-4">
                        {{ $transfers->appends(request()->query())->links() }}
                    </div>
                @endif
            </div>
        </div>
    </div>
</x-app-layout>
