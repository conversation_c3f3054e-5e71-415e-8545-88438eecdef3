@extends('layouts.app')

@section('title', 'ملخص مدفوعات المورد - ' . $supplier->name)

@section('content')
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-0 text-gray-800">ملخص مدفوعات المورد</h1>
                <p class="text-muted mb-0">{{ $supplier->name }}</p>
            </div>
            <div>
                <a href="{{ user_route('payment-reports.index') }}" class="btn btn-outline-secondary me-2">
                    <i class="fas fa-arrow-right me-2"></i>العودة للتقارير
                </a>
                <a href="{{ user_route('suppliers.show', $supplier) }}" class="btn btn-outline-info">
                    <i class="fas fa-user me-2"></i>عرض المورد
                </a>
            </div>
        </div>

        <!-- Date Filter -->
        <div class="card shadow mb-4">
            <div class="card-body">
                <form method="GET" class="row g-3 align-items-end">
                    <div class="col-md-4">
                        <label class="form-label">من تاريخ</label>
                        <input type="date" name="date_from" class="form-control" value="{{ $dateFrom }}">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">إلى تاريخ</label>
                        <input type="date" name="date_to" class="form-control" value="{{ $dateTo }}">
                    </div>
                    <div class="col-md-4">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-search me-2"></i>تطبيق الفلتر
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Supplier Info and Statistics -->
        <div class="row mb-4">
            <!-- Supplier Information -->
            <div class="col-lg-4 mb-4">
                <div class="card shadow h-100">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">معلومات المورد</h6>
                    </div>
                    <div class="card-body">
                        <div class="text-center mb-3">
                            <div class="avatar-circle bg-primary text-white mx-auto mb-2"
                                style="width: 60px; height: 60px; font-size: 24px;">
                                {{ substr($supplier->name, 0, 1) }}
                            </div>
                            <h5 class="fw-bold">{{ $supplier->name }}</h5>
                        </div>

                        @if ($supplier->phone)
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-phone text-muted me-2"></i>
                                <span>{{ $supplier->phone }}</span>
                            </div>
                        @endif

                        @if ($supplier->email)
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-envelope text-muted me-2"></i>
                                <span>{{ $supplier->email }}</span>
                            </div>
                        @endif

                        @if ($supplier->address)
                            <div class="d-flex align-items-start mb-2">
                                <i class="fas fa-map-marker-alt text-muted me-2 mt-1"></i>
                                <span>{{ $supplier->address }}</span>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Financial Statistics -->
            <div class="col-lg-8">
                <div class="row">
                    <div class="col-md-4 mb-4">
                        <div class="card border-left-info shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                            إجمالي المشتريات
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            {{ format_currency($totalPurchases) }}
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4 mb-4">
                        <div class="card border-left-success shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                            إجمالي المدفوع
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            {{ format_currency($totalPaid) }}
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-money-bill-wave fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4 mb-4">
                        <div class="card border-left-danger shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                            الرصيد المعلق
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            {{ format_currency($totalOutstanding) }}
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Outstanding Purchases -->
            <div class="col-lg-6 mb-4">
                <div class="card shadow">
                    <div class="card-header py-3 d-flex justify-content-between align-items-center">
                        <h6 class="m-0 font-weight-bold text-primary">المشتريات المعلقة</h6>
                        @if ($outstandingPurchases->count() > 0)
                            <span class="badge bg-danger">{{ $outstandingPurchases->count() }}</span>
                        @endif
                    </div>
                    <div class="card-body">
                        @if ($outstandingPurchases->count() > 0)
                            @foreach ($outstandingPurchases as $purchase)
                                <div class="d-flex align-items-center justify-content-between mb-3 p-2 border rounded">
                                    <div>
                                        <div class="fw-bold">{{ $purchase->invoice_number }}</div>
                                        <small class="text-muted">{{ $purchase->purchase_date->format('Y-m-d') }}</small>
                                    </div>
                                    <div class="text-end">
                                        <div class="fw-bold text-danger">
                                            {{ format_currency($purchase->actual_remaining_amount) }}</div>
                                        <small class="text-muted">من {{ format_currency($purchase->total_amount) }}</small>
                                    </div>
                                    <div>
                                        <a href="{{ user_route('supplier-payments.create', $purchase) }}"
                                            class="btn btn-warning btn-sm">
                                            <i class="fas fa-money-bill-wave"></i>
                                        </a>
                                    </div>
                                </div>
                            @endforeach
                        @else
                            <div class="text-center py-3">
                                <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                                <p class="text-muted">لا توجد مشتريات معلقة</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Recent Payments -->
            <div class="col-lg-6 mb-4">
                <div class="card shadow">
                    <div class="card-header py-3 d-flex justify-content-between align-items-center">
                        <h6 class="m-0 font-weight-bold text-primary">آخر المدفوعات</h6>
                        @if ($recentPayments->count() > 0)
                            <span class="badge bg-success">{{ $recentPayments->count() }}</span>
                        @endif
                    </div>
                    <div class="card-body">
                        @if ($recentPayments->count() > 0)
                            @foreach ($recentPayments as $payment)
                                <div class="d-flex align-items-center justify-content-between mb-3 p-2 border rounded">
                                    <div>
                                        <div class="fw-bold">عملية #{{ $payment->transactionable->id }}</div>
                                        <small class="text-muted">{{ $payment->created_at->format('Y-m-d H:i') }}</small>
                                    </div>
                                    <div class="text-end">
                                        <div class="fw-bold text-success">{{ format_currency($payment->amount) }}</div>
                                        <small class="text-muted">{{ $payment->user->name ?? 'غير محدد' }}</small>
                                    </div>
                                    <div>
                                        <a href="{{ user_route('supplier-payments.show', $payment->transactionable) }}"
                                            class="btn btn-info btn-sm">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </div>
                                </div>
                            @endforeach
                        @else
                            <div class="text-center py-3">
                                <i class="fas fa-receipt fa-2x text-muted mb-2"></i>
                                <p class="text-muted">لا توجد مدفوعات حديثة</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        .avatar-circle {
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        .border-left-info {
            border-left: 0.25rem solid #36b9cc !important;
        }

        .border-left-success {
            border-left: 0.25rem solid #1cc88a !important;
        }

        .border-left-danger {
            border-left: 0.25rem solid #e74a3b !important;
        }
    </style>
@endsection
