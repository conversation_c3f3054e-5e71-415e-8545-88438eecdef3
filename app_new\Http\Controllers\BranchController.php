<?php

namespace App\Http\Controllers;

use App\Models\Branch;
use App\Models\BranchInventory;
use App\Models\Category;
use App\Models\Product;
use App\Models\InventoryTransfer;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\Helpers\AlertHelper;

class BranchController extends Controller
{
    public function index(Request $request)
    {
        // Check if this is an admin request
        if (Auth::user() && Auth::user()->isAdmin() && request()->routeIs('admin.*')) {
            return $this->adminIndex($request);
        }

        $branches = Branch::latest()->paginate(10);
        return view('branches.index', compact('branches'));
    }

    /**
     * Admin-specific branch listing with enhanced features
     */
    private function adminIndex(Request $request)
    {
        $query = Branch::with(['stores', 'users', 'sales', 'purchases', 'expenses']);

        // Apply search filter
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                    ->orWhere('code', 'like', "%{$search}%")
                    ->orWhere('address', 'like', "%{$search}%");
            });
        }

        // Apply status filter
        if ($request->filled('status')) {
            $status = $request->get('status');
            if ($status === 'active') {
                $query->where('is_active', true);
            } elseif ($status === 'inactive') {
                $query->where('is_active', false);
            }
        }

        // Apply sorting
        $sort = $request->get('sort', 'name');
        switch ($sort) {
            case 'created_at':
                $query->orderBy('created_at', 'desc');
                break;
            case 'sales':
                $query->withCount('sales')->orderBy('sales_count', 'desc');
                break;
            default:
                $query->orderBy('name');
                break;
        }

        $branches = $query->get();

        return view('branches.index', compact('branches'));
    }

    public function create()
    {
        return view('branches.create');
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'address' => ['nullable', 'string'],
            'phone' => ['nullable', 'string', 'max:20'],
            'email' => ['nullable', 'email'],
            'opening_balance' => ['nullable', 'numeric', 'min:0'],
        ]);

        $validated['code'] = 'BR-' . strtoupper(Str::random(6));
        $validated['is_active'] = true;

        Branch::create($validated);

        return redirect()->to(user_route('branches.index'))
            ->with('success', 'تم إضافة الفرع بنجاح');
    }

    public function show(Branch $branch)
    {
        $branch->load([
            'inventory',
            'branchInventories.product',
            'sales' => function ($query) {
                $query->with('customer')->latest();
            },
            'purchases' => function ($query) {
                $query->with('supplier')->latest();
            },
            'expenses',
            'cashTransactions',
            'users',
            'stores' => function ($query) {
                $query->with('users');
            }
        ]);

        // Check if this is an admin request
        if (Auth::user() && Auth::user()->isAdmin() && request()->routeIs('admin.*')) {
            return view('admin.branches.show', compact('branch'));
        }

        return view('branches.show', compact('branch'));
    }

    public function edit(Branch $branch)
    {
        return view('branches.edit', compact('branch'));
    }

    public function update(Request $request, Branch $branch)
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'address' => ['nullable', 'string'],
            'phone' => ['nullable', 'string', 'max:20'],
            'email' => ['nullable', 'email'],
            'opening_balance' => ['nullable', 'numeric', 'min:0'],
            'is_active' => ['boolean'],
        ]);

        $branch->update($validated);

        return redirect()->to(user_route('branches.index'))
            ->with('success', 'تم تحديث الفرع بنجاح');
    }

    public function destroy(Branch $branch)
    {
        // Check if branch has any related records
        if (
            $branch->products()->exists() ||
            $branch->sales()->exists() ||
            $branch->purchases()->exists() ||
            $branch->expenses()->exists() ||
            $branch->cashTransactions()->exists() ||
            $branch->users()->exists()
        ) {
            return redirect()->to(user_route('branches.index'))
                ->with('error', 'لا يمكن حذف الفرع لوجود بيانات مرتبطة به');
        }

        $branch->delete();

        return redirect()->to(user_route('branches.index'))
            ->with('success', 'تم حذف الفرع بنجاح');
    }

    /**
     * Get branch analytics data for admin
     */
    public function analytics(Branch $branch)
    {
        $branch->load(['sales', 'purchases', 'expenses', 'stores', 'users']);

        $analytics = [
            'total_sales' => $branch->sales->sum('total_amount'),
            'total_purchases' => $branch->purchases->sum('total_amount'),
            'total_expenses' => $branch->expenses->sum('amount'),
            'sales_count' => $branch->sales->count(),
            'purchases_count' => $branch->purchases->count(),
            'stores_count' => $branch->stores->count(),
            'users_count' => $branch->users->count(),
            'net_profit' => $branch->sales->sum('total_amount') - $branch->purchases->sum('total_amount') - $branch->expenses->sum('amount'),

            // Daily stats
            'today_sales' => $branch->sales->where('created_at', '>=', now()->startOfDay())->sum('total_amount'),
            'today_purchases' => $branch->purchases->where('created_at', '>=', now()->startOfDay())->sum('total_amount'),

            // Monthly stats
            'month_sales' => $branch->sales->where('created_at', '>=', now()->startOfMonth())->sum('total_amount'),
            'month_purchases' => $branch->purchases->where('created_at', '>=', now()->startOfMonth())->sum('total_amount'),
        ];

        return response()->json($analytics);
    }

    /**
     * Get branch performance comparison
     */
    public function performance()
    {
        $branches = Branch::with(['sales', 'purchases', 'expenses'])
            ->where('is_active', true)
            ->get()
            ->map(function ($branch) {
                return [
                    'id' => $branch->id,
                    'name' => $branch->name,
                    'total_sales' => $branch->sales->sum('total_amount'),
                    'total_purchases' => $branch->purchases->sum('total_amount'),
                    'net_profit' => $branch->sales->sum('total_amount') - $branch->purchases->sum('total_amount'),
                    'sales_count' => $branch->sales->count(),
                    'stores_count' => $branch->stores->count(),
                ];
            })
            ->sortByDesc('total_sales');

        return response()->json($branches->values());
    }

    /**
     * Show branch sales inventory management
     */
    public function salesInventory(Branch $branch, Request $request)
    {
        $query = BranchInventory::where('branch_id', $branch->id)
            ->with(['product.category']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->whereHas('product', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                    ->orWhere('sku', 'like', "%{$search}%");
            });
        }

        // Filter by stock status
        if ($request->filled('stock_status')) {
            switch ($request->stock_status) {
                case 'low':
                    $query->whereRaw('quantity <= 10'); // Low stock threshold
                    break;
                case 'out':
                    $query->where('quantity', '<=', 0);
                    break;
                case 'available':
                    $query->where('quantity', '>', 0);
                    break;
            }
        }

        // Filter by category
        if ($request->filled('category_id')) {
            $query->whereHas('product', function ($q) use ($request) {
                $q->where('category_id', $request->category_id);
            });
        }

        $inventory = $query->orderBy('quantity', 'asc')->paginate(20);

        // Get categories for filter
        $categories = Product::whereHas('branchInventories', function ($q) use ($branch) {
            $q->where('branch_id', $branch->id);
        })->with('category')->get()->pluck('category')->unique('id');

        // Get inventory statistics
        $stats = [
            'total_products' => $branch->branchInventories()->count(),
            'low_stock_count' => $branch->branchInventories()->whereRaw('quantity <= 10')->count(),
            'out_of_stock_count' => $branch->branchInventories()->where('quantity', '<=', 0)->count(),
            'total_value' => $branch->branchInventories()
                ->join('products', 'products.id', '=', 'branch_inventory.product_id')
                ->sum(DB::raw('branch_inventory.quantity * branch_inventory.cost_price')),
            'pending_transfers' => InventoryTransfer::where('destination_type', 'branch')
                ->where('destination_id', $branch->id)
                ->where('status', 'pending')
                ->count(),
        ];

        return view('branches.sales-inventory', compact('branch', 'inventory', 'categories', 'stats'));
    }

    /**
     * Add product to branch inventory
     */
    public function addProduct(Branch $branch)
    {
        $products = Product::where('is_active', true)
            ->whereNotIn('id', function ($query) use ($branch) {
                $query->select('product_id')
                    ->from('branch_inventory')
                    ->where('branch_id', $branch->id);
            })
            ->with('category')
            ->get();

        $categories = Category::all();

        return view('branches.add-product', compact('branch', 'products', 'categories'));
    }

    /**
     * Store product in branch inventory
     */
    public function storeProduct(Branch $branch, Request $request)
    {
        // First, filter out disabled/unchecked products (they come as empty arrays)
        $products = collect($request->input('products', []))
            ->filter(function ($productData) {
                return !empty($productData) &&
                    isset($productData['product_id']) &&
                    isset($productData['quantity']) &&
                    isset($productData['cost_price']);
            });

        if ($products->isEmpty()) {
            return back()->withErrors(['products' => 'يرجى اختيار منتج واحد على الأقل']);
        }

        // Basic validation that products array exists
        $request->validate([
            'products' => 'required|array|min:1',
        ]);

        // Custom validation for each product
        $errors = [];
        foreach ($products as $productId => $productData) {
            if (!isset($productData['product_id']) || !Product::where('id', $productData['product_id'])->where('is_active', true)->exists()) {
                $errors["products.{$productId}.product_id"] = 'المنتج المحدد غير صحيح';
            }

            if (!isset($productData['quantity']) || !is_numeric($productData['quantity']) || $productData['quantity'] <= 0) {
                $errors["products.{$productId}.quantity"] = 'يرجى إدخال كمية صحيحة';
            }

            if (!isset($productData['cost_price']) || !is_numeric($productData['cost_price']) || $productData['cost_price'] <= 0) {
                $errors["products.{$productId}.cost_price"] = 'يرجى إدخال سعر تكلفة صحيح';
            }

            if (isset($productData['sale_price_1']) && $productData['sale_price_1'] !== '' && (!is_numeric($productData['sale_price_1']) || $productData['sale_price_1'] < 0)) {
                $errors["products.{$productId}.sale_price_1"] = 'سعر البيع 1 غير صحيح';
            }

            if (isset($productData['sale_price_2']) && $productData['sale_price_2'] !== '' && (!is_numeric($productData['sale_price_2']) || $productData['sale_price_2'] < 0)) {
                $errors["products.{$productId}.sale_price_2"] = 'سعر البيع 2 غير صحيح';
            }

            if (isset($productData['sale_price_3']) && $productData['sale_price_3'] !== '' && (!is_numeric($productData['sale_price_3']) || $productData['sale_price_3'] < 0)) {
                $errors["products.{$productId}.sale_price_3"] = 'سعر البيع 3 غير صحيح';
            }
        }

        if (!empty($errors)) {
            return back()->withErrors($errors);
        }

        DB::beginTransaction();
        try {
            foreach ($products as $productData) {
                $product = Product::find($productData['product_id']);

                // Check if product already exists in branch inventory
                $existingInventory = BranchInventory::where('branch_id', $branch->id)
                    ->where('product_id', $productData['product_id'])
                    ->first();

                if ($existingInventory) {
                    // Update existing inventory with weighted average cost price
                    $currentTotalValue = $existingInventory->quantity * $existingInventory->cost_price;
                    $newTotalValue = $productData['quantity'] * $productData['cost_price'];
                    $totalQuantity = $existingInventory->quantity + $productData['quantity'];
                    $weightedAverageCost = ($currentTotalValue + $newTotalValue) / $totalQuantity;

                    $existingInventory->update([
                        'quantity' => $totalQuantity,
                        'cost_price' => $weightedAverageCost,
                        'sale_price_1' => $productData['sale_price_1'] ?? $product->selling_price,
                        'sale_price_2' => $productData['sale_price_2'],
                        'sale_price_3' => $productData['sale_price_3'],
                    ]);
                } else {
                    // Create new inventory record
                    BranchInventory::create([
                        'branch_id' => $branch->id,
                        'product_id' => $productData['product_id'],
                        'quantity' => $productData['quantity'],
                        'cost_price' => $productData['cost_price'],
                        'sale_price_1' => $productData['sale_price_1'] ?? $product->selling_price,
                        'sale_price_2' => $productData['sale_price_2'],
                        'sale_price_3' => $productData['sale_price_3'],
                    ]);
                }
            }

            DB::commit();

            return redirect()->to(user_route('branches.sales-inventory', $branch))
                ->with('success', 'تم إضافة المنتجات بنجاح إلى مخزون الفرع');
        } catch (\Exception $e) {
            DB::rollback();
            return back()->withErrors(['error' => 'حدث خطأ أثناء إضافة المنتجات: ' . $e->getMessage()]);
        }
    }

    /**
     * Receive products from store transfers
     */
    public function receiveProducts(Branch $branch)
    {
        $pendingTransfers = InventoryTransfer::where('destination_type', 'branch')
            ->where('destination_id', $branch->id)
            ->whereIn('status', ['approved', 'in_transit'])
            ->with(['items.product', 'source'])
            ->get();

        return view('branches.receive-products', compact('branch', 'pendingTransfers'));
    }

    /**
     * Update sales prices for branch inventory
     */
    public function updateSalesPrices(Branch $branch, BranchInventory $inventory, Request $request)
    {
        $validated = $request->validate([
            'sale_price_1' => 'required|numeric|min:0',
            'sale_price_2' => 'nullable|numeric|min:0',
            'sale_price_3' => 'nullable|numeric|min:0',
        ]);

        try {
            $inventory->update($validated);
            AlertHelper::success('تم تحديث أسعار البيع بنجاح');
            return back();
        } catch (\Exception $e) {
            AlertHelper::error('حدث خطأ أثناء تحديث أسعار البيع: ' . $e->getMessage());
            return back();
        }
    }

    /**
     * Update minimum stock level for branch inventory
     */
    public function updateMinStock(Branch $branch, BranchInventory $inventory, Request $request)
    {
        $validated = $request->validate([
            'minimum_stock' => 'required|numeric|min:0',
        ]);

        $inventory->update([
            'minimum_stock' => $validated['minimum_stock']
        ]);

        AlertHelper::success('تم تحديث الحد الأدنى للمخزون بنجاح');
        return back();
    }

    /**
     * Adjust inventory quantity for sales corrections
     */
    public function adjustInventory(Branch $branch, BranchInventory $inventory, Request $request)
    {
        $validated = $request->validate([
            'adjustment_type' => 'required|in:add,subtract',
            'quantity' => 'required|numeric|min:0.01',
            'reason' => 'required|string|max:255',
        ]);

        DB::beginTransaction();
        try {
            $oldQuantity = $inventory->quantity;

            if ($validated['adjustment_type'] === 'add') {
                $inventory->quantity += $validated['quantity'];
            } else {
                if ($inventory->quantity < $validated['quantity']) {
                    AlertHelper::error('الكمية المطلوب خصمها أكبر من الكمية المتاحة');
                    return back();
                }
                $inventory->quantity -= $validated['quantity'];
            }

            $inventory->save();

            // Log the adjustment (you can create an InventoryAdjustment model for this)
            // For now, we'll just show success message

            DB::commit();

            $action = $validated['adjustment_type'] === 'add' ? 'إضافة' : 'خصم';
            AlertHelper::success("تم {$action} الكمية بنجاح. السبب: {$validated['reason']}");
            return back();
        } catch (\Exception $e) {
            DB::rollback();
            AlertHelper::error('حدث خطأ أثناء تعديل المخزون: ' . $e->getMessage());
            return back();
        }
    }

    /**
     * Toggle branch status
     */
    public function toggleStatus(Branch $branch)
    {
        $branch->update(['is_active' => !$branch->is_active]);

        return response()->json([
            'success' => true,
            'message' => 'Branch status updated successfully.',
            'is_active' => $branch->is_active
        ]);
    }
}
