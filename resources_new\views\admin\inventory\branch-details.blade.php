<x-app-layout>
    <x-slot name="header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                    <i class="fas fa-building text-primary"></i> {{ __('مخزون فرع') }} - {{ $branch->name }}
                </h2>
                <p class="text-muted small mb-0">تفاصيل مخزون فرع {{ $branch->name }} ({{ $branch->code }})</p>
            </div>
            <div>
                <a href="{{ route('admin.inventory.branches') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right"></i> العودة للفروع
                </a>
                <a href="{{ route('admin.inventory-transfers.create') }}?source_type=branch&source_id={{ $branch->id }}" class="btn btn-primary">
                    <i class="fas fa-exchange-alt"></i> نقل من هذا الفرع
                </a>
            </div>
        </div>
    </x-slot>

    <div class="container-fluid">
        <!-- Branch Info Card -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-left-primary shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-info-circle"></i> معلومات الفرع
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="text-center">
                                    <div class="h4 text-primary">{{ $inventory->total() }}</div>
                                    <div class="small text-muted">إجمالي المنتجات</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <div class="h4 text-success">
                                        {{ number_format($inventory->sum(function($item) { return $item->quantity * ($item->cost_price ?? 0); }), 2) }} ج.م
                                    </div>
                                    <div class="small text-muted">إجمالي القيمة</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <div class="h4 text-warning">
                                        {{ $inventory->where('quantity', '<=', 10)->count() }}
                                    </div>
                                    <div class="small text-muted">مخزون منخفض</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <div class="h4 text-info">{{ $branch->code }}</div>
                                    <div class="small text-muted">كود الفرع</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Search -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-search"></i> البحث في المخزون
                </h6>
            </div>
            <div class="card-body">
                <form method="GET" action="{{ route('admin.inventory.branch-details', $branch) }}">
                    <div class="row">
                        <div class="col-md-8 mb-3">
                            <input type="text" class="form-control" name="search" 
                                   value="{{ request('search') }}" placeholder="البحث عن منتج بالاسم أو الكود">
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i> بحث
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Inventory Table -->
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-table"></i> تفاصيل المخزون
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>المنتج</th>
                                <th>الكود</th>
                                <th>التصنيف</th>
                                <th>الكمية</th>
                                <th>سعر التكلفة</th>
                                <th>إجمالي القيمة</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($inventory as $item)
                                <tr>
                                    <td class="fw-bold">{{ $item->product->name }}</td>
                                    <td>{{ $item->product->sku ?? '-' }}</td>
                                    <td>{{ $item->product->category->name ?? 'غير محدد' }}</td>
                                    <td class="text-center">
                                        <span class="badge {{ $item->quantity <= 10 ? 'bg-warning' : 'bg-success' }}">
                                            {{ number_format($item->quantity, 2) }}
                                        </span>
                                    </td>
                                    <td>{{ number_format($item->cost_price ?? 0, 2) }} ج.م</td>
                                    <td class="fw-bold">{{ number_format($item->quantity * ($item->cost_price ?? 0), 2) }} ج.م</td>
                                    <td>
                                        @if($item->quantity <= 0)
                                            <span class="badge bg-danger">نفد المخزون</span>
                                        @elseif($item->quantity <= 10)
                                            <span class="badge bg-warning">مخزون منخفض</span>
                                        @else
                                            <span class="badge bg-success">متوفر</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ route('admin.products.show', $item->product) }}" 
                                               class="btn btn-outline-info" title="عرض المنتج">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('admin.inventory-transfers.create') }}?source_type=branch&source_id={{ $branch->id }}&product_id={{ $item->product->id }}" 
                                               class="btn btn-outline-primary" title="نقل">
                                                <i class="fas fa-exchange-alt"></i>
                                            </a>
                                            @if($item->quantity <= 10)
                                                <a href="{{ route('admin.purchases.create') }}?product={{ $item->product->id }}" 
                                                   class="btn btn-outline-success" title="شراء">
                                                    <i class="fas fa-shopping-cart"></i>
                                                </a>
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="8" class="text-center py-4">
                                        <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">لا توجد منتجات في مخزون هذا الفرع</p>
                                        <a href="{{ route('admin.inventory-transfers.create') }}?destination_type=branch&destination_id={{ $branch->id }}" 
                                           class="btn btn-primary">
                                            <i class="fas fa-plus"></i> نقل منتجات إلى هذا الفرع
                                        </a>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                @if($inventory->hasPages())
                    <div class="mt-4">
                        {{ $inventory->appends(request()->query())->links() }}
                    </div>
                @endif
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card border-left-info shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-info">
                            <i class="fas fa-bolt"></i> إجراءات سريعة
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="{{ route('admin.inventory-transfers.create') }}?source_type=branch&source_id={{ $branch->id }}" 
                               class="btn btn-primary">
                                <i class="fas fa-arrow-right"></i> نقل من هذا الفرع
                            </a>
                            <a href="{{ route('admin.inventory-transfers.create') }}?destination_type=branch&destination_id={{ $branch->id }}" 
                               class="btn btn-success">
                                <i class="fas fa-arrow-left"></i> نقل إلى هذا الفرع
                            </a>
                            <a href="{{ route('admin.purchases.create') }}?branch={{ $branch->id }}" 
                               class="btn btn-info">
                                <i class="fas fa-shopping-cart"></i> شراء لهذا الفرع
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card border-left-warning shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-warning">
                            <i class="fas fa-exclamation-triangle"></i> تنبيهات
                        </h6>
                    </div>
                    <div class="card-body">
                        @php
                            $lowStockCount = $inventory->where('quantity', '<=', 10)->count();
                            $outOfStockCount = $inventory->where('quantity', '<=', 0)->count();
                        @endphp
                        
                        @if($lowStockCount > 0)
                            <div class="alert alert-warning mb-2">
                                <i class="fas fa-exclamation-triangle"></i>
                                {{ $lowStockCount }} منتج بمخزون منخفض
                            </div>
                        @endif
                        
                        @if($outOfStockCount > 0)
                            <div class="alert alert-danger mb-2">
                                <i class="fas fa-times-circle"></i>
                                {{ $outOfStockCount }} منتج نفد مخزونه
                            </div>
                        @endif
                        
                        @if($lowStockCount == 0 && $outOfStockCount == 0)
                            <div class="alert alert-success mb-0">
                                <i class="fas fa-check-circle"></i>
                                جميع المنتجات متوفرة بكميات كافية
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
