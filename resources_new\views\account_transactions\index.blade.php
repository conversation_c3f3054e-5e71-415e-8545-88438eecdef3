<x-app-layout>
    <div class="container-fluid">
        <!-- <PERSON> Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h2 class="h3 mb-0">معاملات الحسابات</h2>
                    <a href="{{ route('account-transactions.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> إضافة معاملة جديدة
                    </a>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <form method="GET" action="{{ route('account-transactions.index') }}">
                            <div class="row">
                                <div class="col-md-3">
                                    <label for="search" class="form-label">البحث</label>
                                    <input type="text" class="form-control" id="search" name="search"
                                        value="{{ request('search') }}" placeholder="رقم المرجع، الوصف...">
                                </div>
                                <div class="col-md-2">
                                    <label for="account_id" class="form-label">الحساب</label>
                                    <select class="form-select" id="account_id" name="account_id">
                                        <option value="">جميع الحسابات</option>
                                        @foreach ($accounts as $account)
                                            <option value="{{ $account->id }}"
                                                {{ request('account_id') == $account->id ? 'selected' : '' }}>
                                                {{ $account->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label for="type" class="form-label">النوع</label>
                                    <select class="form-select" id="type" name="type">
                                        <option value="">جميع الأنواع</option>
                                        <option value="credit" {{ request('type') == 'credit' ? 'selected' : '' }}>دائن
                                        </option>
                                        <option value="debit" {{ request('type') == 'debit' ? 'selected' : '' }}>مدين
                                        </option>
                                        <option value="transfer" {{ request('type') == 'transfer' ? 'selected' : '' }}>
                                            تحويل</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label for="date_from" class="form-label">من تاريخ</label>
                                    <input type="date" class="form-control" id="date_from" name="date_from"
                                        value="{{ request('date_from') }}">
                                </div>
                                <div class="col-md-2">
                                    <label for="date_to" class="form-label">إلى تاريخ</label>
                                    <input type="date" class="form-control" id="date_to" name="date_to"
                                        value="{{ request('date_to') }}">
                                </div>
                                <div class="col-md-1 d-flex align-items-end">
                                    <button type="submit" class="btn btn-outline-primary">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Transactions Table -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        @if ($transactions->count() > 0)
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>رقم المرجع</th>
                                            <th>الحساب</th>
                                            <th>النوع</th>
                                            <th>المبلغ</th>
                                            <th>الرصيد قبل</th>
                                            <th>الرصيد بعد</th>
                                            <th>التاريخ</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach ($transactions as $transaction)
                                            <tr>
                                                <td>
                                                    <span
                                                        class="badge bg-secondary">{{ $transaction->reference_number }}</span>
                                                </td>
                                                <td>
                                                    <div class="fw-bold">{{ $transaction->account->name }}</div>
                                                    <small class="text-muted">{{ $transaction->account->type }}</small>
                                                </td>
                                                <td>
                                                    @if ($transaction->type === 'credit')
                                                        <span class="badge bg-success">دائن</span>
                                                    @elseif($transaction->type === 'debit')
                                                        <span class="badge bg-danger">مدين</span>
                                                    @else
                                                        <span class="badge bg-info">تحويل</span>
                                                    @endif
                                                </td>
                                                <td>
                                                    <span class="fw-bold">{{ number_format($transaction->amount, 2) }}
                                                        ريال</span>
                                                </td>
                                                <td>{{ number_format($transaction->balance_before, 2) }} ريال</td>
                                                <td>{{ number_format($transaction->balance_after, 2) }} ريال</td>
                                                <td>
                                                    <div>{{ $transaction->created_at->format('Y-m-d') }}</div>
                                                    <small
                                                        class="text-muted">{{ $transaction->created_at->format('H:i') }}</small>
                                                </td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <a href="{{ route('account-transactions.show', $transaction) }}"
                                                            class="btn btn-sm btn-outline-info" title="عرض">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        <a href="{{ route('account-transactions.edit', $transaction) }}"
                                                            class="btn btn-sm btn-outline-primary" title="تعديل">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                    </div>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>

                            <!-- Pagination -->
                            <div class="d-flex justify-content-center mt-4">
                                {{ $transactions->withQueryString()->links() }}
                            </div>
                        @else
                            <div class="text-center py-5">
                                <i class="fas fa-exchange-alt fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">لا توجد معاملات</h5>
                                <p class="text-muted">لم يتم العثور على أي معاملات مطابقة للبحث</p>
                                <a href="{{ route('account-transactions.create') }}" class="btn btn-primary">
                                    <i class="fas fa-plus"></i> إضافة معاملة جديدة
                                </a>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
