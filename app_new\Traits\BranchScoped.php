<?php

namespace App\Traits;

use App\Models\User;
use Illuminate\Database\Eloquent\Builder;

trait BranchScoped
{
    /**
     * Scope query to only include records accessible by the given user
     */
    public function scopeAccessibleBy(Builder $query, User $user): Builder
    {
        if ($user->canAccessAllBranches()) {
            return $query;
        }

        return $query->where('branch_id', $user->branch_id);
    }

    /**
     * Scope query to only include records for a specific branch
     */
    public function scopeForBranch(Builder $query, int $branchId): Builder
    {
        return $query->where('branch_id', $branchId);
    }

    /**
     * Scope query to only include records for specific branches
     */
    public function scopeForBranches(Builder $query, array $branchIds): Builder
    {
        return $query->whereIn('branch_id', $branchIds);
    }

    /**
     * Scope query to only include active records
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true);
    }

    /**
     * Check if the current user can access this record
     */
    public function canBeAccessedBy(User $user): bool
    {
        if ($user->canAccessAllBranches()) {
            return true;
        }

        return $this->branch_id === $user->branch_id;
    }

    /**
     * Get the branch ID for this record
     */
    public function getBranchId(): ?int
    {
        return $this->branch_id;
    }
}
