<x-app-layout>
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-0 text-gray-800">دفع مبلغ للمورد</h1>
                <p class="text-muted mb-0">إكمال دفعة لعملية شراء رقم #{{ $purchase->id }}</p>
            </div>
            <div>
                <a href="{{ user_route('supplier-payments.index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right me-2"></i>العودة للقائمة
                </a>
            </div>
        </div>

        <div class="row">
            <!-- Purchase Details -->
            <div class="col-lg-4 mb-4">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">تفاصيل عملية الشراء</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label text-muted">رقم الفاتورة</label>
                            <div class="fw-bold">{{ $purchase->invoice_number }}</div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label text-muted">المورد</label>
                            <div class="d-flex align-items-center">
                                <div class="avatar-circle bg-primary text-white me-2">
                                    {{ substr($purchase->supplier->name, 0, 1) }}
                                </div>
                                <div>
                                    <div class="fw-bold">{{ $purchase->supplier->name }}</div>
                                    @if ($purchase->supplier->phone)
                                        <small class="text-muted">{{ $purchase->supplier->phone }}</small>
                                    @endif
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label text-muted">تاريخ الشراء</label>
                            <div class="fw-bold">{{ $purchase->purchase_date }}</div>
                        </div>

                        <hr>

                        <div class="mb-3">
                            <label class="form-label text-muted">إجمالي المبلغ</label>
                            <div class="fw-bold text-primary">{{ format_currency($purchase->total_amount) }}</div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label text-muted">المبلغ المدفوع</label>
                            <div class="fw-bold text-success">{{ format_currency($purchase->paid_amount) }}</div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label text-muted">المبلغ المتبقي</label>
                            <div class="fw-bold text-danger fs-5">
                                {{ format_currency($purchase->actual_remaining_amount) }}
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label text-muted">حالة الدفع</label>
                            <div>
                                <span class="badge bg-{{ $purchase->payment_status_color }} fs-6">
                                    {{ $purchase->payment_status }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment Form -->
            <div class="col-lg-8 mb-4">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">تفاصيل الدفعة</h6>
                    </div>
                    <div class="card-body">
                        <form action="{{ user_route('supplier-payments.store', $purchase) }}" method="POST">
                            @csrf

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="amount" class="form-label">مبلغ الدفعة <span
                                            class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <input type="number" name="amount" id="amount"
                                            class="form-control @error('amount') is-invalid @enderror"
                                            value="{{ old('amount') }}" step="0.01" min="0.01"
                                            max="{{ $purchase->actual_remaining_amount }}" placeholder="0.00" required>
                                        <span class="input-group-text">{{ currency_symbol() }}</span>
                                    </div>
                                    @error('amount')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">
                                        الحد الأقصى: {{ format_currency($purchase->actual_remaining_amount) }}
                                    </small>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="payment_method" class="form-label">طريقة الدفع <span
                                            class="text-danger">*</span></label>
                                    <select name="payment_method" id="payment_method"
                                        class="form-select @error('payment_method') is-invalid @enderror" required>
                                        <option value="">اختر طريقة الدفع</option>
                                        <option value="cash" {{ old('payment_method') == 'cash' ? 'selected' : '' }}>
                                            نقدي
                                        </option>
                                        <option value="bank_transfer"
                                            {{ old('payment_method') == 'bank_transfer' ? 'selected' : '' }}>تحويل بنكي
                                        </option>
                                        <option value="check"
                                            {{ old('payment_method') == 'check' ? 'selected' : '' }}>شيك
                                        </option>
                                        <option value="credit_card"
                                            {{ old('payment_method') == 'credit_card' ? 'selected' : '' }}>بطاقة ائتمان
                                        </option>
                                        <option value="other"
                                            {{ old('payment_method') == 'other' ? 'selected' : '' }}>
                                            أخرى</option>
                                    </select>
                                    @error('payment_method')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="reference_number" class="form-label">رقم المرجع</label>
                                    <input type="text" name="reference_number" id="reference_number"
                                        class="form-control @error('reference_number') is-invalid @enderror"
                                        value="{{ old('reference_number') }}"
                                        placeholder="رقم الشيك، رقم التحويل، إلخ">
                                    @error('reference_number')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">
                                        اختياري - رقم الشيك أو التحويل أو أي مرجع آخر
                                    </small>
                                </div>
                            </div>
                    </div>

                    <div class="mb-4">
                        <label for="notes" class="form-label">ملاحظات</label>
                        <textarea name="notes" id="notes" class="form-control @error('notes') is-invalid @enderror" rows="3"
                            placeholder="أي ملاحظات إضافية حول هذه الدفعة">{{ old('notes') }}</textarea>
                        @error('notes')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Quick Amount Buttons -->
                    <div class="mb-4">
                        <label class="form-label">مبالغ سريعة</label>
                        <div class="d-flex flex-wrap gap-2">
                            <button type="button" class="btn btn-outline-primary btn-sm quick-amount"
                                data-amount="{{ $purchase->actual_remaining_amount }}">
                                المبلغ كاملاً ({{ format_currency($purchase->actual_remaining_amount) }})
                            </button>
                            @if ($purchase->actual_remaining_amount >= 1000)
                                <button type="button" class="btn btn-outline-secondary btn-sm quick-amount"
                                    data-amount="1000">
                                    1,000 {{ currency_symbol() }}
                                </button>
                            @endif
                            @if ($purchase->actual_remaining_amount >= 500)
                                <button type="button" class="btn btn-outline-secondary btn-sm quick-amount"
                                    data-amount="500">
                                    500 {{ currency_symbol() }}
                                </button>
                            @endif
                            @if ($purchase->actual_remaining_amount >= 100)
                                <button type="button" class="btn btn-outline-secondary btn-sm quick-amount"
                                    data-amount="100">
                                    100 {{ currency_symbol() }}
                                </button>
                            @endif
                        </div>
                    </div>

                    <div class="d-flex justify-content-end gap-2">
                        <a href="{{ user_route('supplier-payments.index') }}" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>إلغاء
                        </a>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-money-bill-wave me-2"></i>تسجيل الدفعة
                        </button>
                    </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    </div>

    <style>
        .avatar-circle {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 14px;
        }
    </style>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Quick amount buttons
            document.querySelectorAll('.quick-amount').forEach(button => {
                button.addEventListener('click', function() {
                    const amount = this.getAttribute('data-amount');
                    document.getElementById('amount').value = amount;
                });
            });

            // Auto-focus on amount field
            document.getElementById('amount').focus();
        });
    </script>
</x-app-layout>
