<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PurchaseItemDistribution extends Model
{
    use HasFactory;

    protected $fillable = [
        'purchase_item_id',
        'location_type',
        'location_id',
        'quantity',
        'cost_price',
        'sale_price_1',
        'sale_price_2',
        'sale_price_3',
    ];

    protected $casts = [
        'quantity' => 'decimal:2',
        'cost_price' => 'decimal:2',
        'sale_price_1' => 'decimal:2',
        'sale_price_2' => 'decimal:2',
        'sale_price_3' => 'decimal:2',
    ];

    // Relationships
    public function purchaseItem()
    {
        return $this->belongsTo(PurchaseItem::class);
    }

    public function branch()
    {
        return $this->belongsTo(Branch::class, 'location_id')->where('location_type', 'branch');
    }

    public function store()
    {
        return $this->belongsTo(Store::class, 'location_id')->where('location_type', 'store');
    }

    // Accessors
    public function getLocationAttribute()
    {
        if ($this->location_type === 'branch') {
            return $this->branch;
        } elseif ($this->location_type === 'store') {
            return $this->store;
        }
        return null;
    }

    public function getLocationNameAttribute()
    {
        $location = $this->getLocationAttribute();
        return $location ? $location->name : 'غير محدد';
    }
}
