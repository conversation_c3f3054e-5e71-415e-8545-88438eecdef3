<x-app-layout>
    <div class="container-fluid">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h4 class="mb-0">الفروع</h4>
                {{-- <a href="{{ user_route('branches.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> إضافة فرع جديد
                </a> --}}
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead>
                            <tr>
                                <th>الكود</th>
                                <th>الاسم</th>
                                <th>العنوان</th>
                                <th>الهاتف</th>
                                <th>البريد الإلكتروني</th>
                                <th>مخزون الفرع</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($branches as $branch)
                                <tr>
                                    <td>{{ $branch->code }}</td>
                                    <td>{{ $branch->name }}</td>
                                    <td>{{ $branch->address ?? '-' }}</td>
                                    <td>{{ $branch->phone ?? '-' }}</td>
                                    <td>{{ $branch->email ?? '-' }}</td>
                                    <td>
                                        @if (request()->routeIs('admin.*'))
                                            <a href="{{ route('admin.branches.sales-inventory', $branch) }}"
                                                class="btn btn-sm btn-outline-primary d-flex align-items-center justify-content-center"
                                                title="عرض مخزون الفرع">
                                                <i class="fas fa-boxes me-1"></i>
                                                <span
                                                    class="badge bg-primary">{{ $branch->branchInventories->count() ?? 0 }}</span>
                                            </a>
                                        @else
                                            <span
                                                class="badge bg-secondary">{{ $branch->branchInventories->count() ?? 0 }}</span>
                                        @endif
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ $branch->is_active ? 'success' : 'danger' }}">
                                            {{ $branch->is_active ? 'نشط' : 'غير نشط' }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="{{ user_route('branches.show', $branch) }}"
                                                class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ user_route('branches.edit', $branch) }}"
                                                class="btn btn-sm btn-primary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            {{-- <form action="{{ user_route('branches.destroy', $branch) }}" method="POST"
                                                class="d-inline">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-sm btn-danger"
                                                    onclick="return confirm('هل أنت متأكد من حذف هذا الفرع؟')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form> --}}
                                        </div>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="8" class="text-center">لا توجد فروع</td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
                {{-- <div class="mt-4">
                    {{ $branches->links() }}
                </div> --}}
            </div>
        </div>
    </div>
</x-app-layout>
