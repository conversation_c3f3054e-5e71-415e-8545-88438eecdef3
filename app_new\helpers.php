<?php

if (!function_exists('user_route')) {
    /**
     * Generate a route URL based on the current user's role
     *
     * @param string $routeName
     * @param mixed $parameters
     * @return string
     */
    function user_route(string $routeName, $parameters = [])
    {
        $user = auth()->user();

        if (!$user) {
            return route($routeName, $parameters);
        }

        if ($user->isAdmin()) {
            return route('admin.' . $routeName, $parameters);
        } elseif ($user->isSeller()) {
            return route('seller.' . $routeName, $parameters);
        }

        return route($routeName, $parameters);
    }
}

if (!function_exists('currency_symbol')) {
    /**
     * Get the currency symbol based on currency code
     *
     * @param string|null $currencyCode
     * @return string
     */
    function currency_symbol(?string $currencyCode = null): string
    {
        if (!$currencyCode) {
            $settings = \App\Models\Setting::first();
            $currencyCode = $settings->company_currency ?? 'EGP';
        }

        return match ($currencyCode) {
            'EGP' => 'ج.م',
            'SAR' => 'ر.س',
            'USD' => '$',
            'EUR' => '€',
            default => 'ج.م'
        };
    }
}

if (!function_exists('format_currency')) {
    /**
     * Format amount with currency symbol
     *
     * @param float|null $amount
     * @param string|null $currencyCode
     * @param int $decimals
     * @return string
     */
    function format_currency(?float $amount, ?string $currencyCode = null, int $decimals = 2): string
    {
        // Handle null values by defaulting to 0
        $amount = $amount ?? 0.0;

        $symbol = currency_symbol($currencyCode);
        return number_format($amount, $decimals) . ' ' . $symbol;
    }
}
