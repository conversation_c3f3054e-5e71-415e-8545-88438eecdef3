<?php

namespace Tests\Feature;

use App\Models\Branch;
use App\Models\BranchInventory;
use App\Models\Customer;
use App\Models\Product;
use App\Models\Sale;
use App\Models\SaleItem;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class AutoCompleteReturnTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $branch;
    protected $customer;
    protected $product;
    protected $sale;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test data
        $this->user = User::factory()->create();
        $this->branch = Branch::factory()->create();
        $this->customer = Customer::factory()->create();
        $this->product = Product::factory()->create();

        // Create branch inventory
        BranchInventory::create([
            'branch_id' => $this->branch->id,
            'product_id' => $this->product->id,
            'quantity' => 100,
            'min_quantity' => 10,
        ]);

        // Create a sale
        $this->sale = Sale::create([
            'invoice_number' => 'INV-001',
            'customer_id' => $this->customer->id,
            'branch_id' => $this->branch->id,
            'user_id' => $this->user->id,
            'total_amount' => 500.00,
            'discount_amount' => 0,
            'status' => 'completed',
        ]);

        // Create sale item
        SaleItem::create([
            'sale_id' => $this->sale->id,
            'product_id' => $this->product->id,
            'quantity' => 10,
            'price' => 50.00,
            'subtotal' => 500.00,
        ]);

        // Reduce inventory for the sale
        $inventory = BranchInventory::where('branch_id', $this->branch->id)
            ->where('product_id', $this->product->id)
            ->first();
        $inventory->decrement('quantity', 10);
    }

    /** @test */
    public function it_automatically_completes_return_on_creation()
    {
        $initialInventory = BranchInventory::where('branch_id', $this->branch->id)
            ->where('product_id', $this->product->id)
            ->first();
        $initialQuantity = $initialInventory->quantity;

        $returnData = [
            'sale_id' => $this->sale->id,
            'return_date' => now()->format('Y-m-d'),
            'return_type' => 'partial',
            'reason' => 'customer_request',
            'notes' => 'Customer requested return',
            'refund_amount' => 250.00,
            'items' => [
                [
                    'sale_item_id' => $this->sale->items->first()->id,
                    'quantity_returned' => 5,
                    'condition' => 'good',
                    'item_notes' => 'Good condition',
                ]
            ]
        ];

        $response = $this->actingAs($this->user)
            ->post(route('sale-returns.store'), $returnData);

        $response->assertRedirect();
        
        // Check that return was created with completed status
        $this->assertDatabaseHas('sale_returns', [
            'sale_id' => $this->sale->id,
            'total_amount' => 250.00,
            'refund_amount' => 250.00,
            'status' => 'completed', // Should be completed automatically
        ]);

        // Check that inventory was updated immediately
        $updatedInventory = BranchInventory::where('branch_id', $this->branch->id)
            ->where('product_id', $this->product->id)
            ->first();

        $this->assertEquals($initialQuantity + 5, $updatedInventory->quantity);

        // Check that return item is marked as inventory adjusted
        $this->assertDatabaseHas('sale_return_items', [
            'product_id' => $this->product->id,
            'quantity_returned' => 5,
            'inventory_adjusted' => true,
        ]);
    }

    /** @test */
    public function it_shows_success_message_indicating_auto_completion()
    {
        $returnData = [
            'sale_id' => $this->sale->id,
            'return_date' => now()->format('Y-m-d'),
            'return_type' => 'partial',
            'reason' => 'customer_request',
            'notes' => 'Customer requested return',
            'refund_amount' => 250.00,
            'items' => [
                [
                    'sale_item_id' => $this->sale->items->first()->id,
                    'quantity_returned' => 5,
                    'condition' => 'good',
                    'item_notes' => 'Good condition',
                ]
            ]
        ];

        $response = $this->actingAs($this->user)
            ->post(route('sale-returns.store'), $returnData);

        $response->assertSessionHas('success');
        $successMessage = session('success');
        
        $this->assertStringContainsString('تم إنشاء وإكمال المرتجع بنجاح', $successMessage);
        $this->assertStringContainsString('تم تحديث المخزون والحسابات تلقائياً', $successMessage);
    }

    /** @test */
    public function it_validates_quantities_before_auto_completion()
    {
        $returnData = [
            'sale_id' => $this->sale->id,
            'return_date' => now()->format('Y-m-d'),
            'return_type' => 'partial',
            'reason' => 'customer_request',
            'notes' => 'Customer requested return',
            'refund_amount' => 600.00,
            'items' => [
                [
                    'sale_item_id' => $this->sale->items->first()->id,
                    'quantity_returned' => 15, // More than original 10
                    'condition' => 'good',
                    'item_notes' => 'Good condition',
                ]
            ]
        ];

        $response = $this->actingAs($this->user)
            ->post(route('sale-returns.store'), $returnData);

        $response->assertSessionHas('error');
        
        // Should not create the return
        $this->assertDatabaseMissing('sale_returns', [
            'sale_id' => $this->sale->id,
            'status' => 'completed',
        ]);
    }
}
