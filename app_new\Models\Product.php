<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Product extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'description',
        'price',
        'selling_price',
        'sku',
        'minimum_stock',
        'category_id',
        'is_active',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'selling_price' => 'decimal:2',
        'cost_price' => 'decimal:2',
        'minimum_stock' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    public function sales()
    {
        return $this->hasManyThrough(Sale::class, SaleItem::class, 'product_id', 'id', 'id', 'sale_id');
    }

    public function saleItems()
    {
        return $this->hasMany(SaleItem::class);
    }

    public function purchaseItems()
    {
        return $this->hasMany(PurchaseItem::class);
    }

    public function purchases()
    {
        return $this->hasManyThrough(Purchase::class, PurchaseItem::class, 'product_id', 'id', 'id', 'purchase_id');
    }

    public function branches()
    {
        return $this->belongsToMany(Branch::class, 'branch_product')
            ->withPivot(['quantity', 'minimum_stock'])
            ->withTimestamps();
    }

    public function branchInventories()
    {
        return $this->hasMany(BranchInventory::class);
    }

    public function storeInventories()
    {
        return $this->hasMany(StoreInventory::class);
    }

    public function getSellingPriceAttribute()
    {
        // If selling_price is already set (e.g., by ProductAvailabilityService), return it
        if (isset($this->attributes['selling_price'])) {
            return $this->attributes['selling_price'];
        }

        // Otherwise, fallback to the price column
        return $this->price ?? 0;
    }

    public function getProfitAttribute()
    {
        return $this->selling_price - $this->cost_price;
    }

    public function getProfitMarginAttribute()
    {
        if ($this->cost_price > 0) {
            return (($this->selling_price - $this->cost_price) / $this->cost_price) * 100;
        }
        return 0;
    }

    public function getImageUrlAttribute()
    {
        return $this->image_path ? asset('storage/' . $this->image_path) : asset('images/no-image.png');
    }
}
