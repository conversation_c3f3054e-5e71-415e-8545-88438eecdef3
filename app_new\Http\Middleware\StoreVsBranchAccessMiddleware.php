<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class StoreVsBranchAccessMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, string $operationType): Response
    {
        $user = $request->user();

        if (!$user) {
            return redirect()->route('login');
        }

        // Admin users can access everything
        if ($user->isAdmin()) {
            return $next($request);
        }

        // Check access based on operation type
        switch ($operationType) {
            case 'store':
            case 'warehouse':
                if (!$user->canAccessStoreOperations()) {
                    abort(403, 'You do not have access to warehouse/store operations.');
                }
                break;

            case 'branch':
            case 'sales':
                if (!$user->canAccessBranchSalesOperations()) {
                    abort(403, 'You do not have access to branch/sales operations.');
                }
                break;

            case 'store-inventory':
                if (!$user->canManageStoreInventory()) {
                    abort(403, 'You do not have permission to manage store inventory.');
                }
                break;

            case 'branch-sales-view':
                if (!$user->canAccessBranchSalesOperations()) {
                    abort(403, 'You do not have permission to view branch sales inventory.');
                }
                break;

            case 'branch-sales-add-products':
                if (!$user->canAddProductsToBranchInventory()) {
                    abort(403, 'You do not have permission to add products to branch inventory.');
                }
                break;

            case 'branch-sales-inventory':
                if (!$user->canManageBranchSalesInventory()) {
                    abort(403, 'You do not have permission to manage branch sales inventory.');
                }
                break;

            case 'transfer-approve':
                if (!$user->canApproveTransfers()) {
                    abort(403, 'You do not have permission to approve inventory transfers.');
                }
                break;

            case 'transfer-request':
                if (!$user->canRequestTransfers()) {
                    abort(403, 'You do not have permission to request inventory transfers.');
                }
                break;

            default:
                abort(403, 'Invalid operation type specified.');
        }

        return $next($request);
    }
}
