<x-app-layout>
    <x-slot name="header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2 class="font-semibold text-xl text-gray-800 leading-tight mb-1">
                    {{ __('إدارة المستخدمين') }}
                </h2>
                <p class="text-sm text-gray-600">{{ __('إدارة حسابات المستخدمين والصلاحيات') }}</p>
            </div>
            <div class="d-flex gap-2">
                <button type="button" class="btn btn-outline-primary" data-bs-toggle="modal"
                    data-bs-target="#bulkActionsModal">
                    <i class="fas fa-tasks"></i> {{ __('إجراءات متعددة') }}
                </button>
                <a href="{{ route('admin.settings.users.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> {{ __('إضافة مستخدم جديد') }}
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-6">
        <div class="container-fluid">
            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <div class="bg-primary bg-opacity-10 rounded-circle p-3">
                                        <i class="fas fa-users text-primary fa-lg"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <div class="text-muted small">{{ __('إجمالي المستخدمين') }}</div>
                                    <div class="h4 mb-0">{{ $users->total() }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <div class="bg-success bg-opacity-10 rounded-circle p-3">
                                        <i class="fas fa-user-check text-success fa-lg"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <div class="text-muted small">{{ __('المستخدمين النشطين') }}</div>
                                    <div class="h4 mb-0">{{ $users->where('is_active', true)->count() }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <div class="bg-warning bg-opacity-10 rounded-circle p-3">
                                        <i class="fas fa-user-times text-warning fa-lg"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <div class="text-muted small">{{ __('المستخدمين غير النشطين') }}</div>
                                    <div class="h4 mb-0">{{ $users->where('is_active', false)->count() }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <div class="bg-info bg-opacity-10 rounded-circle p-3">
                                        <i class="fas fa-user-shield text-info fa-lg"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <div class="text-muted small">{{ __('المديرين') }}</div>
                                    <div class="h4 mb-0">
                                        {{ $users->filter(function ($user) {return $user->role && $user->role->name === 'admin';})->count() }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content Card -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <div class="row align-items-center">
                        <div class="col">
                            <h5 class="mb-0">{{ __('قائمة المستخدمين') }}</h5>
                        </div>
                        <div class="col-auto">
                            <button type="button" class="btn btn-sm btn-outline-secondary" data-bs-toggle="collapse"
                                data-bs-target="#filtersCollapse">
                                <i class="fas fa-filter"></i> {{ __('تصفية النتائج') }}
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Advanced Filters -->
                <div class="collapse" id="filtersCollapse">
                    <div class="card-body border-bottom bg-light">
                        <form action="{{ route('admin.settings.users') }}" method="GET" id="filtersForm">
                            <div class="row g-3">
                                <div class="col-md-3">
                                    <label for="search" class="form-label">{{ __('البحث العام') }}</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-search"></i></span>
                                        <input type="text" name="search" id="search" class="form-control"
                                            value="{{ request('search') }}"
                                            placeholder="{{ __('الاسم، البريد، الهاتف...') }}">
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <label for="role" class="form-label">{{ __('الدور') }}</label>
                                    <select name="role" id="role" class="form-select">
                                        <option value="">{{ __('جميع الأدوار') }}</option>
                                        @foreach ($roles as $role)
                                            <option value="{{ $role->id }}"
                                                {{ request('role') == $role->id ? 'selected' : '' }}>
                                                {{ $role->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label for="branch" class="form-label">{{ __('الفرع') }}</label>
                                    <select name="branch" id="branch" class="form-select">
                                        <option value="">{{ __('جميع الفروع') }}</option>
                                        @foreach ($branches as $branch)
                                            <option value="{{ $branch->id }}"
                                                {{ request('branch') == $branch->id ? 'selected' : '' }}>
                                                {{ $branch->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label for="status" class="form-label">{{ __('الحالة') }}</label>
                                    <select name="status" id="status" class="form-select">
                                        <option value="">{{ __('جميع الحالات') }}</option>
                                        <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>
                                            {{ __('نشط') }}
                                        </option>
                                        <option value="inactive"
                                            {{ request('status') == 'inactive' ? 'selected' : '' }}>
                                            {{ __('غير نشط') }}
                                        </option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">{{ __('الإجراءات') }}</label>
                                    <div class="d-flex gap-2">
                                        <button type="submit" class="btn btn-primary flex-fill">
                                            <i class="fas fa-search"></i> {{ __('تطبيق') }}
                                        </button>
                                        <a href="{{ route('admin.settings.users') }}"
                                            class="btn btn-outline-secondary">
                                            <i class="fas fa-undo"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Users Table -->
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th width="40">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="selectAll">
                                        </div>
                                    </th>
                                    <th>{{ __('المستخدم') }}</th>
                                    <th>{{ __('معلومات الاتصال') }}</th>
                                    <th>{{ __('الدور والفرع') }}</th>
                                    <th>{{ __('الحالة') }}</th>
                                    <th>{{ __('آخر نشاط') }}</th>
                                    <th width="120">{{ __('الإجراءات') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($users as $user)
                                    <tr>
                                        <td>
                                            <div class="form-check">
                                                <input class="form-check-input user-checkbox" type="checkbox"
                                                    value="{{ $user->id }}">
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div
                                                    class="avatar-sm bg-primary bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center me-3">
                                                    <i class="fas fa-user text-primary"></i>
                                                </div>
                                                <div>
                                                    <div class="fw-semibold">{{ $user->name }}</div>
                                                    <div class="text-muted small">{{ __('معرف') }}:
                                                        {{ $user->id }}</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div>
                                                <div class="d-flex align-items-center mb-1">
                                                    <i class="fas fa-envelope text-muted me-2"></i>
                                                    <span class="small">{{ $user->email }}</span>
                                                </div>
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-phone text-muted me-2"></i>
                                                    <span class="small">{{ $user->phone }}</span>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div>
                                                @if ($user->role)
                                                    <span
                                                        class="badge bg-info bg-opacity-10 text-info border border-info mb-1">
                                                        <i class="fas fa-user-tag me-1"></i>{{ $user->role->name }}
                                                    </span>
                                                @endif
                                                <div class="text-muted small">
                                                    <i class="fas fa-building me-1"></i>
                                                    {{ $user->branch ? $user->branch->name : __('غير محدد') }}
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            @if ($user->is_active)
                                                <span
                                                    class="badge bg-success bg-opacity-10 text-success border border-success">
                                                    <i class="fas fa-check-circle me-1"></i>{{ __('نشط') }}
                                                </span>
                                            @else
                                                <span
                                                    class="badge bg-danger bg-opacity-10 text-danger border border-danger">
                                                    <i class="fas fa-times-circle me-1"></i>{{ __('غير نشط') }}
                                                </span>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="text-muted small">
                                                @if ($user->last_login_at)
                                                    <div>{{ $user->last_login_at->format('Y-m-d') }}</div>
                                                    <div>{{ $user->last_login_at->format('H:i') }}</div>
                                                @else
                                                    <span class="text-muted">{{ __('لم يسجل دخول') }}</span>
                                                @endif
                                            </div>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('admin.settings.users.show', $user) }}"
                                                    class="btn btn-sm btn-outline-info" title="{{ __('عرض') }}">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{{ route('admin.settings.users.edit', $user) }}"
                                                    class="btn btn-sm btn-outline-primary"
                                                    title="{{ __('تعديل') }}">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                @if ($user->id !== auth()->id())
                                                    <button type="button" class="btn btn-sm btn-outline-danger"
                                                        onclick="confirmDelete('{{ route('admin.settings.users.destroy', $user) }}')"
                                                        title="{{ __('حذف') }}">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="7" class="text-center py-5">
                                            <div class="text-muted">
                                                <i class="fas fa-users fa-3x mb-3"></i>
                                                <div class="h5">{{ __('لا توجد مستخدمين') }}</div>
                                                <p>{{ __('لم يتم العثور على أي مستخدمين مطابقين للبحث') }}</p>
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Pagination -->
                @if ($users->hasPages())
                    <div class="card-footer bg-white border-top">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="text-muted small">
                                {{ __('عرض') }} {{ $users->firstItem() }} {{ __('إلى') }}
                                {{ $users->lastItem() }}
                                {{ __('من أصل') }} {{ $users->total() }} {{ __('نتيجة') }}
                            </div>
                            <div>
                                {{ $users->links() }}
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Bulk Actions Modal -->
    <div class="modal fade" id="bulkActionsModal" tabindex="-1" aria-labelledby="bulkActionsModalLabel"
        aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="bulkActionsModalLabel">{{ __('الإجراءات المتعددة') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        {{ __('لم يتم تحديد أي مستخدمين') }}
                    </div>
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-success" onclick="bulkAction('activate')">
                            <i class="fas fa-check-circle me-2"></i>{{ __('تفعيل المستخدمين المحددين') }}
                        </button>
                        <button type="button" class="btn btn-warning" onclick="bulkAction('deactivate')">
                            <i class="fas fa-times-circle me-2"></i>{{ __('إلغاء تفعيل المستخدمين المحددين') }}
                        </button>
                        <button type="button" class="btn btn-danger" onclick="bulkAction('delete')">
                            <i class="fas fa-trash me-2"></i>{{ __('حذف المستخدمين المحددين') }}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteModalLabel">{{ __('تأكيد الحذف') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="text-center">
                        <i class="fas fa-exclamation-triangle text-warning fa-3x mb-3"></i>
                        <h5>{{ __('هل أنت متأكد من حذف هذا المستخدم؟') }}</h5>
                        <p class="text-muted">{{ __('لا يمكن التراجع عن هذا الإجراء') }}</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary"
                        data-bs-dismiss="modal">{{ __('إلغاء') }}</button>
                    <form id="deleteForm" method="POST" style="display: inline;">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-danger">{{ __('حذف') }}</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
        <script>
            // Select All functionality
            document.getElementById('selectAll').addEventListener('change', function() {
                const checkboxes = document.querySelectorAll('.user-checkbox');
                checkboxes.forEach(checkbox => {
                    checkbox.checked = this.checked;
                });
                updateBulkActionsButton();
            });

            // Individual checkbox change
            document.querySelectorAll('.user-checkbox').forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    updateBulkActionsButton();
                    updateSelectAllState();
                });
            });

            function updateSelectAllState() {
                const checkboxes = document.querySelectorAll('.user-checkbox');
                const checkedBoxes = document.querySelectorAll('.user-checkbox:checked');
                const selectAll = document.getElementById('selectAll');

                if (checkedBoxes.length === 0) {
                    selectAll.indeterminate = false;
                    selectAll.checked = false;
                } else if (checkedBoxes.length === checkboxes.length) {
                    selectAll.indeterminate = false;
                    selectAll.checked = true;
                } else {
                    selectAll.indeterminate = true;
                }
            }

            function updateBulkActionsButton() {
                const checkedBoxes = document.querySelectorAll('.user-checkbox:checked');
                const bulkButton = document.querySelector('[data-bs-target="#bulkActionsModal"]');
                const alertInfo = document.querySelector('#bulkActionsModal .alert-info');

                if (checkedBoxes.length > 0) {
                    bulkButton.disabled = false;
                    alertInfo.innerHTML =
                        `<i class="fas fa-info-circle me-2"></i>{{ __('تم تحديد') }} ${checkedBoxes.length} {{ __('مستخدم') }}`;
                    alertInfo.className = 'alert alert-success';
                } else {
                    bulkButton.disabled = false;
                    alertInfo.innerHTML = '<i class="fas fa-info-circle me-2"></i>{{ __('لم يتم تحديد أي مستخدمين') }}';
                    alertInfo.className = 'alert alert-info';
                }
            }

            function confirmDelete(url) {
                document.getElementById('deleteForm').action = url;
                new bootstrap.Modal(document.getElementById('deleteModal')).show();
            }

            function bulkAction(action) {
                const checkedBoxes = document.querySelectorAll('.user-checkbox:checked');
                if (checkedBoxes.length === 0) {
                    alert('{{ __('يرجى تحديد مستخدم واحد على الأقل') }}');
                    return;
                }

                const userIds = Array.from(checkedBoxes).map(cb => cb.value);

                if (confirm(`{{ __('هل أنت متأكد من تنفيذ هذا الإجراء على') }} ${userIds.length} {{ __('مستخدم؟') }}`)) {
                    // Create form and submit
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = '{{ route('admin.settings.users') }}/bulk-action';

                    // CSRF token
                    const csrfInput = document.createElement('input');
                    csrfInput.type = 'hidden';
                    csrfInput.name = '_token';
                    csrfInput.value = '{{ csrf_token() }}';
                    form.appendChild(csrfInput);

                    // Action input
                    const actionInput = document.createElement('input');
                    actionInput.type = 'hidden';
                    actionInput.name = 'action';
                    actionInput.value = action;
                    form.appendChild(actionInput);

                    // User IDs
                    userIds.forEach(id => {
                        const idInput = document.createElement('input');
                        idInput.type = 'hidden';
                        idInput.name = 'user_ids[]';
                        idInput.value = id;
                        form.appendChild(idInput);
                    });

                    document.body.appendChild(form);
                    form.submit();
                }
            }

            // Auto-submit filters on change
            document.querySelectorAll('#filtersForm select').forEach(select => {
                select.addEventListener('change', function() {
                    document.getElementById('filtersForm').submit();
                });
            });

            // Initialize on page load
            document.addEventListener('DOMContentLoaded', function() {
                updateBulkActionsButton();
                updateSelectAllState();
            });
        </script>
    @endpush
</x-app-layout>
