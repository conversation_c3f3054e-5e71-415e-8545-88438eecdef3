<x-app-layout>
    <x-slot name="header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                    <i class="fas fa-exchange-alt text-primary"></i> {{ __('نقل مباشر للمنتجات') }}
                </h2>
                <p class="text-muted small mb-0">نقل فوري للمنتجات بين الفروع والمخازن</p>
            </div>
            <div>
                <a href="{{ user_route('transfers.history') }}" class="btn btn-secondary">
                    <i class="fas fa-history"></i> سجل النقل
                </a>
            </div>
        </div>
    </x-slot>

    <div class="container-fluid">
        <!-- Display validation errors -->
        @if ($errors->any())
            <div class="alert alert-danger">
                <h6><i class="fas fa-exclamation-triangle"></i> يرجى تصحيح الأخطاء التالية:</h6>
                <ul class="mb-0">
                    @foreach ($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        <form method="POST" action="{{ user_route('transfers.direct.store') }}" id="transferForm">
            @csrf

            <!-- Transfer Details -->
            <div class="row mb-4">
                <div class="col-lg-6">
                    <div class="card shadow-sm">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-arrow-right"></i> من (المصدر)
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="source_type" class="form-label fw-bold">نوع المصدر</label>
                                    <select class="form-select @error('source_type') is-invalid @enderror"
                                        id="source_type" name="source_type" required>
                                        <option value="">اختر نوع المصدر</option>
                                        <option value="branch"
                                            {{ old('source_type', $preselected['source_type']) == 'branch' ? 'selected' : '' }}>
                                            فرع</option>
                                        <option value="store"
                                            {{ old('source_type', $preselected['source_type']) == 'store' ? 'selected' : '' }}>
                                            مخزن</option>
                                    </select>
                                    @error('source_type')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="source_id" class="form-label fw-bold">المصدر</label>
                                    <select class="form-select @error('source_id') is-invalid @enderror" id="source_id"
                                        name="source_id" required>
                                        <option value="">اختر المصدر</option>
                                    </select>
                                    @error('source_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-6">
                    <div class="card shadow-sm">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-arrow-left"></i> إلى (الوجهة)
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="destination_type" class="form-label fw-bold">نوع الوجهة</label>
                                    <select class="form-select @error('destination_type') is-invalid @enderror"
                                        id="destination_type" name="destination_type" required>
                                        <option value="">اختر نوع الوجهة</option>
                                        <option value="branch"
                                            {{ old('destination_type', $preselected['destination_type']) == 'branch' ? 'selected' : '' }}>
                                            فرع</option>
                                        <option value="store"
                                            {{ old('destination_type', $preselected['destination_type']) == 'store' ? 'selected' : '' }}>
                                            مخزن</option>
                                    </select>
                                    @error('destination_type')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="destination_id" class="form-label fw-bold">الوجهة</label>
                                    <select class="form-select @error('destination_id') is-invalid @enderror"
                                        id="destination_id" name="destination_id" required>
                                        <option value="">اختر الوجهة</option>
                                    </select>
                                    @error('destination_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Products Section -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-boxes"></i> المنتجات المراد نقلها
                    </h5>
                    <button type="button" class="btn btn-light btn-sm" onclick="addProductRow()">
                        <i class="fas fa-plus"></i> إضافة منتج
                    </button>
                </div>
                <div class="card-body">
                    <div id="products-container">
                        <!-- Product rows will be added here -->
                    </div>

                    <div class="text-center mt-3" id="no-products-message">
                        <p class="text-muted">لم يتم إضافة أي منتجات بعد</p>
                        <button type="button" class="btn btn-primary" onclick="addProductRow()">
                            <i class="fas fa-plus"></i> إضافة منتج
                        </button>
                    </div>
                </div>
            </div>

            <!-- Notes -->
            <div class="card shadow-sm mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-sticky-note"></i> ملاحظات
                    </h5>
                </div>
                <div class="card-body">
                    <textarea class="form-control @error('notes') is-invalid @enderror" id="notes" name="notes" rows="3"
                        placeholder="ملاحظات إضافية (اختيارية)">{{ old('notes') }}</textarea>
                    @error('notes')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="card shadow-sm">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <button type="submit" class="btn btn-success btn-lg" id="submitBtn" disabled>
                                <i class="fas fa-check"></i> تنفيذ النقل
                            </button>
                            <button type="button" class="btn btn-secondary btn-lg" onclick="resetForm()">
                                <i class="fas fa-undo"></i> إعادة تعيين
                            </button>
                        </div>
                        <div>
                            <a href="{{ user_route('dashboard') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times"></i> إلغاء
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>

    @push('styles')
        <style>
            .product-dropdown {
                border: 1px solid #dee2e6;
                border-radius: 0.375rem;
                box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            }

            .product-option:hover {
                background-color: #f8f9fa;
            }

            .product-option:last-child {
                border-bottom: none !important;
            }

            .cursor-pointer {
                cursor: pointer;
            }
        </style>
    @endpush

    @push('scripts')
        <script>
            let productRowIndex = 0;
            const branches = @json($branches);
            const stores = @json($stores);
            const products = @json($products);
            const preselected = @json($preselected);

            // Cache for quantity data to avoid repeated requests
            let quantityCache = {};
            let currentCacheKey = null;

            document.addEventListener('DOMContentLoaded', function() {
                console.log('Direct transfer form loaded');
                console.log('Branches:', branches);
                console.log('Stores:', stores);
                console.log('Products:', products);
                console.log('Preselected:', preselected);

                // Initialize source and destination dropdowns
                updateSourceOptions();
                updateDestinationOptions();

                // Pre-fill if parameters provided
                if (preselected.source_type) {
                    document.getElementById('source_type').value = preselected.source_type;
                    updateSourceOptions();
                    if (preselected.source_id) {
                        document.getElementById('source_id').value = preselected.source_id;
                    }
                }

                if (preselected.destination_type) {
                    document.getElementById('destination_type').value = preselected.destination_type;
                    updateDestinationOptions();
                    if (preselected.destination_id) {
                        document.getElementById('destination_id').value = preselected.destination_id;
                    }
                }

                // Add first product row if product is preselected
                if (preselected.product_id) {
                    addProductRow();
                    const firstProductSelect = document.querySelector('select[name="items[0][product_id]"]');
                    if (firstProductSelect) {
                        firstProductSelect.value = preselected.product_id;
                        updateAvailableQuantity(firstProductSelect);
                    }
                }

                // Update submit button state initially
                updateSubmitButton();
            });

            // Update source options based on type
            function updateSourceOptions() {
                const sourceType = document.getElementById('source_type').value;
                const sourceSelect = document.getElementById('source_id');

                sourceSelect.innerHTML = '<option value="">اختر المصدر</option>';

                if (sourceType === 'branch') {
                    branches.forEach(branch => {
                        sourceSelect.innerHTML +=
                            `<option value="${branch.id}">${branch.name} (${branch.code})</option>`;
                    });
                } else if (sourceType === 'store') {
                    stores.forEach(store => {
                        sourceSelect.innerHTML += `<option value="${store.id}">${store.name} (${store.code})</option>`;
                    });
                }
            }

            // Update destination options based on type
            function updateDestinationOptions() {
                const destinationType = document.getElementById('destination_type').value;
                const destinationSelect = document.getElementById('destination_id');

                destinationSelect.innerHTML = '<option value="">اختر الوجهة</option>';

                if (destinationType === 'branch') {
                    branches.forEach(branch => {
                        destinationSelect.innerHTML +=
                            `<option value="${branch.id}">${branch.name} (${branch.code})</option>`;
                    });
                } else if (destinationType === 'store') {
                    stores.forEach(store => {
                        destinationSelect.innerHTML +=
                            `<option value="${store.id}">${store.name} (${store.code})</option>`;
                    });
                }
            }

            // Add product row
            function addProductRow() {
                const container = document.getElementById('products-container');
                const noProductsMessage = document.getElementById('no-products-message');

                const row = document.createElement('div');
                row.className = 'row mb-3 product-row';
                row.innerHTML = `
                <div class="col-md-4">
                    <label class="form-label">المنتج</label>
                    <div class="position-relative">
                        <input type="text" class="form-control product-search"
                               placeholder="ابحث عن المنتج..."
                               onkeyup="searchProducts(this)"
                               onfocus="showProductDropdown(this)"
                               onblur="hideProductDropdown(this)">
                        <input type="hidden" class="product-id-input" name="items[${productRowIndex}][product_id]" required>
                        <div class="product-dropdown position-absolute w-100 bg-white border rounded shadow-sm"
                             style="display: none; max-height: 200px; overflow-y: auto; z-index: 1000; top: 100%;">
                            ${generateProductOptions()}
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <label class="form-label">الكمية المتاحة</label>
                    <input type="text" class="form-control available-quantity" readonly placeholder="0">
                </div>
                <div class="col-md-2">
                    <label class="form-label">الكمية المطلوبة</label>
                    <input type="number" class="form-control" name="items[${productRowIndex}][quantity]"
                           min="0.01" step="0.01" required>
                </div>
                <div class="col-md-3">
                    <label class="form-label">ملاحظات</label>
                    <input type="text" class="form-control" name="items[${productRowIndex}][notes]" placeholder="ملاحظات (اختيارية)">
                </div>
                <div class="col-md-1">
                    <label class="form-label">&nbsp;</label>
                    <button type="button" class="btn btn-danger d-block" onclick="removeProductRow(this)">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            `;

                container.appendChild(row);
                productRowIndex++;

                noProductsMessage.style.display = 'none';
                updateSubmitButton();
            }

            // Remove product row
            function removeProductRow(button) {
                button.closest('.product-row').remove();

                const container = document.getElementById('products-container');
                const noProductsMessage = document.getElementById('no-products-message');

                if (container.children.length === 0) {
                    noProductsMessage.style.display = 'block';
                }

                updateSubmitButton();
            }

            // Update available quantity when product is selected
            async function updateAvailableQuantity(select) {
                const row = select.closest('.product-row');
                const availableInput = row.querySelector('.available-quantity');
                const sourceType = document.getElementById('source_type').value;
                const sourceId = document.getElementById('source_id').value;
                const productId = select.value;

                if (!sourceType || !sourceId || !productId) {
                    availableInput.value = '0';
                    return;
                }

                try {
                    console.log('Fetching available quantity for:', {
                        location_type: sourceType,
                        location_id: sourceId,
                        product_id: productId
                    });

                    const response = await fetch('{{ user_route('transfers.available-quantity') }}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': '{{ csrf_token() }}'
                        },
                        body: JSON.stringify({
                            location_type: sourceType,
                            location_id: sourceId,
                            product_id: productId
                        })
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    const data = await response.json();
                    console.log('Available quantity response:', data);
                    availableInput.value = parseFloat(data.available_quantity).toFixed(2);
                } catch (error) {
                    console.error('Error fetching available quantity:', error);
                    availableInput.value = '0';
                }
            }

            // Update submit button state
            function updateSubmitButton() {
                const submitBtn = document.getElementById('submitBtn');
                const productRows = document.querySelectorAll('.product-row');
                const sourceType = document.getElementById('source_type').value;
                const sourceId = document.getElementById('source_id').value;
                const destinationType = document.getElementById('destination_type').value;
                const destinationId = document.getElementById('destination_id').value;

                const hasProducts = productRows.length > 0;
                const hasSourceAndDestination = sourceType && sourceId && destinationType && destinationId;

                submitBtn.disabled = !(hasProducts && hasSourceAndDestination);
            }

            // Reset form
            function resetForm() {
                document.getElementById('transferForm').reset();
                document.getElementById('products-container').innerHTML = '';
                document.getElementById('no-products-message').style.display = 'block';
                updateSubmitButton();
            }

            // Generate product options with quantities
            function generateProductOptions() {
                return products.map(product => {
                    return `
                        <div class="product-option p-2 cursor-pointer border-bottom"
                             data-product-id="${product.id}"
                             data-product-name="${product.name}"
                             onclick="selectProduct(this)"
                             style="cursor: pointer;">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>${product.name}</strong>
                                    ${product.code ? `<small class="text-muted d-block">كود: ${product.code}</small>` : ''}
                                </div>
                                <div class="text-end">
                                    <span class="badge bg-secondary quantity-badge">جاري التحميل...</span>
                                </div>
                            </div>
                        </div>
                    `;
                }).join('');
            }

            // Search products function
            function searchProducts(input) {
                const searchTerm = input.value.toLowerCase();
                const dropdown = input.nextElementSibling.nextElementSibling; // Skip hidden input
                const options = dropdown.querySelectorAll('.product-option');
                let visibleCount = 0;

                options.forEach(option => {
                    const productName = option.dataset.productName.toLowerCase();
                    const productCode = option.querySelector('small')?.textContent.toLowerCase() || '';

                    if (productName.includes(searchTerm) || productCode.includes(searchTerm)) {
                        option.style.display = 'block';
                        visibleCount++;
                    } else {
                        option.style.display = 'none';
                    }
                });

                // Show/hide "no results" message
                let noResultsMsg = dropdown.querySelector('.no-results-message');
                if (visibleCount === 0 && searchTerm.length > 0) {
                    if (!noResultsMsg) {
                        noResultsMsg = document.createElement('div');
                        noResultsMsg.className = 'no-results-message p-2 text-muted text-center';
                        noResultsMsg.textContent = 'لا توجد منتجات مطابقة للبحث';
                        dropdown.appendChild(noResultsMsg);
                    }
                    noResultsMsg.style.display = 'block';
                } else if (noResultsMsg) {
                    noResultsMsg.style.display = 'none';
                }
            }

            // Show product dropdown
            function showProductDropdown(input) {
                const dropdown = input.nextElementSibling.nextElementSibling;
                dropdown.style.display = 'block';
                updateProductQuantities(dropdown);
            }

            // Hide product dropdown with delay to allow clicks
            function hideProductDropdown(input) {
                setTimeout(() => {
                    const dropdown = input.nextElementSibling.nextElementSibling;
                    dropdown.style.display = 'none';
                }, 200);
            }

            // Select product from dropdown
            function selectProduct(option) {
                const productId = option.dataset.productId;
                const productName = option.dataset.productName;
                const row = option.closest('.product-row');
                const searchInput = row.querySelector('.product-search');
                const hiddenInput = row.querySelector('.product-id-input');
                const dropdown = row.querySelector('.product-dropdown');

                searchInput.value = productName;
                hiddenInput.value = productId;
                dropdown.style.display = 'none';

                // Update available quantity for this specific product
                updateAvailableQuantityForProduct(row, productId);
            }

            // Update product quantities in dropdown using batch request with caching
            async function updateProductQuantities(dropdown) {
                const sourceType = document.getElementById('source_type').value;
                const sourceId = document.getElementById('source_id').value;

                if (!sourceType || !sourceId) {
                    return;
                }

                const options = dropdown.querySelectorAll('.product-option');
                const productIds = Array.from(options).map(option => option.dataset.productId);

                if (productIds.length === 0) {
                    return;
                }

                // Create cache key for current location
                const cacheKey = `${sourceType}_${sourceId}`;

                // Check if we have cached data for this location
                if (quantityCache[cacheKey] && currentCacheKey === cacheKey) {
                    // Use cached data
                    options.forEach(option => {
                        const productId = option.dataset.productId;
                        const quantityBadge = option.querySelector('.quantity-badge');
                        const quantityData = quantityCache[cacheKey][productId];

                        if (quantityData) {
                            const quantity = parseFloat(quantityData.available_quantity).toFixed(2);
                            quantityBadge.textContent = quantity;
                            quantityBadge.className = quantity > 0 ? 'badge bg-success' : 'badge bg-danger';
                        } else {
                            quantityBadge.textContent = '0';
                            quantityBadge.className = 'badge bg-danger';
                        }
                    });
                    return;
                }

                // Set loading state for all badges
                options.forEach(option => {
                    const quantityBadge = option.querySelector('.quantity-badge');
                    quantityBadge.textContent = 'جاري التحميل...';
                    quantityBadge.className = 'badge bg-secondary';
                });

                try {
                    const response = await fetch('{{ user_route('transfers.batch-available-quantities') }}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': '{{ csrf_token() }}'
                        },
                        body: JSON.stringify({
                            location_type: sourceType,
                            location_id: sourceId,
                            product_ids: productIds
                        })
                    });

                    const data = await response.json();

                    if (data.success) {
                        // Cache the data
                        quantityCache[cacheKey] = data.quantities;
                        currentCacheKey = cacheKey;

                        // Update all quantity badges with the fetched data
                        options.forEach(option => {
                            const productId = option.dataset.productId;
                            const quantityBadge = option.querySelector('.quantity-badge');
                            const quantityData = data.quantities[productId];

                            if (quantityData) {
                                const quantity = parseFloat(quantityData.available_quantity).toFixed(2);
                                quantityBadge.textContent = quantity;
                                quantityBadge.className = quantity > 0 ? 'badge bg-success' : 'badge bg-danger';
                            } else {
                                quantityBadge.textContent = '0';
                                quantityBadge.className = 'badge bg-danger';
                            }
                        });
                    } else {
                        // Handle error case
                        options.forEach(option => {
                            const quantityBadge = option.querySelector('.quantity-badge');
                            quantityBadge.textContent = 'خطأ';
                            quantityBadge.className = 'badge bg-danger';
                        });
                    }
                } catch (error) {
                    console.error('Error fetching batch quantities:', error);
                    // Handle error case
                    options.forEach(option => {
                        const quantityBadge = option.querySelector('.quantity-badge');
                        quantityBadge.textContent = 'خطأ';
                        quantityBadge.className = 'badge bg-danger';
                    });
                }
            }

            // Update available quantity for a specific product using cache
            async function updateAvailableQuantityForProduct(row, productId) {
                const availableInput = row.querySelector('.available-quantity');
                const sourceType = document.getElementById('source_type').value;
                const sourceId = document.getElementById('source_id').value;

                if (!sourceType || !sourceId || !productId) {
                    availableInput.value = '0';
                    return;
                }

                const cacheKey = `${sourceType}_${sourceId}`;

                // Check if we have cached data for this product
                if (quantityCache[cacheKey] && quantityCache[cacheKey][productId]) {
                    const quantity = parseFloat(quantityCache[cacheKey][productId].available_quantity).toFixed(2);
                    availableInput.value = quantity;
                    return;
                }

                // If not in cache, make individual request
                try {
                    const response = await fetch('{{ user_route('transfers.available-quantity') }}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': '{{ csrf_token() }}'
                        },
                        body: JSON.stringify({
                            location_type: sourceType,
                            location_id: sourceId,
                            product_id: productId
                        })
                    });

                    const data = await response.json();
                    const quantity = parseFloat(data.available_quantity).toFixed(2);
                    availableInput.value = quantity;

                    // Cache this individual result
                    if (!quantityCache[cacheKey]) {
                        quantityCache[cacheKey] = {};
                    }
                    quantityCache[cacheKey][productId] = data;
                } catch (error) {
                    console.error('Error fetching available quantity:', error);
                    availableInput.value = '0';
                }
            }

            // Event listeners
            document.getElementById('source_type').addEventListener('change', function() {
                updateSourceOptions();
                updateSubmitButton();
                // Clear cache when source type changes
                quantityCache = {};
                currentCacheKey = null;
                // Update available quantities for all products
                document.querySelectorAll('.product-row').forEach(row => {
                    const hiddenInput = row.querySelector('.product-id-input');
                    if (hiddenInput && hiddenInput.value) {
                        updateAvailableQuantityForProduct(row, hiddenInput.value);
                    }
                    // Update dropdown quantities if dropdown is visible
                    const dropdown = row.querySelector('.product-dropdown');
                    if (dropdown && dropdown.style.display !== 'none') {
                        updateProductQuantities(dropdown);
                    }
                });
            });

            document.getElementById('source_id').addEventListener('change', function() {
                updateSubmitButton();
                // Clear cache when source ID changes
                quantityCache = {};
                currentCacheKey = null;
                // Update available quantities for all products
                document.querySelectorAll('.product-row').forEach(row => {
                    const hiddenInput = row.querySelector('.product-id-input');
                    if (hiddenInput && hiddenInput.value) {
                        updateAvailableQuantityForProduct(row, hiddenInput.value);
                    }
                    // Update dropdown quantities if dropdown is visible
                    const dropdown = row.querySelector('.product-dropdown');
                    if (dropdown && dropdown.style.display !== 'none') {
                        updateProductQuantities(dropdown);
                    }
                });
            });

            document.getElementById('destination_type').addEventListener('change', function() {
                updateDestinationOptions();
                updateSubmitButton();
            });

            document.getElementById('destination_id').addEventListener('change', updateSubmitButton);

            // Add form submission debugging
            document.getElementById('transferForm').addEventListener('submit', function(e) {
                console.log('Form submitted');
                console.log('Form action:', this.action);
                console.log('Form data:', new FormData(this));

                // Check if all required fields are filled
                const sourceType = document.getElementById('source_type').value;
                const sourceId = document.getElementById('source_id').value;
                const destinationType = document.getElementById('destination_type').value;
                const destinationId = document.getElementById('destination_id').value;
                const productRows = document.querySelectorAll('.product-row');

                console.log('Source:', sourceType, sourceId);
                console.log('Destination:', destinationType, destinationId);
                console.log('Product rows:', productRows.length);

                if (!sourceType || !sourceId || !destinationType || !destinationId || productRows.length === 0) {
                    console.error('Form validation failed');
                    e.preventDefault();
                    alert('يرجى ملء جميع الحقول المطلوبة');
                    return false;
                }
            });
        </script>
    @endpush
</x-app-layout>
