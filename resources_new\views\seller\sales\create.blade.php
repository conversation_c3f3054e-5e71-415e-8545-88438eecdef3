<x-app-layout>
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="d-sm-flex align-items-center justify-content-between mb-4">
            <div>
                <h1 class="h3 mb-0 text-gray-800">
                    <i class="fas fa-shopping-cart text-primary me-2"></i>
                    إنشاء عملية بيع جديدة
                </h1>
                <p class="mb-0 text-muted">إنشاء فاتورة بيع جديدة للعملاء</p>
            </div>
            <div class="d-flex gap-2">
                <button type="button" class="btn btn-warning" id="togglePricesBtn" onclick="toggleSalePrices()"
                    title="إخفاء الأسعار">
                    <i class="fas fa-eye-slash"></i>
                </button>
                <a href="{{ route('seller.quick-sale') }}" class="btn btn-success">
                    <i class="fas fa-bolt me-2"></i>بيع سريع
                </a>
                <a href="{{ route('seller.sales.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-2"></i>العودة إلى المبيعات
                </a>
            </div>
        </div>

        <form method="POST" action="{{ route('seller.sales.store') }}" id="saleForm">
            @csrf
            <input type="hidden" name="print_after_save" id="printAfterSave" value="0">

            <!-- Basic Information Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-info-circle me-2"></i>المعلومات الأساسية
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="customer_id" class="form-label fw-bold">
                                <i class="fas fa-user text-info me-2"></i>العميل
                            </label>
                            <div class="input-group">
                                <select class="form-select @error('customer_id') is-invalid @enderror" id="customer_id"
                                    name="customer_id" required>
                                    {{-- <option value="">عميل نقدي</option> --}}
                                    @foreach ($customers as $customer)
                                        <option value="{{ $customer->id }}"
                                            data-account-id="{{ $customer->account->id ?? '' }}"
                                            data-phone="{{ $customer->phone ?? '' }}"
                                            data-balance="{{ $customer->getRemainingBalance() }}">
                                            {{ $customer->name }}
                                            {{-- @if ($customer->getRemainingBalance() != 0)
                                                ({{ number_format($customer->getRemainingBalance(), 2) }} ج.م)
                                            @endif --}}
                                        </option>
                                    @endforeach
                                </select>
                                <button type="button" class="btn btn-outline-primary" data-bs-toggle="modal"
                                    data-bs-target="#addCustomerModal">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                            @error('customer_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="col-md-4 mb-3">
                            <label for="branch_id" class="form-label fw-bold">
                                <i class="fas fa-store text-info me-2"></i>الفرع
                            </label>
                            <select class="form-select @error('branch_id') is-invalid @enderror" id="branch_id"
                                name="branch_id" required>
                                @foreach ($branches as $branch)
                                    <option value="{{ $branch->id }}"
                                        {{ old('branch_id', auth()->user()->branch_id) == $branch->id ? 'selected' : '' }}>
                                        {{ $branch->name }}
                                    </option>
                                @endforeach
                            </select>
                            @error('branch_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="col-md-4 mb-3">
                            <label for="notes" class="form-label fw-bold">
                                <i class="fas fa-sticky-note text-warning me-2"></i>ملاحظات
                            </label>
                            <textarea class="form-control @error('notes') is-invalid @enderror" id="notes" name="notes" rows="3"
                                placeholder="أدخل أي ملاحظات إضافية...">{{ old('notes') }}</textarea>
                            @error('notes')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <!-- Products Section -->
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-boxes me-2"></i>المنتجات
                    </h6>
                    <button type="button" class="btn btn-primary btn-sm" id="addProduct">
                        <i class="fas fa-plus me-2"></i>إضافة منتج
                    </button>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" id="productsTable">
                            <thead class="table-light">
                                <tr>
                                    <th width="25%">المنتج</th>
                                    <th width="12%">متوفر</th>
                                    <th width="12%">الكمية</th>
                                    <th width="15%">اختيار السعر</th>
                                    <th width="15%">الخصم</th>
                                    <th width="12%">الإجمالي</th>
                                    <th width="9%">إجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="productsTableBody">
                                <!-- Products will be added here -->
                            </tbody>
                            <tfoot class="table-light">
                                <tr>
                                    <th colspan="4" class="text-end">المجموع الكلي:</th>
                                    <th id="totalAmount">0.00 ج.م</th>
                                    <th></th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                    <div id="emptyProductsMessage" class="text-center py-5 text-muted">
                        <i class="fas fa-shopping-cart fa-3x mb-3"></i>
                        <h5>لا توجد منتجات</h5>
                        <p>انقر على "إضافة منتج" لبدء إضافة المنتجات</p>
                    </div>
                </div>
            </div>

            <!-- Financial Summary -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-calculator me-2"></i>الملخص المالي
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label for="total_amount" class="form-label fw-bold">
                                <i class="fas fa-coins text-info me-2"></i>المبلغ الإجمالي
                            </label>
                            <div class="input-group">
                                <input type="number" step="0.01" class="form-control bg-light" id="total_amount"
                                    name="total_amount" readonly>
                                <span class="input-group-text">ج.م</span>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="discount_amount" class="form-label fw-bold">
                                <i class="fas fa-percentage text-danger me-2"></i>الخصم
                            </label>
                            <div class="row">
                                <div class="col-4">
                                    <select class="form-select" id="discount_type" name="discount_type">
                                        <option value="amount"
                                            {{ old('discount_type', 'amount') == 'amount' ? 'selected' : '' }}>مبلغ
                                        </option>
                                        <option value="percentage"
                                            {{ old('discount_type') == 'percentage' ? 'selected' : '' }}>نسبة</option>
                                    </select>
                                </div>
                                <div class="col-8">
                                    <div class="input-group">
                                        <input type="number" step="0.01"
                                            class="form-control @error('discount_amount') is-invalid @enderror"
                                            id="discount_amount" name="discount_amount"
                                            value="{{ old('discount_amount', 0) }}" min="0">
                                        <span class="input-group-text" id="discount_unit">ج.م</span>
                                    </div>
                                </div>
                            </div>
                            @error('discount_amount')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="paid_amount" class="form-label fw-bold">
                                <i class="fas fa-money-bill text-success me-2"></i>المبلغ المدفوع
                            </label>
                            <div class="input-group">
                                <input type="number" step="0.01"
                                    class="form-control @error('paid_amount') is-invalid @enderror" id="paid_amount"
                                    name="paid_amount" value="{{ old('paid_amount', 0) }}">
                                <span class="input-group-text">ج.م</span>
                            </div>
                            @error('paid_amount')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="remaining_money" class="form-label fw-bold">
                                <i class="fas fa-clock text-warning me-2"></i>المبلغ المتبقي
                            </label>
                            <div class="input-group">
                                <input type="number" step="0.01" class="form-control bg-light"
                                    id="remaining_money" name="remaining_money" readonly>
                                <span class="input-group-text">ج.م</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="card shadow mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                تأكد من إدخال جميع البيانات بشكل صحيح
                            </small>
                        </div>
                        <div class="d-flex gap-2">
                            <a href="{{ route('seller.sales.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </a>
                            <button type="submit" class="btn btn-success btn-lg" id="saveAndPrintBtn">
                                <i class="fas fa-print me-2"></i>حفظ وطباعة
                            </button>
                            <button type="submit" class="btn btn-primary btn-lg" id="saveBtn">
                                <i class="fas fa-save me-2"></i>حفظ العملية
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>

    @push('scripts')
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                let productIndex = 0;
                const productsTableBody = document.getElementById('productsTableBody');
                const productsTable = document.getElementById('productsTable');
                const emptyMessage = document.getElementById('emptyProductsMessage');
                const products = @json($products);

                // Toggle empty message and table visibility
                function toggleEmptyMessage() {
                    const hasProducts = productsTableBody.children.length > 0;
                    emptyMessage.style.display = hasProducts ? 'none' : 'block';
                    if (hasProducts) {
                        productsTable.classList.add('show');
                    } else {
                        productsTable.classList.remove('show');
                    }
                }

                // Add Product Row
                document.getElementById('addProduct').addEventListener('click', function() {
                    const rowHtml = `
                        <tr class="product-row" data-index="${productIndex}">
                            <td>
                                <div class="product-search-container position-relative">
                                    <input type="text" class="form-control product-search" placeholder="ابحث عن المنتج..." autocomplete="off">
                                    <input type="hidden" name="products[${productIndex}][product_id]" class="product-id-input" required>
                                    <div class="product-dropdown position-absolute w-100 bg-white border rounded shadow-sm" style="display: none; max-height: 200px; overflow-y: auto; z-index: 1000;">
                                        ${products.map(product => `
                                                                                                                                                                                                                                                                                                                                            <div class="product-option border-bottom cursor-pointer"
                                                                                                                                                                                                                                                                                                                                                 data-id="${product.id}"
                                                                                                                                                                                                                                                                                                                                                 data-sale-price-1="${product.sale_price_1 || 0}"
                                                                                                                                                                                                                                                                                                                                                 data-sale-price-2="${product.sale_price_2 || 0}"
                                                                                                                                                                                                                                                                                                                                                 data-sale-price-3="${product.sale_price_3 || 0}"
                                                                                                                                                                                                                                                                                                                                                 data-available-quantity="${product.available_quantity || 0}"
                                                                                                                                                                                                                                                                                                                                                 data-name="${product.name}"
                                                                                                                                                                                                                                                                                                                                                 data-sku="${product.sku || ''}">
                                                                                                                                                                                                                                                                                                                                                <div class="p-3 hover-bg-light">
                                                                                                                                                                                                                                                                                                                                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                                                                                                                                                                                                                                                                                                                                        <div>
                                                                                                                                                                                                                                                                                                                                                            <div class="fw-bold text-dark mb-1">${product.name}</div>
                                                                                                                                                                                                                                                                                                                                                            ${product.sku ? `<small class="text-muted">كود: ${product.sku}</small>` : ''}
                                                                                                                                                                                                                                                                                                                                                        </div>
                                                                                                                                                                                                                                                                                                                                                        <span class="badge bg-info text-white">متوفر: ${product.available_quantity || 0}</span>
                                                                                                                                                                                                                                                                                                                                                    </div>

                                                                                                                                                                                                                                                                                                                                                    <div class="sale-prices-info" style="display: ${window.pricesVisible !== false ? 'block' : 'none'}">
                                                                                                                                                                                                                                                                                                                                                        <div class="d-flex gap-1 justify-content-start align-items-center flex-nowrap">
                                                                                                                                                                                                                                                                                                                                                            ${product.sale_price_1 > 0 ? `
                                                                <button type="button" class="btn btn-outline-success btn-sm price-btn flex-shrink-0"
                                                                        data-price="${product.sale_price_1}"
                                                                        data-price-type="1"
                                                                        data-product-id="${product.id}"
                                                                        data-product-name="${product.name}"
                                                                        data-available-quantity="${product.available_quantity || 0}">
                                                                    ${parseFloat(product.sale_price_1).toFixed(2)} ج.م
                                                                </button>
                                                            ` : ''}
                                                                                                                                                                                                                                                                                                                                                            ${product.sale_price_2 > 0 ? `
                                                                <button type="button" class="btn btn-outline-info btn-sm price-btn flex-shrink-0"
                                                                        data-price="${product.sale_price_2}"
                                                                        data-price-type="2"
                                                                        data-product-id="${product.id}"
                                                                        data-product-name="${product.name}"
                                                                        data-available-quantity="${product.available_quantity || 0}">
                                                                    ${parseFloat(product.sale_price_2).toFixed(2)} ج.م
                                                                </button>
                                                            ` : ''}
                                                                                                                                                                                                                                                                                                                                                            ${product.sale_price_3 > 0 ? `
                                                                <button type="button" class="btn btn-outline-warning btn-sm price-btn flex-shrink-0"
                                                                        data-price="${product.sale_price_3}"
                                                                        data-price-type="3"
                                                                        data-product-id="${product.id}"
                                                                        data-product-name="${product.name}"
                                                                        data-available-quantity="${product.available_quantity || 0}">
                                                                    ${parseFloat(product.sale_price_3).toFixed(2)} ج.م
                                                                </button>
                                                            ` : ''}
                                                                                                                                                                                                                                                                                                                                                        </div>
                                                                                                                                                                                                                                                                                                                                                        <small class="text-muted mt-1 d-block">انقر على السعر لاختياره واختيار المنتج</small>
                                                                                                                                                                                                                                                                                                                                                    </div>
                                                                                                                                                                                                                                                                                                                                                </div>
                                                                                                                                                                                                                                                                                                                                            </div>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        `).join('')}
                                    </div>
                                </div>
                            </td>
                            <td>
                                <input type="text" class="form-control available-quantity-display" readonly placeholder="0" style="background-color: #f8f9fa;">
                            </td>
                            <td>
                                <input type="number" name="products[${productIndex}][quantity]" class="form-control quantity-input" min="1" step="1" required>
                            </td>
                            <td>
                                <div class="price-input-container">
                                    <input type="number" name="products[${productIndex}][sale_price]"
                                           class="form-control sale-price-input"
                                           min="0" step="0.01"
                                           required>
                                    <small class="text-muted">يمكن التعديل بعد اختيار المنتج</small>
                                </div>
                            </td>
                            <td>
                                <div class="row">
                                    <div class="col-4">
                                        <select class="form-select discount-type-select">
                                            <option value="amount">مبلغ</option>
                                            <option value="percentage">نسبة</option>
                                        </select>
                                    </div>
                                    <div class="col-8">
                                        <input type="number" name="products[${productIndex}][discount]" class="form-control discount-input" min="0" step="0.01" value="0">
                                    </div>
                                </div>
                            </td>
                            <td>
                                <input type="number" class="form-control total-price-input bg-light" readonly>
                            </td>
                            <td>
                                <button type="button" class="btn btn-danger btn-sm remove-product">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                    `;

                    productsTableBody.insertAdjacentHTML('beforeend', rowHtml);
                    const newRow = productsTableBody.lastElementChild;
                    initializeRowEvents(newRow);
                    productIndex++;
                    toggleEmptyMessage();

                    // Auto-scroll to the new row and focus on product search
                    setTimeout(() => {
                        newRow.scrollIntoView({
                            behavior: 'smooth',
                            block: 'center',
                            inline: 'nearest'
                        });
                        // Focus on the product search input
                        const productSearchInput = newRow.querySelector('.product-search');
                        if (productSearchInput) {
                            productSearchInput.focus();
                        }
                    }, 100);
                });

                // Initialize Events for a Row
                function initializeRowEvents(row) {
                    const productSearch = row.querySelector('.product-search');
                    const productIdInput = row.querySelector('.product-id-input');
                    const productDropdown = row.querySelector('.product-dropdown');
                    const quantityInput = row.querySelector('.quantity-input');
                    const salePriceInput = row.querySelector('.sale-price-input');
                    const discountInput = row.querySelector('.discount-input');
                    const discountTypeSelect = row.querySelector('.discount-type-select');
                    const totalPriceInput = row.querySelector('.total-price-input');
                    const removeButton = row.querySelector('.remove-product');

                    // Handle product search
                    productSearch.addEventListener('input', function() {
                        const searchTerm = this.value.toLowerCase();
                        const options = productDropdown.querySelectorAll('.product-option');
                        let hasVisibleOptions = false;

                        options.forEach(option => {
                            const name = option.dataset.name.toLowerCase();
                            const sku = option.dataset.sku.toLowerCase();

                            if (name.includes(searchTerm) || sku.includes(searchTerm)) {
                                option.style.display = 'block';
                                hasVisibleOptions = true;
                            } else {
                                option.style.display = 'none';
                            }
                        });

                        if (searchTerm && hasVisibleOptions) {
                            productDropdown.style.display = 'block';
                        } else {
                            productDropdown.style.display = 'none';
                        }
                    });

                    // Handle product selection and price button clicks
                    productDropdown.addEventListener('click', function(e) {
                        // Handle price button clicks - auto select product
                        if (e.target.closest('.price-btn')) {
                            e.stopPropagation();
                            const priceBtn = e.target.closest('.price-btn');
                            const price = parseFloat(priceBtn.dataset.price) || 0;
                            const productId = priceBtn.dataset.productId;
                            const productName = priceBtn.dataset.productName;
                            const availableQuantity = parseFloat(priceBtn.dataset.availableQuantity) || 0;

                            // Auto-select the product
                            const availableQuantityDisplay = row.querySelector('.available-quantity-display');
                            const salePriceInput = row.querySelector('.sale-price-input');
                            const quantityInput = row.querySelector('.quantity-input');

                            productSearch.value = productName;
                            productIdInput.value = productId;
                            availableQuantityDisplay.value = availableQuantity.toFixed(2);
                            salePriceInput.value = price.toFixed(2);
                            quantityInput.max = availableQuantity;

                            productDropdown.style.display = 'none';
                            calculateTotal();
                            return;
                        }

                        // Handle product selection
                        const option = e.target.closest('.product-option');
                        if (option) {
                            const availableQuantityDisplay = row.querySelector('.available-quantity-display');
                            const availableQuantity = parseFloat(option.dataset.availableQuantity) || 0;
                            const salePriceInput = row.querySelector('.sale-price-input');

                            productSearch.value = option.dataset.name;
                            productIdInput.value = option.dataset.id;
                            availableQuantityDisplay.value = availableQuantity.toFixed(2);

                            // Always set sale_price_1 as default, especially when prices are hidden
                            const salePrice1 = parseFloat(option.dataset.salePrice1) || 0;
                            if (salePrice1 > 0) {
                                salePriceInput.value = salePrice1.toFixed(2);
                            }

                            // Set max quantity for the quantity input
                            const quantityInput = row.querySelector('.quantity-input');
                            quantityInput.max = availableQuantity;

                            productDropdown.style.display = 'none';
                            calculateTotal();
                        }
                    });

                    // Position dropdown function
                    function positionDropdown() {
                        const rect = productSearch.getBoundingClientRect();
                        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                        const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;

                        // Simple positioning - always show below the input
                        productDropdown.style.top = (rect.bottom + scrollTop + 5) + 'px';
                        productDropdown.style.left = (rect.left + scrollLeft) + 'px';
                        productDropdown.style.width = Math.max(400, rect.width) + 'px';

                        // Ensure dropdown is visible within viewport
                        setTimeout(() => {
                            const dropdownRect = productDropdown.getBoundingClientRect();
                            if (dropdownRect.right > window.innerWidth) {
                                productDropdown.style.left = (window.innerWidth - dropdownRect.width - 10) +
                                    'px';
                            }
                            if (dropdownRect.bottom > window.innerHeight) {
                                productDropdown.style.top = (rect.top + scrollTop - 300) + 'px';
                            }
                        }, 10);
                    }

                    // Show dropdown on focus
                    productSearch.addEventListener('focus', function() {
                        if (this.value) {
                            productDropdown.style.display = 'block';
                        }
                    });

                    // Hide dropdown when clicking outside
                    document.addEventListener('click', function(e) {
                        if (!row.contains(e.target)) {
                            productDropdown.style.display = 'none';
                        }
                    });

                    // Reposition dropdown on window resize
                    window.addEventListener('resize', function() {
                        if (productDropdown.style.display === 'block') {
                            positionDropdown();
                        }
                    });

                    // Calculate Total Price
                    function calculateTotal() {
                        const quantity = parseFloat(quantityInput.value) || 0;
                        const price = parseFloat(salePriceInput.value) || 0;
                        const discountValue = parseFloat(discountInput.value) || 0;
                        const discountType = discountTypeSelect.value;

                        let subtotal = quantity * price;
                        let discountAmount = 0;

                        if (discountType === 'percentage') {
                            discountAmount = (subtotal * discountValue) / 100;
                        } else {
                            discountAmount = discountValue;
                        }

                        const total = subtotal - discountAmount;
                        totalPriceInput.value = total.toFixed(2);
                        updateSaleTotals();
                    }

                    quantityInput.addEventListener('input', calculateTotal);
                    salePriceInput.addEventListener('input', calculateTotal);
                    discountInput.addEventListener('input', calculateTotal);
                    discountTypeSelect.addEventListener('change', calculateTotal);

                    // Remove Row
                    removeButton.addEventListener('click', function() {
                        row.remove();
                        updateSaleTotals();
                        toggleEmptyMessage();
                    });
                }

                // Update Sale Totals
                function updateSaleTotals() {
                    let total = 0;
                    document.querySelectorAll('.total-price-input').forEach(input => {
                        total += parseFloat(input.value) || 0;
                    });

                    // Update table footer total
                    document.getElementById('totalAmount').textContent = total.toFixed(2) + ' ج.م';

                    // Calculate discount
                    const discountType = document.getElementById('discount_type').value;
                    const discountValue = parseFloat(document.getElementById('discount_amount').value) || 0;
                    let discountAmount = 0;

                    if (discountType === 'percentage') {
                        discountAmount = (total * discountValue) / 100;
                    } else {
                        discountAmount = discountValue;
                    }

                    // Update financial summary
                    const paid = parseFloat(document.getElementById('paid_amount').value) || 0;
                    const remaining = total - discountAmount - paid;

                    document.getElementById('total_amount').value = total.toFixed(2);
                    document.getElementById('remaining_money').value = remaining.toFixed(2);
                }

                // Handle discount type change
                document.getElementById('discount_type').addEventListener('change', function() {
                    const discountUnit = document.getElementById('discount_unit');
                    const discountAmount = document.getElementById('discount_amount');

                    if (this.value === 'percentage') {
                        discountUnit.textContent = '%';
                        discountAmount.max = '100';
                        discountAmount.placeholder = 'نسبة الخصم';
                    } else {
                        discountUnit.textContent = 'ج.م';
                        discountAmount.removeAttribute('max');
                        discountAmount.placeholder = 'مبلغ الخصم';
                    }
                    updateSaleTotals();
                });

                // Update totals when discount or paid amount changes
                document.getElementById('discount_amount').addEventListener('input', updateSaleTotals);
                document.getElementById('paid_amount').addEventListener('input', updateSaleTotals);

                // Initialize discount type
                document.getElementById('discount_type').dispatchEvent(new Event('change'));

                // Initialize empty message
                toggleEmptyMessage();

                // Handle print button
                document.getElementById('saveAndPrintBtn').addEventListener('click', function(e) {
                    e.preventDefault();
                    document.getElementById('printAfterSave').value = '1';
                    document.getElementById('saleForm').submit();
                });

                // Handle regular save button
                document.getElementById('saveBtn').addEventListener('click', function(e) {
                    e.preventDefault();
                    document.getElementById('printAfterSave').value = '0';
                    document.getElementById('saleForm').submit();
                });
            });

            // Initialize global price visibility state
            window.pricesVisible = true;

            // Toggle sale prices visibility globally
            function toggleSalePrices() {
                const pricesInfo = document.querySelectorAll('.sale-prices-info');
                const toggleBtn = document.getElementById('togglePricesBtn');

                window.pricesVisible = !window.pricesVisible;

                // Update button state regardless of whether there are price elements
                if (window.pricesVisible) {
                    toggleBtn.innerHTML = '<i class="fas fa-eye-slash"></i>';
                    toggleBtn.className = 'btn btn-warning';
                    toggleBtn.title = 'إخفاء الأسعار';
                } else {
                    toggleBtn.innerHTML = '<i class="fas fa-eye"></i>';
                    toggleBtn.className = 'btn btn-success';
                    toggleBtn.title = 'إظهار الأسعار';
                }

                // Update existing price info elements
                pricesInfo.forEach(info => {
                    info.style.display = window.pricesVisible ? 'block' : 'none';
                });
            }
        </script>
    @endpush

    @push('styles')
        <style>
            #productsTable {
                display: none;
            }

            #productsTable.show {
                display: table !important;
            }

            .product-row td {
                vertical-align: middle;
                padding: 0.5rem;
            }

            .product-search-container {
                position: relative;
            }

            .product-dropdown {
                top: 100%;
                left: 0;
                right: 0;
                background: white;
                border: 1px solid #dee2e6;
                border-radius: 0.375rem;
                box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
                z-index: 1050;
                max-height: 300px;
                overflow-y: auto;
            }

            .product-option {
                border-bottom: 1px solid #f8f9fa;
                cursor: pointer;
                transition: background-color 0.15s ease-in-out;
            }

            .product-option:hover {
                background-color: #f8f9fa;
            }

            .product-option:last-child {
                border-bottom: none;
            }

            .hover-bg-light:hover {
                background-color: #f8f9fa;
            }

            .price-btn {
                transition: all 0.15s ease-in-out;
                border-radius: 0.25rem;
                font-size: 0.75rem;
                margin: 0.125rem;
                padding: 0.25rem 0.5rem;
                min-width: 70px;
                white-space: nowrap;
            }

            .price-btn:hover {
                transform: translateY(-1px);
                box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            }

            .price-input-container small {
                font-size: 0.75rem;
                margin-top: 0.25rem;
                display: block;
            }

            .sale-price-input:focus {
                border-color: #86b7fe;
                box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
            }

            .cursor-pointer {
                cursor: pointer;
            }
        </style>
    @endpush

    @push('scripts')
        <!-- Select2 CSS -->
        <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
        <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css"
            rel="stylesheet" />

        <!-- Select2 JS -->
        <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

        <script>
            $(document).ready(function() {
                // Initialize Select2 for customer dropdown
                $('#customer_id').select2({
                    theme: 'bootstrap-5',
                    placeholder: 'ابحث عن العميل أو اختر عميل نقدي...',
                    allowClear: true,
                    dir: 'rtl',
                    language: {
                        noResults: function() {
                            return 'لا توجد نتائج';
                        },
                        searching: function() {
                            return 'جاري البحث...';
                        },
                        inputTooShort: function() {
                            return 'يرجى إدخال حرف واحد على الأقل';
                        }
                    },
                    templateResult: function(option) {
                        if (!option.id) {
                            return option.text;
                        }

                        var $option = $(option.element);
                        var balance = $option.data('balance');
                        var phone = $option.data('phone');

                        var $result = $('<div></div>');
                        $result.text(option.text);

                        // if (phone) {
                        //     $result.append('<br><small class="text-muted">📞 ' + phone + '</small>');
                        // }

                        return $result;
                    }
                });
            });
        </script>

        <style>
            /* Fix Select2 RTL close button positioning */
            .select2-container--bootstrap-5[dir="rtl"] .select2-selection--single .select2-selection__clear {
                right: auto !important;
                left: 7px !important;
            }

            .select2-container--bootstrap-5[dir="rtl"] .select2-selection--single .select2-selection__rendered {
                padding-left: 25px !important;
                padding-right: 12px !important;
            }

            .select2-container--bootstrap-5[dir="rtl"] .select2-selection--single .select2-selection__arrow {
                right: auto !important;
                left: 1px !important;
            }

            /* Ensure proper spacing for Arabic text */
            .select2-container--bootstrap-5 .select2-selection--single .select2-selection__rendered {
                line-height: 1.5;
                text-align: right;
            }
        </style>
    @endpush
</x-app-layout>
