<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SaleItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'sale_id',
        'product_id',
        'quantity',
        'price',
        'subtotal',
    ];

    public function sale()
    {
        return $this->belongsTo(Sale::class);
    }

    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    public function returnItems()
    {
        return $this->hasMany(SaleReturnItem::class);
    }

    // Helper methods
    public function getTotalReturnedQuantityAttribute(): float
    {
        return $this->returnItems()
            ->whereHas('saleReturn', function ($q) {
                $q->where('status', '!=', 'cancelled');
            })
            ->sum('quantity_returned');
    }

    public function getRemainingQuantityAttribute(): float
    {
        return $this->quantity - $this->total_returned_quantity;
    }

    public function canBeReturned(): bool
    {
        return $this->remaining_quantity > 0;
    }

    public function getReturnPercentageAttribute(): float
    {
        if ($this->quantity <= 0) {
            return 0;
        }
        return ($this->total_returned_quantity / $this->quantity) * 100;
    }
}
