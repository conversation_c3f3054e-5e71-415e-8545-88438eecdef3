<?php

namespace App\Http\Controllers;

use App\Models\Branch;
use App\Models\Role;
use App\Models\User;
use App\Models\Permission;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Storage;

class SettingController extends Controller
{
    public function users(Request $request)
    {
        $query = User::with(['role', 'branch'])
            ->when($request->filled('search'), function ($query) use ($request) {
                $search = $request->search;
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                        ->orWhere('email', 'like', "%{$search}%")
                        ->orWhere('phone', 'like', "%{$search}%");
                });
            })
            ->when($request->filled('role'), function ($query) use ($request) {
                $query->where('role_id', $request->role);
            })
            ->when($request->filled('branch'), function ($query) use ($request) {
                $query->where('branch_id', $request->branch);
            })
            ->when($request->filled('status'), function ($query) use ($request) {
                $query->where('is_active', $request->status === 'active');
            });

        $users = $query->latest()->paginate(10);
        $roles = Role::where('is_active', true)->get();
        $branches = Branch::where('is_active', true)->get();

        return view('users.index', compact('users', 'roles', 'branches'));
    }

    public function createUser()
    {
        $roles = Role::where('is_active', true)->get();
        $branches = Branch::where('is_active', true)->get();
        return view('users.create', compact('roles', 'branches'));
    }

    public function storeUser(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'phone' => 'required|string|max:20|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'role_id' => 'required|exists:roles,id',
            'branch_id' => 'required|exists:branches,id',
            'is_active' => 'boolean',
        ]);

        $validated['password'] = Hash::make($validated['password']);
        $validated['is_active'] = $request->boolean('is_active', true);

        User::create($validated);

        return redirect()->route('admin.settings.users')
            ->with('success', 'تم إضافة المستخدم بنجاح');
    }

    public function showUser(User $user)
    {
        $user->load(['role', 'branch']);

        // Get user statistics
        $stats = [
            'total_sales' => $user->sales()->count(),
            'total_sales_amount' => $user->sales()->sum('total_amount') ?? 0,
            'last_login' => $user->last_login_at,
            'account_created' => $user->created_at,
        ];

        // Get recent activity (last 10 sales)
        $recentSales = $user->sales()
            ->with(['customer', 'branch'])
            ->latest()
            ->limit(10)
            ->get();

        return view('users.show', compact('user', 'stats', 'recentSales'));
    }

    public function editUser(User $user)
    {
        $roles = Role::where('is_active', true)->get();
        $branches = Branch::where('is_active', true)->get();
        return view('users.edit', compact('user', 'roles', 'branches'));
    }

    public function updateUser(Request $request, User $user)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => ['required', 'string', 'email', 'max:255', Rule::unique('users')->ignore($user->id)],
            'phone' => ['required', 'string', 'max:20', Rule::unique('users')->ignore($user->id)],
            'password' => 'nullable|string|min:8|confirmed',
            'role_id' => 'required|exists:roles,id',
            'branch_id' => 'required|exists:branches,id',
            'is_active' => 'boolean',
        ]);

        if ($request->filled('password')) {
            $validated['password'] = Hash::make($validated['password']);
        } else {
            unset($validated['password']);
        }

        $validated['is_active'] = $request->boolean('is_active', true);

        $user->update($validated);

        return redirect()->route('admin.settings.users')
            ->with('success', 'تم تحديث المستخدم بنجاح');
    }

    public function destroyUser(User $user)
    {
        if ($user->id === auth()->id()) {
            return back()->with('error', 'لا يمكن حذف المستخدم الحالي');
        }

        $user->delete();

        return redirect()->route('admin.settings.users')
            ->with('success', 'تم حذف المستخدم بنجاح');
    }

    public function roles(Request $request)
    {
        $query = Role::withCount('users')
            ->when($request->filled('search'), function ($query) use ($request) {
                $search = $request->search;
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                        ->orWhere('description', 'like', "%{$search}%");
                });
            })
            ->when($request->filled('permission'), function ($query) use ($request) {
                $query->whereJsonContains('permissions', $request->permission);
            });

        $sortBy = $request->input('sort_by', 'name_asc');
        switch ($sortBy) {
            case 'name_desc':
                $query->orderByDesc('name');
                break;
            case 'users_count_asc':
                $query->orderBy('users_count');
                break;
            case 'users_count_desc':
                $query->orderByDesc('users_count');
                break;
            default:
                $query->orderBy('name');
        }

        $roles = $query->paginate(10);
        $permissions = Permission::where('is_active', true)->get();
        return view('roles.index', compact('roles', 'permissions'));
    }

    public function createRole()
    {
        return view('roles.create');
    }

    public function storeRole(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:roles',
            'description' => 'nullable|string',
            'permissions' => 'required|array',
            'permissions.*' => 'required|string|exists:permissions,name',
            'is_active' => 'boolean',
        ]);

        $validated['is_active'] = $request->boolean('is_active', true);

        Role::create($validated);

        return redirect()->route('roles.index')
            ->with('success', 'تم إضافة الدور بنجاح');
    }

    public function editRole(Role $role)
    {
        return view('roles.edit', compact('role'));
    }

    public function updateRole(Request $request, Role $role)
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255', Rule::unique('roles')->ignore($role->id)],
            'description' => 'nullable|string',
            'permissions' => 'required|array',
            'permissions.*' => 'required|string|exists:permissions,name',
            'is_active' => 'boolean',
        ]);

        $validated['is_active'] = $request->boolean('is_active', true);

        $role->update($validated);

        return redirect()->route('roles.index')
            ->with('success', 'تم تحديث الدور بنجاح');
    }

    public function destroyRole(Role $role)
    {
        if ($role->users()->exists()) {
            return back()->with('error', 'لا يمكن حذف هذا الدور لوجود مستخدمين مرتبطين به');
        }

        $role->delete();

        return redirect()->route('roles.index')
            ->with('success', 'تم حذف الدور بنجاح');
    }

    public function company()
    {
        $settings = \App\Models\Setting::first();
        return view('settings.company', compact('settings'));
    }

    public function updateCompany(Request $request)
    {
        $validated = $request->validate([
            'company_name' => 'required|string|max:255',
            'company_address' => 'nullable|string',
            'company_phone' => 'nullable|string|max:20',
            'company_email' => 'nullable|email|max:255',
            'company_tax_number' => 'nullable|string|max:50',
            'company_logo' => 'nullable|image|max:2048',
            'company_currency' => 'required|in:EGP,SAR,USD,EUR'
        ]);

        $settings = \App\Models\Setting::first() ?? new \App\Models\Setting();

        if ($request->hasFile('company_logo')) {
            if ($settings->company_logo) {
                Storage::delete($settings->company_logo);
            }
            $validated['company_logo'] = $request->file('company_logo')->store('company', 'public');
        }

        $settings->fill($validated);
        $settings->save();

        return redirect()
            ->route('settings.company')
            ->with('success', 'تم تحديث معلومات الشركة بنجاح');
    }
}
