<?php

namespace App\Http\Controllers;

use App\Models\Purchase;
use App\Models\PurchaseItem;
use App\Models\PurchaseItemDistribution;
use App\Models\Branch;
use App\Models\Store;
use App\Models\BranchInventory;
use App\Models\StoreInventory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use App\Helpers\AlertHelper;

class PurchaseDistributionController extends Controller
{
    /**
     * Show distribution form for a purchase
     */
    public function show(Purchase $purchase)
    {
        // Only show distribution for purchases that haven't been distributed yet
        if ($purchase->is_distributed) {
            AlertHelper::warning('هذه المشتريات تم توزيعها بالفعل');
            return redirect()->route('admin.purchases.show', $purchase);
        }

        $purchase->load(['items.product', 'supplier']);
        $branches = Branch::where('is_active', true)->get();
        $stores = Store::where('is_active', true)->get();

        return view('admin.purchases.distribute', compact('purchase', 'branches', 'stores'));
    }

    /**
     * Execute the distribution
     */
    public function store(Request $request, Purchase $purchase)
    {
        // Validate that purchase hasn't been distributed yet
        if ($purchase->is_distributed) {
            AlertHelper::error('هذه المشتريات تم توزيعها بالفعل');
            return back();
        }

        $validated = $request->validate([
            'distributions' => 'required|array',
            'distributions.*' => 'required|array',
            'distributions.*.*.location_type' => 'required|in:branch,store',
            'distributions.*.*.location_id' => 'required|integer',
            'distributions.*.*.quantity' => 'required|numeric|min:0.01',
            'distributions.*.*.sale_price_1' => 'required|numeric|min:0.01',
            'distributions.*.*.sale_price_2' => 'nullable|numeric|min:0.01',
            'distributions.*.*.sale_price_3' => 'nullable|numeric|min:0.01',
        ]);

        DB::beginTransaction();
        try {
            foreach ($validated['distributions'] as $itemId => $distributions) {
                $purchaseItem = PurchaseItem::where('id', $itemId)
                    ->where('purchase_id', $purchase->id)
                    ->firstOrFail();

                // Validate total distributed quantity
                $totalDistributedQuantity = collect($distributions)->sum('quantity');
                if (abs($totalDistributedQuantity - $purchaseItem->quantity) > 0.01) {
                    throw new \Exception("الكمية الموزعة للمنتج {$purchaseItem->product->name} لا تساوي الكمية المشتراة");
                }

                // Create distribution records and add to inventory
                foreach ($distributions as $distribution) {
                    // Validate sale price is not below cost price
                    if ($distribution['sale_price_1'] < $purchaseItem->cost_price) {
                        throw new \Exception("سعر البيع 1 للمنتج {$purchaseItem->product->name} لا يمكن أن يكون أقل من سعر التكلفة");
                    }

                    // Create distribution record
                    PurchaseItemDistribution::create([
                        'purchase_item_id' => $purchaseItem->id,
                        'location_type' => $distribution['location_type'],
                        'location_id' => $distribution['location_id'],
                        'quantity' => $distribution['quantity'],
                        'cost_price' => $purchaseItem->cost_price,
                        'sale_price_1' => $distribution['sale_price_1'],
                        'sale_price_2' => $distribution['sale_price_2'],
                        'sale_price_3' => $distribution['sale_price_3'],
                    ]);

                    // Add to inventory
                    $this->addToInventory(
                        $distribution['location_type'],
                        $distribution['location_id'],
                        $purchaseItem->product_id,
                        $distribution['quantity'],
                        $purchaseItem->cost_price,
                        $distribution['sale_price_1'],
                        $distribution['sale_price_2'],
                        $distribution['sale_price_3']
                    );
                }

                // Update purchase item
                $purchaseItem->update([
                    'distributed_quantity' => $totalDistributedQuantity,
                    'is_distributed' => true,
                ]);
            }

            // Mark purchase as distributed
            $purchase->update([
                'is_distributed' => true,
                'distributed_at' => now(),
                'distributed_by' => Auth::id(),
                'status' => 'completed'
            ]);

            DB::commit();

            AlertHelper::success('تم توزيع المشتريات بنجاح');
            return redirect()->route('admin.purchases.show', $purchase);
        } catch (\Exception $e) {
            DB::rollBack();
            AlertHelper::error('حدث خطأ: ' . $e->getMessage());
            return back()->withInput();
        }
    }

    /**
     * Add items to inventory (branch or store)
     */
    private function addToInventory($locationType, $locationId, $productId, $quantity, $costPrice, $salePrice1, $salePrice2 = null, $salePrice3 = null)
    {
        if ($locationType === 'branch') {
            $inventory = BranchInventory::where('branch_id', $locationId)
                ->where('product_id', $productId)
                ->first();

            if ($inventory) {
                // Update existing inventory
                $inventory->increment('quantity', $quantity);
                $inventory->update([
                    'cost_price' => $costPrice,
                    'sale_price_1' => $salePrice1,
                    'sale_price_2' => $salePrice2,
                    'sale_price_3' => $salePrice3,
                ]);
            } else {
                // Create new inventory record
                BranchInventory::create([
                    'branch_id' => $locationId,
                    'product_id' => $productId,
                    'quantity' => $quantity,
                    'cost_price' => $costPrice,
                    'sale_price_1' => $salePrice1,
                    'sale_price_2' => $salePrice2,
                    'sale_price_3' => $salePrice3,
                ]);
            }
        } else {
            $inventory = StoreInventory::where('store_id', $locationId)
                ->where('product_id', $productId)
                ->first();

            if ($inventory) {
                // Update existing inventory
                $inventory->increment('quantity', $quantity);
                $inventory->update([
                    'cost_price' => $costPrice,
                    'sale_price_1' => $salePrice1,
                    'sale_price_2' => $salePrice2,
                    'sale_price_3' => $salePrice3,
                ]);
            } else {
                // Create new inventory record
                StoreInventory::create([
                    'store_id' => $locationId,
                    'product_id' => $productId,
                    'quantity' => $quantity,
                    'minimum_stock' => 0,
                    'maximum_stock' => null,
                    'cost_price' => $costPrice,
                    'sale_price_1' => $salePrice1,
                    'sale_price_2' => $salePrice2,
                    'sale_price_3' => $salePrice3,
                ]);
            }
        }
    }
}
