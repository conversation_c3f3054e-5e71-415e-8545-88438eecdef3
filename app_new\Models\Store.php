<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Store extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'code',
        'branch_id',
        'address',
        'phone',
        'email',
        'manager_id',
        'opening_balance',
        'is_active',
        'description',
    ];

    protected $casts = [
        'opening_balance' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class)->withDefault();
    }

    public function manager(): BelongsTo
    {
        return $this->belongsTo(User::class, 'manager_id');
    }

    public function users(): HasMany
    {
        return $this->hasMany(User::class);
    }

    public function inventory(): BelongsToMany
    {
        return $this->belongsToMany(Product::class, 'store_inventory')
            ->withPivot('quantity', 'minimum_stock', 'maximum_stock')
            ->withTimestamps();
    }

    public function storeInventories(): HasMany
    {
        return $this->hasMany(StoreInventory::class);
    }

    public function outgoingTransfers(): HasMany
    {
        return $this->hasMany(InventoryTransfer::class, 'source_id')
            ->where('source_type', 'store');
    }

    public function incomingTransfers(): HasMany
    {
        return $this->hasMany(InventoryTransfer::class, 'destination_id')
            ->where('destination_type', 'store');
    }

    public function sales(): HasMany
    {
        return $this->hasMany(Sale::class);
    }

    public function purchases(): HasMany
    {
        return $this->hasMany(Purchase::class);
    }

    public function expenses(): HasMany
    {
        return $this->hasMany(Expense::class);
    }

    public function cashTransactions(): HasMany
    {
        return $this->hasMany(CashTransaction::class);
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeForBranch($query, int $branchId)
    {
        return $query->where('branch_id', $branchId);
    }

    public function scopeIndependent($query)
    {
        return $query->whereNull('branch_id');
    }

    public function scopeAccessibleBy($query, User $user)
    {
        if ($user->canAccessAllBranches()) {
            return $query;
        }

        return $query->where(function ($q) use ($user) {
            // Include stores from user's branch
            if ($user->branch_id) {
                $q->where('branch_id', $user->branch_id);
            }

            // Include independent stores if user can access them
            if ($user->canAccessIndependentStores()) {
                $q->orWhereNull('branch_id');
            }
        });
    }

    public function getInventoryValue(): float
    {
        return $this->inventory()
            ->withPivot('quantity', 'cost_price')
            ->get()
            ->sum(function ($product) {
                $costPrice = $product->pivot->cost_price ?? $product->price ?? 0;
                return $costPrice * $product->pivot->quantity;
            });
    }

    public function getTotalSales(): float
    {
        return $this->sales()->sum('total_amount');
    }

    public function getTotalPurchases(): float
    {
        return $this->purchases()->sum('total_amount');
    }

    public function isIndependent(): bool
    {
        return is_null($this->branch_id);
    }

    public function getLocationTypeAttribute(): string
    {
        return $this->isIndependent() ? 'independent_store' : 'branch_store';
    }

    public function getFullLocationNameAttribute(): string
    {
        if ($this->isIndependent()) {
            return $this->name . ' (مستقل)';
        }

        return $this->name . ' (' . ($this->branch->name ?? 'فرع غير محدد') . ')';
    }

    public function getProfit(): float
    {
        return $this->sales()->sum('profit');
    }
}
