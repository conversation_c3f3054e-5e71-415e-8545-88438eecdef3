<x-app-layout>
    <x-slot name="header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                    <i class="fas fa-plus text-primary"></i> {{ __('إضافة منتج جديد') }}
                </h2>
                <p class="text-muted small mb-0">إنشاء منتج جديد وإضافته إلى المخزون</p>
            </div>
            <div class="d-flex gap-2">
                <a href="{{ user_route('products.index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right"></i> العودة للقائمة
                </a>
            </div>
        </div>
    </x-slot>

    <div class="container-fluid px-4">
        <!-- Breadcrumb -->
        <div class="row align-items-center mb-4">
            <div class="col-md-6">
                <nav aria-label="breadcrumb" class="d-flex justify-content-start">
                    <ol class="breadcrumb mb-0 bg-light px-3 py-2 rounded">
                        <li class="breadcrumb-item">
                            <a href="{{ user_route('dashboard') }}" class="text-decoration-none">
                                <i class="fas fa-home me-1"></i>الرئيسية
                            </a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="{{ user_route('products.index') }}" class="text-decoration-none">
                                <i class="fas fa-boxes me-1"></i>المنتجات
                            </a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">
                            <i class="fas fa-plus me-1"></i>إضافة جديد
                        </li>
                    </ol>
                </nav>
            </div>
        </div>

        <div class="row">
            <!-- Preview Section -->
            <div class="col-xl-4 col-lg-5">
                <div class="card shadow border-0 h-100">
                    <div class="card-header bg-gradient-success text-white py-3">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-eye fa-lg me-3"></i>
                            <h5 class="mb-0 fw-bold">معاينة المنتج</h5>
                        </div>
                    </div>
                    <div class="card-body p-4">
                        <!-- Product Image Preview -->
                        <div class="text-center mb-4">
                            <div class="product-image-preview mx-auto mb-3" style="width: 150px; height: 150px;">
                                <img id="preview-image" src="{{ asset('images/no-image.png') }}" alt="معاينة الصورة"
                                    class="img-fluid rounded border"
                                    style="width: 100%; height: 100%; object-fit: cover;">
                            </div>
                            <h4 class="fw-bold text-primary" id="preview-name">اسم المنتج</h4>
                            <p class="text-muted small" id="preview-category">الفئة</p>
                            <p class="text-muted" id="preview-description">وصف المنتج</p>
                        </div>

                        <!-- Preview Details -->
                        <div class="preview-details">
                            <div
                                class="detail-item d-flex justify-content-between align-items-center py-2 border-bottom">
                                <span class="fw-bold text-muted">الاسم:</span>
                                <span id="preview-name-text">غير محدد</span>
                            </div>
                            <div
                                class="detail-item d-flex justify-content-between align-items-center py-2 border-bottom">
                                <span class="fw-bold text-muted">الفئة:</span>
                                <span id="preview-category-text">غير محددة</span>
                            </div>
                            <div
                                class="detail-item d-flex justify-content-between align-items-center py-2 border-bottom">
                                <span class="fw-bold text-muted">السعر:</span>
                                <span id="preview-price">0 ج.م</span>
                            </div>
                            <div
                                class="detail-item d-flex justify-content-between align-items-center py-2 border-bottom">
                                <span class="fw-bold text-muted">سعر البيع:</span>
                                <span id="preview-selling-price">0 ج.م</span>
                            </div>
                            <div class="detail-item d-flex justify-content-between align-items-center py-2">
                                <span class="fw-bold text-muted">الحالة:</span>
                                <span class="badge bg-warning">جديد</span>
                            </div>
                        </div>

                        <!-- Product Stats Placeholder -->
                        <div class="mt-4 p-3 bg-light rounded">
                            <h6 class="fw-bold mb-3">إحصائيات المنتج</h6>
                            <div class="row text-center">
                                <div class="col-6">
                                    <div class="fw-bold text-primary">0</div>
                                    <small class="text-muted">الكمية</small>
                                </div>
                                <div class="col-6">
                                    <div class="fw-bold text-success">0 ج.م</div>
                                    <small class="text-muted">قيمة المخزون</small>
                                </div>
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="mt-4 pt-3 border-top">
                            <h6 class="fw-bold mb-3">بعد الحفظ يمكنك:</h6>
                            <div class="d-grid gap-2">
                                <button class="btn btn-outline-primary btn-sm" disabled>
                                    <i class="fas fa-warehouse me-2"></i>إدارة المخزون
                                </button>
                                <button class="btn btn-outline-info btn-sm" disabled>
                                    <i class="fas fa-edit me-2"></i>تعديل المنتج
                                </button>
                                <button class="btn btn-outline-success btn-sm" disabled>
                                    <i class="fas fa-shopping-cart me-2"></i>إضافة للمبيعات
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Section -->
            <div class="col-xl-8 col-lg-7">
                <div class="card shadow border-0 h-100">
                    <div class="card-header bg-gradient-primary text-white py-3">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-edit fa-lg me-3"></i>
                            <h5 class="mb-0 fw-bold">بيانات المنتج</h5>
                        </div>
                    </div>
                    <div class="card-body p-4">
                        <form action="{{ user_route('products.store') }}" method="POST" enctype="multipart/form-data"
                            id="product-form">
                            @csrf

                            <!-- Basic Information -->
                            <div class="row">
                                <!-- Product Name -->
                                <div class="col-md-6 mb-4">
                                    <label for="name" class="form-label fw-bold">
                                        <i class="fas fa-tag text-primary me-2"></i>اسم المنتج
                                        <span class="text-danger">*</span>
                                    </label>
                                    <div class="input-group input-group-lg">
                                        <span class="input-group-text">
                                            <i class="fas fa-box text-muted"></i>
                                        </span>
                                        <input type="text" class="form-control @error('name') is-invalid @enderror"
                                            id="name" name="name" value="{{ old('name') }}"
                                            placeholder="أدخل اسم المنتج..." required>
                                        @error('name')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    <div class="form-text">
                                        <i class="fas fa-info-circle text-info me-1"></i>
                                        اختر اسماً واضحاً ومميزاً للمنتج
                                    </div>
                                </div>

                                <!-- Category -->
                                <div class="col-md-6 mb-4">
                                    <label for="category_id" class="form-label fw-bold">
                                        <i class="fas fa-tags text-info me-2"></i>الفئة
                                        <span class="text-danger">*</span>
                                    </label>
                                    <div class="input-group input-group-lg">
                                        <span class="input-group-text">
                                            <i class="fas fa-list text-muted"></i>
                                        </span>
                                        <select class="form-select @error('category_id') is-invalid @enderror"
                                            id="category_id" name="category_id" required>
                                            <option value="">اختر الفئة</option>
                                            @foreach ($categories as $category)
                                                <option value="{{ $category->id }}"
                                                    {{ old('category_id') == $category->id ? 'selected' : '' }}>
                                                    {{ $category->name }}
                                                </option>
                                            @endforeach
                                        </select>
                                        @error('category_id')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    <div class="form-text">
                                        <i class="fas fa-info-circle text-info me-1"></i>
                                        اختر الفئة المناسبة لتصنيف المنتج
                                    </div>
                                </div>
                            </div>

                            <!-- SKU and Status -->
                            <div class="row">
                                <!-- SKU -->
                                <div class="col-md-6 mb-4">
                                    <label for="sku" class="form-label fw-bold">
                                        <i class="fas fa-barcode text-secondary me-2"></i>رمز المنتج (SKU)
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="fas fa-hashtag text-muted"></i>
                                        </span>
                                        <input type="text" class="form-control @error('sku') is-invalid @enderror"
                                            id="sku" name="sku" value="{{ old('sku') }}"
                                            placeholder="مثال: PRD-001">
                                        @error('sku')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    <div class="form-text">
                                        <i class="fas fa-info-circle text-info me-1"></i>
                                        رمز فريد للمنتج (اختياري - سيتم إنشاؤه تلقائياً)
                                    </div>
                                </div>

                                <!-- Status -->
                                <div class="col-md-6 mb-4">
                                    <label class="form-label fw-bold">
                                        <i class="fas fa-toggle-on text-success me-2"></i>حالة المنتج
                                    </label>
                                    <div class="form-check form-switch mt-2">
                                        <input class="form-check-input @error('is_active') is-invalid @enderror"
                                            type="checkbox" id="is_active" name="is_active" value="1"
                                            {{ old('is_active', true) ? 'checked' : '' }}>
                                        <label class="form-check-label fw-bold" for="is_active">
                                            منتج نشط
                                        </label>
                                        @error('is_active')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    <div class="form-text">
                                        <i class="fas fa-info-circle text-info me-1"></i>
                                        المنتجات النشطة فقط تظهر في المبيعات
                                    </div>
                                </div>
                            </div>

                            <!-- Pricing -->
                            <div class="row">
                                <!-- Cost Price -->
                                <div class="col-md-6 mb-4">
                                    <label for="price" class="form-label fw-bold">
                                        <i class="fas fa-dollar-sign text-warning me-2"></i>سعر التكلفة
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text">ج.م</span>
                                        <input type="number" step="0.01" min="0"
                                            class="form-control @error('price') is-invalid @enderror" id="price"
                                            name="price" value="{{ old('price') }}" placeholder="0.00">
                                        @error('price')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    <div class="form-text">
                                        <i class="fas fa-info-circle text-info me-1"></i>
                                        سعر شراء المنتج من المورد
                                    </div>
                                </div>

                                <!-- Selling Price -->
                                <div class="col-md-6 mb-4">
                                    <label for="selling_price" class="form-label fw-bold">
                                        <i class="fas fa-money-bill text-success me-2"></i>سعر البيع
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text">ج.م</span>
                                        <input type="number" step="0.01" min="0"
                                            class="form-control @error('selling_price') is-invalid @enderror"
                                            id="selling_price" name="selling_price"
                                            value="{{ old('selling_price') }}" placeholder="0.00">
                                        @error('selling_price')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    <div class="form-text">
                                        <i class="fas fa-info-circle text-info me-1"></i>
                                        سعر بيع المنتج للعملاء
                                    </div>
                                </div>
                            </div>

                            <!-- Description -->
                            <div class="mb-4">
                                <label for="description" class="form-label fw-bold">
                                    <i class="fas fa-align-left text-info me-2"></i>وصف المنتج
                                </label>
                                <textarea class="form-control @error('description') is-invalid @enderror" id="description" name="description"
                                    rows="4" placeholder="أدخل وصفاً مفصلاً للمنتج (اختياري)...">{{ old('description') }}</textarea>
                                @error('description')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">
                                    <i class="fas fa-info-circle text-info me-1"></i>
                                    وصف مفصل يساعد في فهم خصائص ومميزات المنتج
                                </div>
                            </div>

                            <!-- Product Image -->
                            <div class="mb-4">
                                <label for="image" class="form-label fw-bold">
                                    <i class="fas fa-image text-primary me-2"></i>صورة المنتج
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-camera text-muted"></i>
                                    </span>
                                    <input type="file" class="form-control @error('image') is-invalid @enderror"
                                        id="image" name="image" accept="image/*">
                                    @error('image')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="form-text">
                                    <i class="fas fa-info-circle text-info me-1"></i>
                                    اختر صورة واضحة للمنتج (JPG, PNG, GIF - حد أقصى 2MB)
                                </div>
                            </div>

                            <!-- Form Tips -->
                            <div class="alert alert-info border-0 bg-light">
                                <div class="d-flex align-items-start">
                                    <i class="fas fa-lightbulb text-info me-3 mt-1"></i>
                                    <div>
                                        <h6 class="alert-heading mb-2">نصائح لإنشاء منتج فعال:</h6>
                                        <ul class="mb-0 small">
                                            <li>استخدم أسماء واضحة ومفهومة للمنتجات</li>
                                            <li>اختر الفئة المناسبة لسهولة البحث والتصنيف</li>
                                            <li>أضف أسعار دقيقة لحساب الأرباح بشكل صحيح</li>
                                            <li>استخدم صور عالية الجودة لتحسين تجربة المستخدم</li>
                                            <li>أضف وصفاً مفصلاً يساعد في التمييز بين المنتجات</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>

                            <!-- Form Action Buttons -->
                            <div class="d-flex justify-content-between align-items-center pt-3 border-top">
                                <button type="reset" class="btn btn-outline-warning">
                                    <i class="fas fa-undo me-2"></i>إعادة تعيين
                                </button>
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-save me-2"></i>حفظ المنتج
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>

@push('styles')
    <style>
        .bg-gradient-primary {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        }

        .bg-gradient-success {
            background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
        }

        .product-image-preview {
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            overflow: hidden;
            transition: border-color 0.3s ease;
        }

        .product-image-preview:hover {
            border-color: #007bff;
        }

        .form-control:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        .form-select:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }
    </style>
@endpush

@push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Get form elements
            const nameInput = document.getElementById('name');
            const categorySelect = document.getElementById('category_id');
            const priceInput = document.getElementById('price');
            const sellingPriceInput = document.getElementById('selling_price');
            const descriptionInput = document.getElementById('description');
            const imageInput = document.getElementById('image');

            // Get preview elements
            const previewName = document.getElementById('preview-name');
            const previewCategory = document.getElementById('preview-category');
            const previewDescription = document.getElementById('preview-description');
            const previewNameText = document.getElementById('preview-name-text');
            const previewCategoryText = document.getElementById('preview-category-text');
            const previewPrice = document.getElementById('preview-price');
            const previewSellingPrice = document.getElementById('preview-selling-price');
            const previewImage = document.getElementById('preview-image');

            // Live preview functionality
            function updatePreview() {
                const name = nameInput.value.trim() || 'اسم المنتج';
                const categoryText = categorySelect.options[categorySelect.selectedIndex]?.text || 'الفئة';
                const description = descriptionInput.value.trim() || 'وصف المنتج';
                const price = priceInput.value || '0';
                const sellingPrice = sellingPriceInput.value || '0';

                // Update main preview
                previewName.textContent = name;
                previewCategory.textContent = categoryText;
                previewDescription.textContent = description.length > 50 ?
                    description.substring(0, 50) + '...' : description;

                // Update detail preview
                previewNameText.textContent = name === 'اسم المنتج' ? 'غير محدد' : name;
                previewCategoryText.textContent = categoryText === 'الفئة' ? 'غير محددة' : categoryText;
                previewPrice.textContent = price + ' ج.م';
                previewSellingPrice.textContent = sellingPrice + ' ج.م';
            }

            // Image preview functionality
            function updateImagePreview(file) {
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        previewImage.src = e.target.result;
                    }
                    reader.readAsDataURL(file);
                }
            }

            // Event listeners for live preview
            nameInput.addEventListener('input', updatePreview);
            categorySelect.addEventListener('change', updatePreview);
            priceInput.addEventListener('input', updatePreview);
            sellingPriceInput.addEventListener('input', updatePreview);
            descriptionInput.addEventListener('input', updatePreview);

            // Image upload preview
            imageInput.addEventListener('change', function(e) {
                const file = e.target.files[0];
                updateImagePreview(file);
            });

            // Form validation
            const form = document.getElementById('product-form');
            form.addEventListener('submit', function(e) {
                const name = nameInput.value.trim();
                const categoryId = categorySelect.value;

                if (!name) {
                    e.preventDefault();
                    nameInput.focus();
                    nameInput.classList.add('is-invalid');
                    showError(nameInput, 'اسم المنتج مطلوب');
                    return false;
                }

                if (!categoryId) {
                    e.preventDefault();
                    categorySelect.focus();
                    categorySelect.classList.add('is-invalid');
                    showError(categorySelect, 'يجب اختيار فئة للمنتج');
                    return false;
                }

                // Show loading state
                const submitBtn = form.querySelector('button[type="submit"]');
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الحفظ...';
                submitBtn.disabled = true;

                // Re-enable button after 5 seconds (in case of error)
                setTimeout(() => {
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                }, 5000);
            });

            // Helper function to show error messages
            function showError(element, message) {
                let errorDiv = element.parentNode.querySelector('.invalid-feedback');
                if (!errorDiv) {
                    errorDiv = document.createElement('div');
                    errorDiv.className = 'invalid-feedback';
                    element.parentNode.appendChild(errorDiv);
                }
                errorDiv.textContent = message;
            }

            // Remove validation errors on input
            [nameInput, categorySelect].forEach(element => {
                element.addEventListener('input', function() {
                    this.classList.remove('is-invalid');
                    const errorDiv = this.parentNode.querySelector('.invalid-feedback');
                    if (errorDiv) {
                        errorDiv.remove();
                    }
                });

                element.addEventListener('change', function() {
                    this.classList.remove('is-invalid');
                    const errorDiv = this.parentNode.querySelector('.invalid-feedback');
                    if (errorDiv) {
                        errorDiv.remove();
                    }
                });
            });

            // Reset form functionality
            const resetBtn = form.querySelector('button[type="reset"]');
            resetBtn.addEventListener('click', function() {
                setTimeout(() => {
                    updatePreview();
                    previewImage.src = "{{ asset('images/no-image.png') }}";
                }, 10);
            });

            // Auto-calculate selling price (optional feature)
            priceInput.addEventListener('input', function() {
                const costPrice = parseFloat(this.value) || 0;
                if (costPrice > 0 && !sellingPriceInput.value) {
                    // Suggest 30% markup
                    const suggestedPrice = (costPrice * 1.3).toFixed(2);
                    sellingPriceInput.placeholder = `مقترح: ${suggestedPrice}`;
                }
            });
        });
    </script>
@endpush
