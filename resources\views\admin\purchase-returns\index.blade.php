<x-app-layout>
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="d-sm-flex align-items-center justify-content-between mb-4">
            <div>
                <h1 class="h3 mb-0 text-gray-800">
                    <i class="fas fa-undo text-warning me-2"></i>
                    مرتجعات الشراء
                </h1>
                <p class="mb-0 text-muted">إدارة ومتابعة مرتجعات المشتريات</p>
            </div>
            <div class="d-flex gap-2">
                <a href="{{ user_route('purchase-returns.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>إنشاء مرتجع جديد
                </a>
                <a href="{{ user_route('purchases.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-2"></i>العودة إلى المشتريات
                </a>
            </div>
        </div>

        <!-- Purchase Returns Table -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-list me-2"></i>قائمة المرتجعات
                </h6>
            </div>
            <div class="card-body">
                <!-- Filters -->
                <form method="GET" action="{{ user_route('purchase-returns.index') }}" class="mb-4">
                    <div class="row">
                        <div class="col-md-3">
                            <label for="status" class="form-label">الحالة</label>
                            <select name="status" id="status" class="form-select">
                                <option value="">جميع الحالات</option>
                                <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>في
                                    الانتظار</option>
                                <option value="approved" {{ request('status') == 'approved' ? 'selected' : '' }}>
                                    معتمد</option>
                                <option value="completed" {{ request('status') == 'completed' ? 'selected' : '' }}>
                                    مكتمل</option>
                                <option value="cancelled" {{ request('status') == 'cancelled' ? 'selected' : '' }}>
                                    ملغي</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="supplier_id" class="form-label">المورد</label>
                            <select name="supplier_id" id="supplier_id" class="form-select">
                                <option value="">جميع الموردين</option>
                                @foreach ($suppliers as $supplier)
                                    <option value="{{ $supplier->id }}"
                                        {{ request('supplier_id') == $supplier->id ? 'selected' : '' }}>
                                        {{ $supplier->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="date_from" class="form-label">من تاريخ</label>
                            <input type="date" name="date_from" id="date_from" class="form-control"
                                value="{{ request('date_from') }}">
                        </div>
                        <div class="col-md-2">
                            <label for="date_to" class="form-label">إلى تاريخ</label>
                            <input type="date" name="date_to" id="date_to" class="form-control"
                                value="{{ request('date_to') }}">
                        </div>
                        <div class="col-md-2">
                            <label for="search" class="form-label">البحث</label>
                            <input type="text" name="search" id="search" class="form-control"
                                placeholder="رقم المرتجع أو رقم الشراء" value="{{ request('search') }}">
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <button type="submit" class="btn btn-secondary">
                                <i class="fas fa-search"></i> بحث
                            </button>
                            <a href="{{ route('admin.purchase-returns.index') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times"></i> إلغاء الفلتر
                            </a>
                        </div>
                    </div>
                </form>

                <!-- Returns Table -->
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>رقم المرتجع</th>
                                <th>رقم الشراء</th>
                                <th>المورد</th>
                                <th>تاريخ المرتجع</th>
                                <th>المبلغ الإجمالي</th>
                                <th>مبلغ الاسترداد</th>
                                <th>الحالة</th>
                                <th>نوع المرتجع</th>
                                <th>المستخدم</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($returns as $return)
                                <tr>
                                    <td>
                                        <a href="{{ route('admin.purchase-returns.show', $return) }}"
                                            class="text-decoration-none">
                                            {{ $return->return_number }}
                                        </a>
                                    </td>
                                    <td>
                                        <a href="{{ route('admin.purchases.show', $return->purchase) }}"
                                            class="text-decoration-none">
                                            {{ $return->purchase->invoice_number }}
                                        </a>
                                    </td>
                                    <td>{{ $return->supplier->name }}</td>
                                    <td>{{ $return->return_date->format('Y-m-d') }}</td>
                                    <td>{{ number_format($return->total_amount, 2) }} ج.م</td>
                                    <td>{{ number_format($return->refund_amount, 2) }} ج.م</td>
                                    <td>
                                        @switch($return->status)
                                            @case('pending')
                                                <span class="badge bg-warning">في الانتظار</span>
                                            @break

                                            @case('approved')
                                                <span class="badge bg-info">معتمد</span>
                                            @break

                                            @case('completed')
                                                <span class="badge bg-success">مكتمل</span>
                                            @break

                                            @case('cancelled')
                                                <span class="badge bg-danger">ملغي</span>
                                            @break
                                        @endswitch
                                    </td>
                                    <td>
                                        @if ($return->return_type == 'full')
                                            <span class="badge bg-primary">مرتجع كامل</span>
                                        @else
                                            <span class="badge bg-secondary">مرتجع جزئي</span>
                                        @endif
                                    </td>
                                    <td>{{ $return->user->name }}</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ user_route('purchase-returns.show', $return) }}"
                                                class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            @if ($return->canBeEdited())
                                                <a href="{{ user_route('purchase-returns.edit', $return) }}"
                                                    class="btn btn-sm btn-outline-warning">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                            @endif
                                            @if ($return->canBeApproved())
                                                <form method="POST"
                                                    action="{{ user_route('purchase-returns.approve', $return) }}"
                                                    class="d-inline">
                                                    @csrf
                                                    <button type="submit" class="btn btn-sm btn-outline-info"
                                                        onclick="return confirm('هل أنت متأكد من اعتماد هذا المرتجع؟')">
                                                        <i class="fas fa-check"></i>
                                                    </button>
                                                </form>
                                            @endif
                                            @if ($return->canBeCompleted())
                                                <form method="POST"
                                                    action="{{ user_route('purchase-returns.complete', $return) }}"
                                                    class="d-inline">
                                                    @csrf
                                                    <button type="submit" class="btn btn-sm btn-outline-success"
                                                        onclick="return confirm('هل أنت متأكد من إتمام هذا المرتجع؟ سيتم تعديل المخزون والحسابات.')">
                                                        <i class="fas fa-check-double"></i>
                                                    </button>
                                                </form>
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                                @empty
                                    <tr>
                                        <td colspan="10" class="text-center">لا توجد مرتجعات</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-center">
                        {{ $returns->appends(request()->query())->links() }}
                    </div>
                </div>
            </div>
        </div>
    </x-app-layout>
