<?php

namespace App\Http\Controllers;

use App\Models\Branch;
use App\Models\Product;
use App\Models\Purchase;
use App\Models\Supplier;
use App\Models\Account;
use App\Models\AccountTransaction;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use App\Helpers\AlertHelper;
use App\Models\BranchInventory;

class PurchaseController extends Controller
{
    public function index(Request $request)
    {
        $query = Purchase::with(['supplier', 'items.product', 'branch'])
            ->when($request->filled('supplier_id'), function ($query) use ($request) {
                $query->where('supplier_id', $request->supplier_id);
            })
            ->when($request->filled('branch_id'), function ($query) use ($request) {
                $query->where('branch_id', $request->branch_id);
            })
            ->when($request->filled('status'), function ($query) use ($request) {
                $query->where('status', $request->status);
            })
            ->when($request->filled('date_from'), function ($query) use ($request) {
                $query->whereDate('purchase_date', '>=', $request->date_from);
            })
            ->when($request->filled('date_to'), function ($query) use ($request) {
                $query->whereDate('purchase_date', '<=', $request->date_to);
            });

        $purchases = $query->latest()->paginate(10);
        $suppliers = Supplier::where('is_active', true)->get();
        $branches = Branch::where('is_active', true)->get();

        return view('purchases.index', compact('purchases', 'suppliers', 'branches'));
    }

    public function create()
    {
        $products = Product::where('is_active', true)->get();
        $branches = Branch::where('is_active', true)->get();
        $suppliers = Supplier::where('is_active', true)->get();
        return view('purchases.create', compact('products', 'branches', 'suppliers'));
    }

    public function store(Request $request)
    {
        // dd($request->all());
        $validated = $request->validate([
            'supplier_id' => 'required|exists:suppliers,id',
            'total_amount' => 'required|numeric|min:0',
            'discount_type' => 'required|in:amount,percentage',
            'discount_amount' => 'nullable|numeric|min:0',
            'paid_amount' => 'required|numeric|min:0',
            'notes' => 'nullable|string',
            'products' => 'required|array|min:1',
            'products.*.product_id' => 'required|exists:products,id',
            'products.*.quantity' => 'required|integer|min:1',
            'products.*.purchase_price' => 'required|numeric|min:0',
        ]);

        // Validate percentage discount
        if ($validated['discount_type'] === 'percentage' && $validated['discount_amount'] > 100) {
            return back()->withErrors(['discount_amount' => 'نسبة الخصم لا يمكن أن تزيد عن 100%'])->withInput();
        }

        try {
            DB::beginTransaction();

            // Calculate actual discount amount
            $discountValue = $validated['discount_amount'] ?? 0; // Original value (percentage or amount)
            $discountAmount = $discountValue; // Actual discount amount
            if ($validated['discount_type'] === 'percentage') {
                $discountAmount = ($validated['total_amount'] * $discountValue) / 100;
            }

            // Create purchase (without inventory updates)
            $purchase = Purchase::create([
                'supplier_id' => $validated['supplier_id'],
                'total_amount' => $validated['total_amount'],
                'discount_type' => $validated['discount_type'],
                'discount_amount' => $discountAmount,
                'discount_value' => $discountValue,
                'paid_amount' => $validated['paid_amount'],
                'remaining_amount' => $validated['total_amount'] - $discountAmount - $validated['paid_amount'],
                'notes' => $validated['notes'],
                'status' => 'pending_distribution', // Changed status
                'purchase_date' => now(),
                'is_distributed' => false // New field
            ]);

            // Create purchase items (NO inventory updates yet)
            foreach ($validated['products'] as $product) {
                // Create purchase item only
                $purchase->items()->create([
                    'product_id' => $product['product_id'],
                    'quantity' => $product['quantity'],
                    'cost_price' => $product['purchase_price'],
                    'total_price' => $product['quantity'] * $product['purchase_price'],
                    'is_distributed' => false // New field
                ]);
            }

            // Get supplier account
            $supplier = Supplier::findOrFail($validated['supplier_id']);
            $supplierAccount = $supplier->account;

            if ($supplierAccount) {
                // Create transaction for paid amount
                if ($validated['paid_amount'] > 0) {
                    AccountTransaction::create([
                        'account_id' => $supplierAccount->id,
                        'amount' => $validated['paid_amount'],
                        'description' => 'دفعة مقابل عملية شراء رقم ' . $purchase->invoice_number,
                        'transactionable_type' => Purchase::class,
                        'transactionable_id' => $purchase->id,
                        'user_id' => auth()->id()
                    ]);
                }

                // Create transaction for remaining amount
                if ($purchase->total_amount - $validated['paid_amount'] > 0) {
                    AccountTransaction::create([
                        'account_id' => $supplierAccount->id,
                        'type' => 'credit',
                        'amount' => -1 * ($purchase->total_amount - $validated['paid_amount']),
                        'description' => 'مبلغ متبقي مقابل عملية شراء رقم ' . $purchase->invoice_number,
                        'transactionable_type' => Purchase::class,
                        'transactionable_id' => $purchase->id,
                        'user_id' => auth()->id()
                    ]);
                }
            }

            DB::commit();
            AlertHelper::success('تم إنشاء عملية الشراء بنجاح. يرجى توزيع المنتجات الآن.');
            return redirect()->route('admin.purchases.distribute', $purchase);
        } catch (\Exception $e) {
            DB::rollBack();
            AlertHelper::error('حدث خطأ أثناء إنشاء عملية الشراء: ' . $e->getMessage());
            return back()->withInput();
        }
    }

    public function show(Purchase $purchase)
    {
        $purchase->load(['supplier', 'items.product', 'items.branch', 'transactions']);
        return view('purchases.show', compact('purchase'));
    }

    public function edit(Purchase $purchase)
    {
        if ($purchase->status !== 'pending') {
            return redirect()->route('admin.purchases.show', $purchase)
                ->with('error', 'Only pending purchases can be edited.');
        }

        $suppliers = Supplier::where('is_active', true)->get();
        $branches = Branch::where('is_active', true)->get();
        $products = Product::where('is_active', true)->get();
        $purchase->load('products');

        return view('purchases.edit', compact('purchase', 'suppliers', 'branches', 'products'));
    }

    public function update(Request $request, Purchase $purchase)
    {
        if ($purchase->status !== 'pending') {
            return redirect()->route('admin.purchases.show', $purchase)
                ->with('error', 'Only pending purchases can be edited.');
        }

        $validated = $request->validate([
            'supplier_id' => ['required', 'exists:suppliers,id'],
            'branch_id' => ['required', 'exists:branches,id'],
            'purchase_date' => ['required', 'date'],
            'products' => ['required', 'array'],
            'products.*.id' => ['required', 'exists:products,id'],
            'products.*.quantity' => ['required', 'numeric', 'min:1'],
            'products.*.price' => ['required', 'numeric', 'min:0'],
        ]);

        $purchase->update([
            'supplier_id' => $validated['supplier_id'],
            'branch_id' => $validated['branch_id'],
            'purchase_date' => $validated['purchase_date'],
        ]);

        $purchase->products()->detach();
        $total = 0;
        foreach ($validated['products'] as $item) {
            $purchase->products()->attach($item['id'], [
                'quantity' => $item['quantity'],
                'price' => $item['price'],
            ]);
            $total += $item['quantity'] * $item['price'];
        }

        $purchase->update(['total' => $total]);

        return redirect()->route('admin.purchases.show', $purchase)
            ->with('success', 'Purchase updated successfully.');
    }

    public function complete(Purchase $purchase)
    {
        if ($purchase->status !== 'pending') {
            return redirect()->route('admin.purchases.show', $purchase)
                ->with('error', 'Only pending purchases can be completed.');
        }

        // Update product stock
        foreach ($purchase->products as $product) {
            $product->increment('stock', $product->pivot->quantity);
        }

        $purchase->update(['status' => 'completed']);

        return redirect()->route('admin.purchases.show', $purchase)
            ->with('success', 'Purchase completed successfully.');
    }

    public function cancel(Purchase $purchase)
    {
        if ($purchase->status !== 'pending') {
            return redirect()->route('admin.purchases.show', $purchase)
                ->with('error', 'Only pending purchases can be cancelled.');
        }

        $purchase->update(['status' => 'cancelled']);

        return redirect()->route('admin.purchases.show', $purchase)
            ->with('success', 'Purchase cancelled successfully.');
    }

    public function print(Purchase $purchase)
    {
        $purchase->load(['supplier', 'items.product', 'branch']);
        return view('purchases.print', compact('purchase'));
    }
}
