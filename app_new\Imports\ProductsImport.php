<?php

namespace App\Imports;

use App\Models\Product;
use App\Models\Category;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\SkipsEmptyRows;
use Illuminate\Support\Facades\Log;

class ProductsImport implements
    ToModel,
    WithHeadingRow,
    WithValidation,
    WithBatchInserts,
    WithChunkReading,
    SkipsEmptyRows
{
    protected $errors = [];
    protected $successCount = 0;
    protected $categoryCache = [];

    public function model(array $row)
    {
        try {
            // Skip empty rows
            if (empty($row['name'])) {
                return null;
            }

            // Default category if not provided
            $categoryName = $row['category'] ?? 'عام';

            // Get or create category
            $category = $this->getOrCreateCategory($categoryName);
            if (!$category) {
                $this->errors[] = "لا يمكن إنشاء الفئة: {$categoryName}";
                return null;
            }

            // Validate required fields
            if (strlen(trim($row['name'])) < 2) {
                $this->errors[] = "اسم المنتج قصير جداً: {$row['name']}";
                return null;
            }

            // Create product
            $product = new Product([
                'name' => trim($row['name']),
                'description' => !empty($row['description']) ? trim($row['description']) : null,
                'price' => $this->parsePrice($row['price'] ?? 0),
                'selling_price' => $this->parsePrice($row['selling_price'] ?? 0),
                'category_id' => $category->id,
                'is_active' => $this->parseBoolean($row['is_active'] ?? true),
            ]);

            $this->successCount++;
            return $product;
        } catch (\Exception $e) {
            $this->errors[] = "خطأ في المنتج '{$row['name']}': " . $e->getMessage();
            Log::error('Product import error: ' . $e->getMessage(), ['row' => $row]);
            return null;
        }
    }

    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'category' => 'required|string|max:255',
            'description' => 'nullable|string',
            'price' => 'nullable|numeric|min:0',
            'selling_price' => 'nullable|numeric|min:0',
            'is_active' => 'nullable|boolean',
        ];
    }

    public function customValidationMessages()
    {
        return [
            'name.required' => 'Product name is required',
            'category.required' => 'Category is required',
            'price.numeric' => 'Price must be a number',
            'selling_price.numeric' => 'Selling price must be a number',
        ];
    }

    public function batchSize(): int
    {
        return 100; // Process 100 rows at a time
    }

    public function chunkSize(): int
    {
        return 100; // Read 100 rows at a time
    }

    protected function getOrCreateCategory($categoryName)
    {
        // Use cache to avoid repeated database queries
        if (isset($this->categoryCache[$categoryName])) {
            return $this->categoryCache[$categoryName];
        }

        $category = Category::firstOrCreate(
            ['name' => $categoryName],
            ['description' => "Auto-created during import"]
        );

        $this->categoryCache[$categoryName] = $category;
        return $category;
    }

    protected function parsePrice($value)
    {
        if (is_null($value) || $value === '') {
            return 0;
        }

        // Remove any currency symbols or commas
        $cleaned = preg_replace('/[^\d.]/', '', $value);
        return floatval($cleaned);
    }

    protected function parseBoolean($value)
    {
        if (is_bool($value)) {
            return $value;
        }

        $value = strtolower(trim($value));
        return in_array($value, ['true', '1', 'yes', 'active', 'نشط']);
    }

    public function getErrors()
    {
        return $this->errors;
    }

    public function getSuccessCount()
    {
        return $this->successCount;
    }
}
