<x-app-layout>
    <x-slot name="header">
        <div class="d-flex justify-content-between align-items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('تقرير الأرباح') }}
            </h2>
            <div>
                <button type="button" class="btn btn-success" onclick="printReport()">
                    <i class="fas fa-print"></i> {{ __('طباعة') }}
                </button>
                <button type="button" class="btn btn-primary" onclick="exportToExcel()">
                    <i class="fas fa-file-excel"></i> {{ __('تصدير إلى Excel') }}
                </button>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="container-fluid">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    <!-- Filters -->
                    <form action="{{ route('reports.profit') }}" method="GET" class="mb-4">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="branch">{{ __('الفرع') }}</label>
                                    <select name="branch" id="branch" class="form-select">
                                        <option value="">{{ __('الكل') }}</option>
                                        @foreach($branches as $branch)
                                            <option value="{{ $branch->id }}" {{ request('branch') == $branch->id ? 'selected' : '' }}>
                                                {{ $branch->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="date_range">{{ __('الفترة الزمنية') }}</label>
                                    <input type="text" name="date_range" id="date_range" class="form-control" value="{{ request('date_range') }}" placeholder="{{ __('اختر الفترة الزمنية') }}">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="group_by">{{ __('تجميع حسب') }}</label>
                                    <select name="group_by" id="group_by" class="form-select">
                                        <option value="daily" {{ request('group_by') == 'daily' ? 'selected' : '' }}>{{ __('يومي') }}</option>
                                        <option value="weekly" {{ request('group_by') == 'weekly' ? 'selected' : '' }}>{{ __('أسبوعي') }}</option>
                                        <option value="monthly" {{ request('group_by') == 'monthly' ? 'selected' : '' }}>{{ __('شهري') }}</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="sort">{{ __('ترتيب حسب') }}</label>
                                    <select name="sort" id="sort" class="form-select">
                                        <option value="date_asc" {{ request('sort') == 'date_asc' ? 'selected' : '' }}>{{ __('التاريخ (تصاعدي)') }}</option>
                                        <option value="date_desc" {{ request('sort') == 'date_desc' ? 'selected' : '' }}>{{ __('التاريخ (تنازلي)') }}</option>
                                        <option value="profit_asc" {{ request('sort') == 'profit_asc' ? 'selected' : '' }}>{{ __('الربح (تصاعدي)') }}</option>
                                        <option value="profit_desc" {{ request('sort') == 'profit_desc' ? 'selected' : '' }}>{{ __('الربح (تنازلي)') }}</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12 text-start">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i> {{ __('عرض التقرير') }}
                                </button>
                                <a href="{{ route('reports.profit') }}" class="btn btn-secondary">
                                    <i class="fas fa-redo"></i> {{ __('إعادة تعيين') }}
                                </a>
                            </div>
                        </div>
                    </form>

                    <!-- Summary Cards -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <h5 class="card-title">{{ __('إجمالي المبيعات') }}</h5>
                                    <h3 class="mb-0">{{ number_format($summary['total_sales'], 2) }}</h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <h5 class="card-title">{{ __('تكلفة البضاعة') }}</h5>
                                    <h3 class="mb-0">{{ number_format($summary['total_cost'], 2) }}</h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <h5 class="card-title">{{ __('إجمالي الأرباح') }}</h5>
                                    <h3 class="mb-0">{{ number_format($summary['total_profit'], 2) }}</h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <h5 class="card-title">{{ __('نسبة الربح') }}</h5>
                                    <h3 class="mb-0">{{ number_format($summary['profit_percentage'], 2) }}%</h3>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Profit Table -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead>
                                <tr>
                                    <th>{{ __('الفترة') }}</th>
                                    <th>{{ __('عدد المبيعات') }}</th>
                                    <th>{{ __('إجمالي المبيعات') }}</th>
                                    <th>{{ __('تكلفة البضاعة') }}</th>
                                    <th>{{ __('المصروفات') }}</th>
                                    <th>{{ __('صافي الربح') }}</th>
                                    <th>{{ __('نسبة الربح') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($profits as $profit)
                                    <tr>
                                        <td>{{ $profit->period }}</td>
                                        <td>{{ $profit->sales_count }}</td>
                                        <td>{{ number_format($profit->total_sales, 2) }}</td>
                                        <td>{{ number_format($profit->total_cost, 2) }}</td>
                                        <td>{{ number_format($profit->total_expenses, 2) }}</td>
                                        <td>{{ number_format($profit->net_profit, 2) }}</td>
                                        <td>{{ number_format($profit->profit_percentage, 2) }}%</td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="7" class="text-center">{{ __('لا توجد بيانات') }}</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="mt-4">
                        {{ $profits->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>

    @push('styles')
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css" />
    <style>
        .rtl {
            direction: rtl;
            text-align: right;
        }
        .rtl .form-select {
            text-align: right;
        }
        .rtl .table th,
        .rtl .table td {
            text-align: right;
        }
        .rtl .text-start {
            text-align: right !important;
        }
        .rtl .text-end {
            text-align: left !important;
        }
        .rtl .form-control {
            text-align: right;
        }
        .rtl .form-control::placeholder {
            text-align: right;
        }
        .rtl .card-title {
            text-align: right;
        }
        @media print {
            .no-print {
                display: none !important;
            }
            .container-fluid {
                width: 100% !important;
                padding: 0 !important;
                margin: 0 !important;
            }
        }
    </style>
    @endpush

    @push('scripts')
    <script type="text/javascript" src="https://cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
    <script>
        $(document).ready(function() {
            $('#date_range').daterangepicker({
                locale: {
                    format: 'YYYY-MM-DD',
                    separator: ' إلى ',
                    applyLabel: 'تطبيق',
                    cancelLabel: 'إلغاء',
                    fromLabel: 'من',
                    toLabel: 'إلى',
                    customRangeLabel: 'مخصص',
                    weekLabel: 'أ',
                    daysOfWeek: ['ح', 'ن', 'ث', 'ر', 'خ', 'ج', 'س'],
                    monthNames: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
                    firstDay: 6
                }
            });
        });

        function printReport() {
            window.print();
        }

        function exportToExcel() {
            // Add Excel export functionality here
            alert('سيتم إضافة وظيفة التصدير إلى Excel قريباً');
        }
    </script>
    @endpush
</x-app-layout>
