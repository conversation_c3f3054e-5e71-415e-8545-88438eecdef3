<x-app-layout>
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-0 text-gray-800">
                    <i class="fas fa-exchange-alt text-primary me-2"></i>
                    إدارة نقل المخزون
                </h1>
                <p class="text-muted mb-0">إدارة وتتبع عمليات نقل المنتجات بين المخازن والفروع</p>
            </div>
            <div>
                <a href="{{ user_route('inventory-transfers.create') }}" class="btn btn-primary shadow-sm">
                    <i class="fas fa-plus me-2"></i>
                    طلب نقل جديد
                </a>
            </div>
        </div>

        <!-- Filters -->
        <div class="card shadow mb-4">
            <div class="card-body">
                <form method="GET" action="{{ user_route('inventory-transfers.index') }}">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <label for="status" class="form-label">الحالة</label>
                            <select name="status" id="status" class="form-select">
                                <option value="">جميع الحالات</option>
                                <option value="pending" {{ request('status') === 'pending' ? 'selected' : '' }}>في الانتظار</option>
                                <option value="approved" {{ request('status') === 'approved' ? 'selected' : '' }}>موافق عليه</option>
                                <option value="in_transit" {{ request('status') === 'in_transit' ? 'selected' : '' }}>في الطريق</option>
                                <option value="completed" {{ request('status') === 'completed' ? 'selected' : '' }}>مكتمل</option>
                                <option value="cancelled" {{ request('status') === 'cancelled' ? 'selected' : '' }}>ملغي</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="type" class="form-label">نوع النقل</label>
                            <select name="type" id="type" class="form-select">
                                <option value="">جميع الأنواع</option>
                                <option value="store_to_branch" {{ request('type') === 'store_to_branch' ? 'selected' : '' }}>من مخزن إلى فرع</option>
                                <option value="branch_to_store" {{ request('type') === 'branch_to_store' ? 'selected' : '' }}>من فرع إلى مخزن</option>
                                <option value="store_to_store" {{ request('type') === 'store_to_store' ? 'selected' : '' }}>من مخزن إلى مخزن</option>
                                <option value="branch_to_branch" {{ request('type') === 'branch_to_branch' ? 'selected' : '' }}>من فرع إلى فرع</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="search" class="form-label">البحث</label>
                            <input type="text" name="search" id="search" class="form-control" 
                                   placeholder="رقم النقل أو الملاحظات..." value="{{ request('search') }}">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-2"></i>بحث
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Transfers Table -->
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">عمليات النقل</h6>
            </div>
            <div class="card-body">
                @if($transfers->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th class="border-0 fw-bold">رقم النقل</th>
                                    <th class="border-0 fw-bold">النوع</th>
                                    <th class="border-0 fw-bold">من</th>
                                    <th class="border-0 fw-bold">إلى</th>
                                    <th class="border-0 fw-bold">عدد المنتجات</th>
                                    <th class="border-0 fw-bold">الحالة</th>
                                    <th class="border-0 fw-bold">تاريخ الطلب</th>
                                    <th class="border-0 fw-bold text-center">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($transfers as $transfer)
                                    <tr>
                                        <td>
                                            <a href="{{ user_route('inventory-transfers.show', $transfer) }}" 
                                               class="fw-bold text-primary text-decoration-none">
                                                {{ $transfer->transfer_number }}
                                            </a>
                                        </td>
                                        <td>
                                            @php
                                                $typeLabels = [
                                                    'store_to_branch' => 'مخزن → فرع',
                                                    'branch_to_store' => 'فرع → مخزن',
                                                    'store_to_store' => 'مخزن → مخزن',
                                                    'branch_to_branch' => 'فرع → فرع'
                                                ];
                                            @endphp
                                            <span class="badge bg-info">{{ $typeLabels[$transfer->type] ?? $transfer->type }}</span>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-circle bg-secondary text-white me-2">
                                                    {{ substr($transfer->source->name ?? 'غير محدد', 0, 1) }}
                                                </div>
                                                <div>
                                                    <div class="fw-bold">{{ $transfer->source->name ?? 'غير محدد' }}</div>
                                                    @if($transfer->source_type === 'store' && $transfer->source && !$transfer->source->isIndependent())
                                                        <small class="text-muted">({{ $transfer->source->branch->name ?? 'فرع غير محدد' }})</small>
                                                    @elseif($transfer->source_type === 'store' && $transfer->source && $transfer->source->isIndependent())
                                                        <small class="text-muted">(مستقل)</small>
                                                    @endif
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-circle bg-success text-white me-2">
                                                    {{ substr($transfer->destination->name ?? 'غير محدد', 0, 1) }}
                                                </div>
                                                <div>
                                                    <div class="fw-bold">{{ $transfer->destination->name ?? 'غير محدد' }}</div>
                                                    @if($transfer->destination_type === 'store' && $transfer->destination && !$transfer->destination->isIndependent())
                                                        <small class="text-muted">({{ $transfer->destination->branch->name ?? 'فرع غير محدد' }})</small>
                                                    @elseif($transfer->destination_type === 'store' && $transfer->destination && $transfer->destination->isIndependent())
                                                        <small class="text-muted">(مستقل)</small>
                                                    @endif
                                                </div>
                                            </div>
                                        </td>
                                        <td>{{ $transfer->items_count ?? $transfer->items->count() }}</td>
                                        <td>
                                            @php
                                                $statusColors = [
                                                    'pending' => 'warning',
                                                    'approved' => 'info',
                                                    'in_transit' => 'secondary',
                                                    'completed' => 'success',
                                                    'cancelled' => 'danger'
                                                ];
                                                $statusLabels = [
                                                    'pending' => 'في الانتظار',
                                                    'approved' => 'موافق عليه',
                                                    'in_transit' => 'في الطريق',
                                                    'completed' => 'مكتمل',
                                                    'cancelled' => 'ملغي'
                                                ];
                                            @endphp
                                            <span class="badge bg-{{ $statusColors[$transfer->status] ?? 'secondary' }}">
                                                {{ $statusLabels[$transfer->status] ?? $transfer->status }}
                                            </span>
                                        </td>
                                        <td>{{ $transfer->requested_at ? $transfer->requested_at->format('Y-m-d H:i') : $transfer->created_at->format('Y-m-d H:i') }}</td>
                                        <td class="text-center">
                                            <div class="btn-group" role="group">
                                                <a href="{{ user_route('inventory-transfers.show', $transfer) }}" 
                                                   class="btn btn-sm btn-outline-primary" title="عرض التفاصيل">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                @if($transfer->canBeApproved() && auth()->user()->isAdmin())
                                                    <button type="button" class="btn btn-sm btn-outline-success" 
                                                            onclick="approveTransfer({{ $transfer->id }})" title="موافقة">
                                                        <i class="fas fa-check"></i>
                                                    </button>
                                                @endif
                                                @if($transfer->canBeCancelled())
                                                    <button type="button" class="btn btn-sm btn-outline-danger" 
                                                            onclick="cancelTransfer({{ $transfer->id }})" title="إلغاء">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-center mt-4">
                        {{ $transfers->appends(request()->query())->links() }}
                    </div>
                @else
                    <div class="text-center py-5">
                        <i class="fas fa-exchange-alt fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد عمليات نقل</h5>
                        <p class="text-muted">لم يتم العثور على عمليات نقل مطابقة للمعايير المحددة</p>
                        <a href="{{ user_route('inventory-transfers.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>إنشاء طلب نقل جديد
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <style>
        .avatar-circle {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 12px;
        }

        .table-hover tbody tr:hover {
            background-color: rgba(0, 0, 0, 0.02);
        }
    </style>

    <script>
        function approveTransfer(transferId) {
            if (confirm('هل أنت متأكد من الموافقة على هذا الطلب؟')) {
                window.location.href = `{{ user_route('inventory-transfers.index') }}/${transferId}`;
            }
        }

        function cancelTransfer(transferId) {
            if (confirm('هل أنت متأكد من إلغاء هذا الطلب؟')) {
                // Handle cancel logic
                fetch(`{{ user_route('inventory-transfers.index') }}/${transferId}/cancel`, {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                        'Content-Type': 'application/json',
                    },
                }).then(response => {
                    if (response.ok) {
                        location.reload();
                    }
                });
            }
        }
    </script>
</x-app-layout>
