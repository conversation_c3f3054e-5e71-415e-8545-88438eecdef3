<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\Category;
use App\Models\BranchInventory;
use App\Models\StoreInventory;
use App\Helpers\AlertHelper;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class ProductController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Product::with(['category', 'branchInventories', 'storeInventories']);

        // Search functionality
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                    ->orWhere('description', 'like', "%{$search}%")
                    ->orWhere('sku', 'like', "%{$search}%");
            });
        }

        // Category filter
        if ($request->has('category_id') && $request->category_id) {
            $query->where('category_id', $request->category_id);
        }

        // Status filter
        if ($request->has('status') && $request->status) {
            $query->where('is_active', $request->status === 'active');
        }

        // Stock status filter
        if ($request->has('stock_status') && $request->stock_status) {
            switch ($request->stock_status) {
                case 'in_stock':
                    $query->whereHas('branchInventories', function ($q) {
                        $q->where('quantity', '>', 10);
                    })->orWhereHas('storeInventories', function ($q) {
                        $q->where('quantity', '>', 10);
                    });
                    break;
                case 'low_stock':
                    $query->whereHas('branchInventories', function ($q) {
                        $q->where('quantity', '>', 0)->where('quantity', '<=', 10);
                    })->orWhereHas('storeInventories', function ($q) {
                        $q->where('quantity', '>', 0)->where('quantity', '<=', 10);
                    });
                    break;
                case 'out_of_stock':
                    $query->whereDoesntHave('branchInventories', function ($q) {
                        $q->where('quantity', '>', 0);
                    })->whereDoesntHave('storeInventories', function ($q) {
                        $q->where('quantity', '>', 0);
                    });
                    break;
            }
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'name');
        switch ($sortBy) {
            case 'created_at':
                $query->orderBy('created_at', 'desc');
                break;
            case 'category':
                $query->join('categories', 'products.category_id', '=', 'categories.id')
                    ->orderBy('categories.name');
                break;
            case 'stock':
                // This is complex, we'll handle it after getting the results
                break;
            default:
                $query->orderBy('created_at', 'desc');
        }

        $products = $query->paginate(20);
        $categories = Category::all();

        // Calculate statistics
        $stats = [
            'total_products' => Product::count(),
            'active_products' => Product::where('is_active', true)->count(),
            'low_stock' => Product::whereHas('branchInventories', function ($q) {
                $q->where('quantity', '>', 0)->where('quantity', '<=', 10);
            })->orWhereHas('storeInventories', function ($q) {
                $q->where('quantity', '>', 0)->where('quantity', '<=', 10);
            })->count(),
            'inventory_value' => $this->calculateInventoryValue(),
        ];

        return view('products.index', compact('products', 'categories', 'stats'));
    }

    /**
     * Calculate total inventory value
     */
    private function calculateInventoryValue()
    {
        $branchValue = DB::table('branch_inventory')
            ->join('products', 'branch_inventory.product_id', '=', 'products.id')
            ->selectRaw('SUM(branch_inventory.quantity * COALESCE(products.price, 0)) as total')
            ->value('total') ?? 0;

        $storeValue = DB::table('store_inventory')
            ->join('products', 'store_inventory.product_id', '=', 'products.id')
            ->selectRaw('SUM(store_inventory.quantity * COALESCE(products.price, 0)) as total')
            ->value('total') ?? 0;

        return $branchValue + $storeValue;
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $categories = Category::all();
        return view('products.create', compact('categories'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'sku' => 'nullable|string|max:255|unique:products',
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'price' => 'nullable|numeric|min:0',
            'selling_price' => 'nullable|numeric|min:0',
            'category_id' => 'required|exists:categories,id',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_active' => 'boolean'
        ]);

        if ($request->hasFile('image')) {
            $image = $request->file('image');
            $filename = Str::slug($request->name) . '-' . time() . '.' . $image->getClientOriginalExtension();
            $path = $image->storeAs('products', $filename, 'public');
            $validated['image_path'] = $path;
        }

        Product::create($validated);
        AlertHelper::success('تم إضافة المنتج بنجاح');

        // Redirect to appropriate products index based on user role
        if (auth()->user()->hasRole('admin')) {
            return redirect()->route('admin.products.index');
        } else {
            return redirect()->route('seller.products.index');
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Product $product)
    {
        // Load all necessary relationships for comprehensive product details
        $product->load([
            'category',
            'branchInventories.branch',
            'storeInventories.store.branch',
            'saleItems' => function ($query) {
                $query->with(['sale.customer', 'sale.branch'])->latest()->limit(20);
            }
        ]);

        // Get transfer history for this product
        $transferHistory = \App\Models\InventoryTransferItem::with([
            'inventoryTransfer.source',
            'inventoryTransfer.destination',
            'inventoryTransfer.requestedBy',
            'inventoryTransfer.approvedBy'
        ])
            ->where('product_id', $product->id)
            ->whereHas('inventoryTransfer', function ($query) {
                $query->orderBy('created_at', 'desc');
            })
            ->limit(15)
            ->get();

        // Get purchase history for this product
        $purchaseHistory = \App\Models\Purchase::with(['supplier', 'branch'])
            ->whereHas('items', function ($query) use ($product) {
                $query->where('product_id', $product->id);
            })
            ->latest()
            ->limit(15)
            ->get();

        // Add purchase item data to each purchase
        foreach ($purchaseHistory as $purchase) {
            $purchaseItem = $purchase->items()->where('product_id', $product->id)->first();
            if ($purchaseItem) {
                $purchase->purchase_item = $purchaseItem;
            }
        }

        return view('products.show', compact('product', 'transferHistory', 'purchaseHistory'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Product $product)
    {
        $categories = Category::all();
        return view('products.edit', compact('product', 'categories'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Product $product)
    {
        $validated = $request->validate([
            'sku' => 'nullable|string|max:255|unique:products,sku,' . $product->id,
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'price' => 'nullable|numeric|min:0',
            'selling_price' => 'nullable|numeric|min:0',
            'category_id' => 'required|exists:categories,id',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_active' => 'boolean'
        ]);

        if ($request->hasFile('image')) {
            // Delete old image
            if ($product->image_path) {
                Storage::disk('public')->delete($product->image_path);
            }

            $image = $request->file('image');
            $filename = Str::slug($request->name) . '-' . time() . '.' . $image->getClientOriginalExtension();
            $path = $image->storeAs('products', $filename, 'public');
            $validated['image_path'] = $path;
        }

        $product->update($validated);
        AlertHelper::success('تم تحديث المنتج بنجاح');

        // Redirect to appropriate products index based on user role
        if (auth()->user()->hasRole('admin')) {
            return redirect()->route('admin.products.index');
        } else {
            return redirect()->route('seller.products.index');
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Product $product)
    {
        // Delete product image
        if ($product->image_path) {
            Storage::disk('public')->delete($product->image_path);
        }

        $product->delete();
        AlertHelper::success('تم حذف المنتج بنجاح');

        // Redirect to appropriate products index based on user role
        if (auth()->user()->hasRole('admin')) {
            return redirect()->route('admin.products.index');
        } else {
            return redirect()->route('seller.products.index');
        }
    }

    /**
     * Toggle product status
     */
    public function toggleStatus(Product $product)
    {
        $product->is_active = !$product->is_active;
        $product->save();

        return response()->json([
            'success' => true,
            'message' => 'تم تغيير حالة المنتج بنجاح',
            'status' => $product->is_active
        ]);
    }

    /**
     * Bulk activate products
     */
    public function bulkActivate(Request $request)
    {
        $request->validate([
            'product_ids' => 'required|array',
            'product_ids.*' => 'exists:products,id'
        ]);

        Product::whereIn('id', $request->product_ids)->update(['is_active' => true]);

        return response()->json([
            'success' => true,
            'message' => 'تم تفعيل المنتجات بنجاح'
        ]);
    }

    /**
     * Bulk deactivate products
     */
    public function bulkDeactivate(Request $request)
    {
        $request->validate([
            'product_ids' => 'required|array',
            'product_ids.*' => 'exists:products,id'
        ]);

        Product::whereIn('id', $request->product_ids)->update(['is_active' => false]);

        return response()->json([
            'success' => true,
            'message' => 'تم إلغاء تفعيل المنتجات بنجاح'
        ]);
    }

    /**
     * Bulk delete products
     */
    public function bulkDelete(Request $request)
    {
        $request->validate([
            'product_ids' => 'required|array',
            'product_ids.*' => 'exists:products,id'
        ]);

        Product::whereIn('id', $request->product_ids)->delete();

        return response()->json([
            'success' => true,
            'message' => 'تم حذف المنتجات بنجاح'
        ]);
    }

    /**
     * Show bulk create form
     */
    public function bulkCreate()
    {
        $categories = Category::all();
        return view('products.bulk-create', compact('categories'));
    }

    /**
     * Store bulk created products
     */
    public function bulkStore(Request $request)
    {
        $request->validate([
            'category_id' => 'required|exists:categories,id',
            'products' => 'required|array|min:1',
            'products.*.name' => 'required|string|max:255',
            'products.*.description' => 'nullable|string',
            'products.*.price' => 'nullable|numeric|min:0',
            'products.*.selling_price' => 'nullable|numeric|min:0',
        ]);

        $createdCount = 0;
        $errors = [];

        foreach ($request->products as $index => $productData) {
            try {
                // Skip empty rows
                if (empty($productData['name'])) {
                    continue;
                }

                Product::create([
                    'name' => $productData['name'],
                    'description' => $productData['description'] ?? null,
                    'price' => $productData['price'] ?? 0,
                    'selling_price' => $productData['selling_price'] ?? 0,
                    'category_id' => $request->category_id,
                    'is_active' => true,
                ]);

                $createdCount++;
            } catch (\Exception $e) {
                $errors[] = "المنتج في الصف " . ($index + 1) . ": " . $e->getMessage();
            }
        }

        if ($createdCount > 0) {
            AlertHelper::success("تم إضافة {$createdCount} منتج بنجاح");
        }

        if (!empty($errors)) {
            AlertHelper::error('بعض المنتجات لم يتم إضافتها: ' . implode(', ', $errors));
        }

        return redirect()->route('admin.products.index');
    }

    /**
     * Export products
     */
    public function export(Request $request)
    {
        $query = Product::with(['category', 'branchInventories', 'storeInventories']);

        // If specific products are selected
        if ($request->has('product_ids')) {
            $query->whereIn('id', $request->product_ids);
        }

        $products = $query->get();

        // Create CSV content
        $csvContent = "الاسم,الكود,التصنيف,السعر,المخزون الإجمالي,مخزون الفروع,مخزون المخازن,الحالة,تاريخ الإنشاء\n";

        foreach ($products as $product) {
            $branchStock = $product->branchInventories->sum('quantity');
            $storeStock = $product->storeInventories->sum('quantity');
            $totalStock = $branchStock + $storeStock;

            $csvContent .= sprintf(
                "%s,%s,%s,%s,%s,%s,%s,%s,%s\n",
                $product->name,
                $product->sku ?? '',
                $product->category->name,
                $product->price ?? 0,
                $totalStock,
                $branchStock,
                $storeStock,
                $product->is_active ? 'نشط' : 'غير نشط',
                $product->created_at->format('Y-m-d')
            );
        }

        $fileName = 'products_' . date('Y-m-d_H-i-s') . '.csv';

        return response($csvContent)
            ->header('Content-Type', 'text/csv; charset=UTF-8')
            ->header('Content-Disposition', 'attachment; filename="' . $fileName . '"')
            ->header('Content-Length', strlen($csvContent));
    }

    /**
     * Get sale prices for a product based on location
     */
    public function getSalePrices(Request $request, Product $product)
    {
        $validated = $request->validate([
            'location_type' => 'required|in:branch,store',
            'location_id' => 'required|integer',
        ]);

        $locationType = $validated['location_type'];
        $locationId = $validated['location_id'];

        try {
            if ($locationType === 'branch') {
                $inventory = BranchInventory::where('branch_id', $locationId)
                    ->where('product_id', $product->id)
                    ->first();
            } else {
                $inventory = StoreInventory::where('store_id', $locationId)
                    ->where('product_id', $product->id)
                    ->first();
            }

            // If inventory exists, return its sale prices
            if ($inventory) {
                return response()->json([
                    'success' => true,
                    'sale_price_1' => $inventory->sale_price_1,
                    'sale_price_2' => $inventory->sale_price_2,
                    'sale_price_3' => $inventory->sale_price_3,
                    'source' => 'existing_inventory'
                ]);
            }

            // If no inventory exists, return product default prices
            return response()->json([
                'success' => true,
                'sale_price_1' => $product->selling_price ?? $product->price ?? 0,
                'sale_price_2' => null,
                'sale_price_3' => null,
                'source' => 'product_default'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في جلب أسعار البيع: ' . $e->getMessage()
            ], 500);
        }
    }
}
