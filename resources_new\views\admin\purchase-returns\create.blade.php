<x-app-layout>
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="d-sm-flex align-items-center justify-content-between mb-4">
            <div>
                <h1 class="h3 mb-0 text-gray-800">
                    <i class="fas fa-plus text-primary me-2"></i>
                    إنشاء مرتجع شراء جديد
                </h1>
                <p class="mb-0 text-muted">إنشاء مرتجع جديد لعملية شراء</p>
            </div>
            <div class="d-flex gap-2">
                <a href="{{ user_route('purchase-returns.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-2"></i>العودة إلى المرتجعات
                </a>
            </div>
        </div>

        <form method="POST" action="{{ user_route('purchase-returns.store') }}" id="returnForm">
            @csrf

            <!-- Purchase Selection Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-shopping-cart me-2"></i>اختيار عملية الشراء
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="purchase_id" class="form-label fw-bold">
                                <i class="fas fa-receipt text-info me-2"></i>عملية الشراء
                            </label>
                            <select name="purchase_id" id="purchase_id"
                                class="form-select @error('purchase_id') is-invalid @enderror" required>
                                <option value="">اختر عملية شراء...</option>
                                @foreach ($recentPurchases as $purchaseOption)
                                    <option value="{{ $purchaseOption->id }}"
                                        {{ $purchase && $purchase->id == $purchaseOption->id ? 'selected' : '' }}>
                                        #{{ $purchaseOption->id }} - {{ $purchaseOption->supplier->name }} -
                                        {{ $purchaseOption->purchase_date }} -
                                        {{ format_currency($purchaseOption->total_amount) }}
                                    </option>
                                @endforeach
                            </select>
                            @error('purchase_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="return_date" class="form-label fw-bold">
                                <i class="fas fa-calendar text-warning me-2"></i>تاريخ المرتجع
                            </label>
                            <input type="date" name="return_date" id="return_date"
                                class="form-control @error('return_date') is-invalid @enderror"
                                value="{{ old('return_date', date('Y-m-d')) }}" required>
                            @error('return_date')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <!-- Purchase Details Card -->
            <div class="card shadow mb-4" id="purchaseDetailsCard" style="display: none;">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-info-circle me-2"></i>تفاصيل عملية الشراء
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label class="form-label fw-bold text-muted">المورد</label>
                            <div class="fw-bold text-primary" id="supplierName">-</div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label fw-bold text-muted">الفرع</label>
                            <div class="fw-bold" id="branchName">-</div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label fw-bold text-muted">المخزن</label>
                            <div class="fw-bold" id="storeName">-</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Return Details Card -->
            <div class="card shadow mb-4" id="returnDetailsCard" style="display: none;">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-undo me-2"></i>تفاصيل المرتجع
                    </h6>
                </div>
                <div class="card-body">

                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="return_type" class="form-label fw-bold">
                                <i class="fas fa-tags text-info me-2"></i>نوع المرتجع
                            </label>
                            <select name="return_type" id="return_type"
                                class="form-select @error('return_type') is-invalid @enderror" required>
                                <option value="partial" {{ old('return_type') == 'partial' ? 'selected' : '' }}>
                                    مرتجع جزئي</option>
                                <option value="full" {{ old('return_type') == 'full' ? 'selected' : '' }}>
                                    مرتجع كامل</option>
                            </select>
                            @error('return_type')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="refund_amount" class="form-label fw-bold">
                                <i class="fas fa-money-bill text-success me-2"></i>مبلغ الاسترداد
                            </label>
                            <div class="input-group">
                                <input type="number" name="refund_amount" id="refund_amount"
                                    class="form-control @error('refund_amount') is-invalid @enderror" step="0.01"
                                    min="0" value="{{ old('refund_amount', 0) }}" required>
                                <span class="input-group-text">ج.م</span>
                            </div>
                            @error('refund_amount')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="reason" class="form-label fw-bold">
                                <i class="fas fa-question-circle text-warning me-2"></i>سبب المرتجع
                            </label>
                            <select name="reason" id="reason"
                                class="form-select @error('reason') is-invalid @enderror" required>
                                <option value="">اختر السبب...</option>
                                <option value="damaged" {{ old('reason') == 'damaged' ? 'selected' : '' }}>تالف
                                </option>
                                <option value="expired" {{ old('reason') == 'expired' ? 'selected' : '' }}>منتهي
                                    الصلاحية</option>
                                <option value="wrong_item" {{ old('reason') == 'wrong_item' ? 'selected' : '' }}>خطأ
                                    في الطلب</option>
                                <option value="defective" {{ old('reason') == 'defective' ? 'selected' : '' }}>عيب في
                                    التصنيع</option>
                                <option value="other" {{ old('reason') == 'other' ? 'selected' : '' }}>أخرى</option>
                            </select>
                            @error('reason')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="notes" class="form-label fw-bold">
                                <i class="fas fa-sticky-note text-warning me-2"></i>ملاحظات
                            </label>
                            <textarea name="notes" id="notes" class="form-control @error('notes') is-invalid @enderror" rows="3"
                                placeholder="أي ملاحظات إضافية حول المرتجع...">{{ old('notes') }}</textarea>
                            @error('notes')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <!-- Items Selection Card -->
            <div class="card shadow mb-4" id="itemsCard" style="display: none;">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-boxes me-2"></i>أصناف المرتجع
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover" id="itemsTable">
                            <thead class="table-light">
                                <tr>
                                    <th width="5%">اختيار</th>
                                    <th width="25%">المنتج</th>
                                    <th width="10%">الكمية الأصلية</th>
                                    <th width="10%">المرتجعة سابقاً</th>
                                    <th width="10%">المتاحة للإرجاع</th>
                                    <th width="10%">الكمية المرتجعة</th>
                                    <th width="10%">سعر التكلفة</th>
                                    <th width="10%">الإجمالي</th>
                                    <th width="10%">الحالة</th>
                                </tr>
                            </thead>
                            <tbody id="itemsTableBody">
                                <!-- Items will be loaded here via AJAX -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Summary Card -->
            <div class="card shadow mb-4" id="summaryCard" style="display: none;">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-calculator me-2"></i>ملخص المرتجع
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-4">
                            <h5 class="text-primary">عدد المنتجات</h5>
                            <h3 class="text-primary" id="totalItems">0</h3>
                        </div>
                        <div class="col-md-4">
                            <h5 class="text-warning">إجمالي الكمية</h5>
                            <h3 class="text-warning" id="totalQuantity">0</h3>
                        </div>
                        <div class="col-md-4">
                            <h5 class="text-success">إجمالي المبلغ</h5>
                            <h3 class="text-success" id="totalReturnAmount">0.00 ج.م</h3>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="card shadow mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                تأكد من صحة البيانات قبل الحفظ
                            </small>
                        </div>
                        <div class="d-flex gap-2">
                            <a href="{{ user_route('purchase-returns.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </a>
                            <button type="submit" class="btn btn-primary btn-lg" id="submitBtn" disabled>
                                <i class="fas fa-save me-2"></i>إنشاء المرتجع
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>

    @push('scripts')
        <script>
            $(document).ready(function() {
                // Load purchase details when purchase is selected
                $('#purchase_id').change(function() {
                    const purchaseId = $(this).val();
                    if (purchaseId) {
                        loadPurchaseDetails(purchaseId);
                    } else {
                        $('#purchaseDetailsCard').hide();
                        $('#returnDetailsCard').hide();
                        $('#itemsCard').hide();
                        $('#summaryCard').hide();
                        $('#submitBtn').prop('disabled', true);
                    }
                });

                // If purchase is pre-selected, load its details
                @if ($purchase)
                    loadPurchaseDetails({{ $purchase->id }});
                @endif

                function loadPurchaseDetails(purchaseId) {
                    $.get(`{{ url('admin/purchases') }}/${purchaseId}/details`)
                        .done(function(data) {
                            // Populate purchase info
                            $('#supplierName').text(data.purchase.supplier.name);
                            $('#branchName').text(data.purchase.branch ? data.purchase.branch.name : 'غير محدد');
                            $('#storeName').text(data.purchase.store ? data.purchase.store.name : 'غير محدد');

                            // Populate items table
                            populateItemsTable(data.items);

                            // Show all cards
                            $('#purchaseDetailsCard').show();
                            $('#returnDetailsCard').show();
                            $('#itemsCard').show();
                            $('#summaryCard').show();
                            $('#submitBtn').prop('disabled', false);
                        })
                        .fail(function(xhr, status, error) {
                            console.error('AJAX Error:', xhr.responseText);
                            alert('حدث خطأ في تحميل بيانات الشراء: ' + error);
                        });
                }

                function populateItemsTable(items) {
                    const tbody = $('#itemsTableBody');
                    tbody.empty();

                    items.forEach(function(item, index) {
                        if (item.available_for_return > 0) {
                            const row = `
                    <tr>
                        <td>
                            <input type="checkbox" class="form-check-input item-checkbox" data-index="${index}">
                        </td>
                        <td>
                            <strong>${item.product.name}</strong>
                            <br><small class="text-muted">كود: ${item.product.code || 'غير محدد'}</small>
                        </td>
                        <td class="text-center">${item.quantity}</td>
                        <td class="text-center text-warning">${item.already_returned}</td>
                        <td class="text-center text-success">${item.available_for_return}</td>
                        <td>
                            <input type="number" name="items[${index}][quantity_returned]" class="form-control quantity-input"
                                   step="0.01" min="0.01" max="${item.available_for_return}" disabled>
                            <input type="hidden" name="items[${index}][purchase_item_id]" value="${item.id}">
                        </td>
                        <td class="text-center">${parseFloat(item.cost_price).toFixed(2)} ج.م</td>
                        <td class="item-total text-center fw-bold">0.00 ج.م</td>
                        <td>
                            <select name="items[${index}][condition]" class="form-select form-select-sm" disabled>
                                <option value="good">جيد</option>
                                <option value="damaged">تالف</option>
                                <option value="expired">منتهي الصلاحية</option>
                                <option value="defective">معيب</option>
                            </select>
                        </td>
                    </tr>
                `;
                            tbody.append(row);
                        }
                    });

                    // Bind events
                    bindItemEvents();
                }

                function bindItemEvents() {
                    // Enable/disable inputs based on checkbox
                    $('.item-checkbox').change(function() {
                        const row = $(this).closest('tr');
                        const isChecked = $(this).is(':checked');

                        row.find('input, select').not('.item-checkbox').prop('disabled', !isChecked);

                        if (!isChecked) {
                            row.find('.quantity-input').val('');
                            row.find('.item-total').text('0.00');
                        }

                        calculateTotal();
                    });

                    // Calculate item total when quantity changes
                    $('.quantity-input').on('input', function() {
                        const row = $(this).closest('tr');
                        const quantity = parseFloat($(this).val()) || 0;
                        const costPriceText = row.find('td:nth-child(7)').text().replace(' ج.م', '');
                        const costPrice = parseFloat(costPriceText) || 0;
                        const total = quantity * costPrice;

                        row.find('.item-total').text(total.toFixed(2) + ' ج.م');
                        calculateTotal();
                    });
                }

                function calculateTotal() {
                    let total = 0;
                    let totalQuantity = 0;
                    let totalItems = 0;

                    $('.item-checkbox:checked').each(function() {
                        const row = $(this).closest('tr');
                        const itemTotal = parseFloat(row.find('.item-total').text().replace(' ج.م', '')) || 0;
                        const quantity = parseFloat(row.find('.quantity-input').val()) || 0;

                        if (quantity > 0) {
                            total += itemTotal;
                            totalQuantity += quantity;
                            totalItems++;
                        }
                    });

                    $('#totalReturnAmount').text(total.toFixed(2) + ' ج.م');
                    $('#totalQuantity').text(totalQuantity.toFixed(2));
                    $('#totalItems').text(totalItems);
                    $('#refund_amount').val(total.toFixed(2));

                    // Enable/disable submit button based on whether items are selected
                    $('#submitBtn').prop('disabled', totalItems === 0);
                }
            });
        </script>
    @endpush
</x-app-layout>
