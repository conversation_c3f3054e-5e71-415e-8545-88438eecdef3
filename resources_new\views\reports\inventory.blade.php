<x-app-layout>
    <x-slot name="header">
        <div class="d-flex justify-content-between align-items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('تقرير المخزون') }}
            </h2>
            <div>
                <button type="button" class="btn btn-success" onclick="printReport()">
                    <i class="fas fa-print"></i> {{ __('طباعة') }}
                </button>
                <button type="button" class="btn btn-primary" onclick="exportToExcel()">
                    <i class="fas fa-file-excel"></i> {{ __('تصدير إلى Excel') }}
                </button>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="container-fluid">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <!-- Filters -->
                    <form action="{{ route('reports.inventory') }}" method="GET" class="mb-4">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="search">{{ __('بحث') }}</label>
                                    <input type="text" name="search" id="search" class="form-control" value="{{ request('search') }}" placeholder="{{ __('اسم المنتج أو الرمز') }}">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="category">{{ __('التصنيف') }}</label>
                                    <select name="category" id="category" class="form-select">
                                        <option value="">{{ __('الكل') }}</option>
                                        @foreach($categories as $category)
                                            <option value="{{ $category->id }}" {{ request('category') == $category->id ? 'selected' : '' }}>
                                                {{ $category->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="branch">{{ __('الفرع') }}</label>
                                    <select name="branch" id="branch" class="form-select">
                                        <option value="">{{ __('الكل') }}</option>
                                        @foreach($branches as $branch)
                                            <option value="{{ $branch->id }}" {{ request('branch') == $branch->id ? 'selected' : '' }}>
                                                {{ $branch->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="stock_status">{{ __('حالة المخزون') }}</label>
                                    <select name="stock_status" id="stock_status" class="form-select">
                                        <option value="">{{ __('الكل') }}</option>
                                        <option value="available" {{ request('stock_status') == 'available' ? 'selected' : '' }}>{{ __('متوفر') }}</option>
                                        <option value="low" {{ request('stock_status') == 'low' ? 'selected' : '' }}>{{ __('منخفض') }}</option>
                                        <option value="out" {{ request('stock_status') == 'out' ? 'selected' : '' }}>{{ __('غير متوفر') }}</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="sort_by">{{ __('ترتيب حسب') }}</label>
                                    <select name="sort_by" id="sort_by" class="form-select">
                                        <option value="name_asc" {{ request('sort_by') == 'name_asc' ? 'selected' : '' }}>{{ __('الاسم (أ-ي)') }}</option>
                                        <option value="name_desc" {{ request('sort_by') == 'name_desc' ? 'selected' : '' }}>{{ __('الاسم (ي-أ)') }}</option>
                                        <option value="stock_asc" {{ request('sort_by') == 'stock_asc' ? 'selected' : '' }}>{{ __('المخزون (تصاعدي)') }}</option>
                                        <option value="stock_desc" {{ request('sort_by') == 'stock_desc' ? 'selected' : '' }}>{{ __('المخزون (تنازلي)') }}</option>
                                        <option value="value_asc" {{ request('sort_by') == 'value_asc' ? 'selected' : '' }}>{{ __('القيمة (تصاعدي)') }}</option>
                                        <option value="value_desc" {{ request('sort_by') == 'value_desc' ? 'selected' : '' }}>{{ __('القيمة (تنازلي)') }}</option>
                                        <option value="category_asc" {{ request('sort_by') == 'category_asc' ? 'selected' : '' }}>{{ __('التصنيف (أ-ي)') }}</option>
                                        <option value="category_desc" {{ request('sort_by') == 'category_desc' ? 'selected' : '' }}>{{ __('التصنيف (ي-أ)') }}</option>
                                        <option value="branch_asc" {{ request('sort_by') == 'branch_asc' ? 'selected' : '' }}>{{ __('الفرع (أ-ي)') }}</option>
                                        <option value="branch_desc" {{ request('sort_by') == 'branch_desc' ? 'selected' : '' }}>{{ __('الفرع (ي-أ)') }}</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-9 text-start">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i> {{ __('عرض التقرير') }}
                                </button>
                                <a href="{{ route('reports.inventory') }}" class="btn btn-secondary">
                                    <i class="fas fa-redo"></i> {{ __('إعادة تعيين') }}
                                </a>
                            </div>
                        </div>
                    </form>

                    <!-- Summary Cards -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <h5 class="card-title">{{ __('إجمالي المنتجات') }}</h5>
                                    <h3 class="mb-0">{{ $summary['total_products'] }}</h3>
                                    <small>{{ __('في') }} {{ $summary['total_categories'] }} {{ __('تصنيف') }}</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <h5 class="card-title">{{ __('قيمة المخزون') }}</h5>
                                    <h3 class="mb-0">{{ number_format($summary['total_value'], 2) }}</h3>
                                    <small>{{ __('قيمة البيع') }}: {{ number_format($summary['total_sale_value'], 2) }}</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <h5 class="card-title">{{ __('حالة المخزون') }}</h5>
                                    <h3 class="mb-0">{{ $summary['total_stock'] }}</h3>
                                    <small>{{ __('متوسط') }}: {{ number_format($summary['average_stock'], 1) }}</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-danger text-white">
                                <div class="card-body">
                                    <h5 class="card-title">{{ __('تنبيهات') }}</h5>
                                    <h3 class="mb-0">{{ $summary['low_stock_count'] + $summary['out_of_stock_count'] }}</h3>
                                    <small>{{ __('منخفض') }}: {{ $summary['low_stock_count'] }} | {{ __('غير متوفر') }}: {{ $summary['out_of_stock_count'] }}</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Top Categories and Branches -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">{{ __('أعلى التصنيفات') }}</h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>{{ __('التصنيف') }}</th>
                                                    <th>{{ __('المخزون') }}</th>
                                                    <th>{{ __('القيمة') }}</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach($topCategories as $category)
                                                    <tr>
                                                        <td>{{ $category['name'] }}</td>
                                                        <td>{{ $category['total_stock'] }}</td>
                                                        <td>{{ number_format($category['total_value'], 2) }}</td>
                                                    </tr>
                                                @endforeach
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">{{ __('أعلى الفروع') }}</h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>{{ __('الفرع') }}</th>
                                                    <th>{{ __('المخزون') }}</th>
                                                    <th>{{ __('القيمة') }}</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach($topBranches as $branch)
                                                    <tr>
                                                        <td>{{ $branch['name'] }}</td>
                                                        <td>{{ $branch['total_stock'] }}</td>
                                                        <td>{{ number_format($branch['total_value'], 2) }}</td>
                                                    </tr>
                                                @endforeach
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Inventory Table -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead>
                                <tr>
                                    <th>{{ __('المنتج') }}</th>
                                    <th>{{ __('التصنيف') }}</th>
                                    <th>{{ __('الفرع') }}</th>
                                    <th>{{ __('المخزون') }}</th>
                                    <th>{{ __('الحد الأدنى') }}</th>
                                    <th>{{ __('سعر الشراء') }}</th>
                                    <th>{{ __('سعر البيع') }}</th>
                                    <th>{{ __('قيمة المخزون') }}</th>
                                    <th>{{ __('الحالة') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($products as $product)
                                    <tr>
                                        <td>
                                            {{ $product->name }}
                                            @if($product->sku)
                                                <br><small class="text-muted">{{ $product->sku }}</small>
                                            @endif
                                        </td>
                                        <td>{{ $product->category->name }}</td>
                                        <td>
                                            @foreach($product->branchInventories as $inventory)
                                                {{ $inventory->branch->name }}: {{ $inventory->quantity }}<br>
                                            @endforeach
                                        </td>
                                        <td>{{ $product->branchInventories->sum('quantity') }}</td>
                                        <td>{{ $product->minimum_stock }}</td>
                                        <td>{{ number_format($product->cost_price, 2) }}</td>
                                        <td>{{ number_format($product->sale_price, 2) }}</td>
                                        <td>{{ number_format($product->branchInventories->sum(function($inventory) use ($product) {
                                            return $inventory->quantity * $product->cost_price;
                                        }), 2) }}</td>
                                        <td>
                                            @php
                                                $totalStock = $product->branchInventories->sum('quantity');
                                                $minStock = $product->minimum_stock;
                                            @endphp
                                            @if($totalStock <= 0)
                                                <span class="badge bg-danger">{{ __('غير متوفر') }}</span>
                                            @elseif($totalStock <= $minStock)
                                                <span class="badge bg-warning">{{ __('منخفض') }}</span>
                                            @else
                                                <span class="badge bg-success">{{ __('متوفر') }}</span>
                                            @endif
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="9" class="text-center">{{ __('لا توجد بيانات') }}</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="mt-4">
                        {{ $products->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>

    @push('styles')
    <style>
        .rtl {
            direction: rtl;
            text-align: right;
        }
        .rtl .form-select {
            text-align: right;
        }
        .rtl .table th,
        .rtl .table td {
            text-align: right;
        }
        .rtl .text-start {
            text-align: right !important;
        }
        .rtl .text-end {
            text-align: left !important;
        }
        .rtl .form-control {
            text-align: right;
        }
        .rtl .form-control::placeholder {
            text-align: right;
        }
        .rtl .card-title {
            text-align: right;
        }
        @media print {
            .no-print {
                display: none !important;
            }
            .container-fluid {
                width: 100% !important;
                padding: 0 !important;
                margin: 0 !important;
            }
        }
    </style>
    @endpush

    @push('scripts')
    <script>
        function printReport() {
            window.print();
        }

        function exportToExcel() {
            // Add Excel export functionality here
            alert('سيتم إضافة وظيفة التصدير إلى Excel قريباً');
        }
    </script>
    @endpush
</x-app-layout>
