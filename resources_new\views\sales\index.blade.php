<x-app-layout>
    <x-slot name="header">
        <div class="d-flex justify-content-between align-items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                <i class="fas fa-shopping-cart text-primary"></i> إدارة المبيعات
            </h2>
            <div class="d-flex gap-2">
                {{-- @if (auth()->user()->hasRole('seller'))
                    <a href="{{ route('seller.quick-sale') }}" class="btn btn-success btn-sm">
                        <i class="fas fa-bolt"></i> بيع سريع
                    </a>
                @endif --}}
                <a href="{{ user_route('sales.create') }}" class="btn btn-primary btn-sm">
                    <i class="fas fa-plus"></i> عملية بيع جديدة
                </a>
                {{-- <div class="dropdown">
                    <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button"
                        data-bs-toggle="dropdown">
                        <i class="fas fa-download"></i> تصدير
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="exportSales('excel')">
                                <i class="fas fa-file-excel text-success"></i> Excel
                            </a></li>
                        <li><a class="dropdown-item" href="#" onclick="exportSales('pdf')">
                                <i class="fas fa-file-pdf text-danger"></i> PDF
                            </a></li>
                    </ul>
                </div> --}}
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="container-fluid">
            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-primary shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                        مبيعات اليوم
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        {{ number_format($stats['today_sales'] ?? 0, 2) }} ج.م
                                    </div>
                                    <div class="text-xs text-muted">
                                        {{ $stats['today_sales_count'] ?? 0 }} عملية
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-calendar-day fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-success shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        مبيعات الشهر
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        {{ number_format($stats['month_sales'] ?? 0, 2) }} ج.م
                                    </div>
                                    <div class="text-xs text-muted">
                                        {{ $stats['month_sales_count'] ?? 0 }} عملية
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-calendar-alt fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-info shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                        متوسط قيمة البيع
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        {{ number_format($stats['avg_sale_value'] ?? 0, 2) }} ج.م
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-warning shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                        عمليات معلقة
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        {{ $stats['pending_sales'] ?? 0 }}
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-clock fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Enhanced Filters -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-body">
                    <div class="bg-light p-3 rounded">
                        <form action="{{ user_route('sales.index') }}" method="GET" id="filterForm">
                            <div class="row g-3">
                                <div class="col-md-2">
                                    <label class="form-label small fw-bold">البحث</label>
                                    <div class="input-group">
                                        <input type="text" name="search" class="form-control"
                                            placeholder="رقم الفاتورة أو اسم العميل..." value="{{ request('search') }}">
                                        <button class="btn btn-primary" type="submit">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label small fw-bold">العميل</label>
                                    <select name="customer_id" class="form-select" onchange="this.form.submit()">
                                        <option value="">جميع العملاء</option>
                                        @foreach ($customers as $customer)
                                            <option value="{{ $customer->id }}"
                                                {{ request('customer_id') == $customer->id ? 'selected' : '' }}>
                                                {{ $customer->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label small fw-bold">من تاريخ</label>
                                    <input type="date" name="start_date" class="form-control"
                                        value="{{ request('start_date') }}">
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label small fw-bold">إلى تاريخ</label>
                                    <input type="date" name="end_date" class="form-control"
                                        value="{{ request('end_date') }}">
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label small fw-bold">الحالة</label>
                                    <select name="status" class="form-select" onchange="this.form.submit()">
                                        <option value="">جميع الحالات</option>
                                        <option value="completed"
                                            {{ request('status') == 'completed' ? 'selected' : '' }}>
                                            <i class="fas fa-check-circle"></i> مكتمل
                                        </option>
                                        <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>
                                            <i class="fas fa-clock"></i> قيد الانتظار
                                        </option>
                                        <option value="cancelled"
                                            {{ request('status') == 'cancelled' ? 'selected' : '' }}>
                                            <i class="fas fa-times-circle"></i> ملغي
                                        </option>
                                    </select>
                                </div>
                                @if (auth()->user()->canAccessAllBranches() && $branches->count() > 0)
                                    <div class="col-md-2">
                                        <label class="form-label small fw-bold">الفرع</label>
                                        <select name="branch_id" class="form-select" onchange="this.form.submit()">
                                            <option value="">جميع الفروع</option>
                                            @foreach ($branches as $branch)
                                                <option value="{{ $branch->id }}"
                                                    {{ request('branch_id') == $branch->id ? 'selected' : '' }}>
                                                    {{ $branch->name }}
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>
                                @endif
                                <div class="col-md-1">
                                    <label class="form-label small fw-bold">&nbsp;</label>
                                    <div class="d-grid">
                                        <button type="button" class="btn btn-outline-secondary"
                                            onclick="clearFilters()">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Quick Filter Buttons -->
                            <div class="row mt-3">
                                <div class="col-12">
                                    <div class="btn-group btn-group-sm" role="group">
                                        <button type="button" class="btn btn-outline-primary"
                                            onclick="setDateFilter('today')">
                                            اليوم
                                        </button>
                                        <button type="button" class="btn btn-outline-primary"
                                            onclick="setDateFilter('yesterday')">
                                            أمس
                                        </button>
                                        <button type="button" class="btn btn-outline-primary"
                                            onclick="setDateFilter('week')">
                                            هذا الأسبوع
                                        </button>
                                        <button type="button" class="btn btn-outline-primary"
                                            onclick="setDateFilter('month')">
                                            هذا الشهر
                                        </button>
                                        <button type="button" class="btn btn-outline-primary"
                                            onclick="setDateFilter('last_month')">
                                            الشهر الماضي
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Main Sales Table Card -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white border-bottom">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-list text-primary"></i> قائمة المبيعات
                        </h5>
                        <div class="btn-group">
                            <button type="button" class="btn btn-outline-success btn-sm">
                                <i class="fas fa-file-excel"></i> تصدير Excel
                            </button>
                            <button type="button" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-print"></i> طباعة
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0" id="salesTable">
                            <thead class="table-light">
                                <tr>
                                    <th class="border-0" style="width: 40px;">
                                        <input type="checkbox" id="selectAll" class="form-check-input">
                                    </th>
                                    <th class="border-0">
                                        <a href="#" class="text-decoration-none text-dark"
                                            onclick="sortTable('invoice_number')">
                                            <i class="fas fa-receipt me-1 text-primary"></i>رقم الفاتورة
                                            <i class="fas fa-sort ms-1 text-muted"></i>
                                        </a>
                                    </th>
                                    <th class="border-0">
                                        <a href="#" class="text-decoration-none text-dark"
                                            onclick="sortTable('customer_name')">
                                            <i class="fas fa-user me-1 text-primary"></i>العميل
                                            <i class="fas fa-sort ms-1 text-muted"></i>
                                        </a>
                                    </th>
                                    @if (auth()->user()->canAccessAllBranches())
                                        <th class="border-0">
                                            <i class="fas fa-store me-1 text-primary"></i>الفرع
                                        </th>
                                    @endif
                                    <th class="border-0">
                                        <a href="#" class="text-decoration-none text-dark"
                                            onclick="sortTable('created_at')">
                                            <i class="fas fa-calendar me-1 text-primary"></i>التاريخ
                                            <i class="fas fa-sort ms-1 text-muted"></i>
                                        </a>
                                    </th>
                                    <th class="border-0 text-center">
                                        <i class="fas fa-boxes me-1 text-primary"></i>المنتجات
                                    </th>
                                    <th class="border-0">
                                        <a href="#" class="text-decoration-none text-dark"
                                            onclick="sortTable('total_amount')">
                                            <i class="fas fa-money-bill me-1 text-primary"></i>الإجمالي
                                            <i class="fas fa-sort ms-1 text-muted"></i>
                                        </a>
                                    </th>
                                    <th class="border-0">
                                        <i class="fas fa-undo me-1 text-primary"></i>المرتجعات
                                    </th>
                                    <th class="border-0">
                                        <i class="fas fa-credit-card me-1 text-primary"></i>حالة الدفع
                                    </th>
                                    <th class="border-0">
                                        <i class="fas fa-user-tie me-1 text-primary"></i>البائع
                                    </th>
                                    <th class="border-0 text-center">
                                        <i class="fas fa-cogs me-1 text-primary"></i>الإجراءات
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($sales as $sale)
                                    <tr class="sale-row" data-sale-id="{{ $sale->id }}">
                                        <td>
                                            <input type="checkbox" class="form-check-input sale-checkbox"
                                                value="{{ $sale->id }}">
                                        </td>
                                        <td>
                                            <div class="d-flex flex-column">
                                                <span class="fw-bold text-primary">{{ $sale->invoice_number }}</span>
                                                <small
                                                    class="text-muted">{{ $sale->created_at->format('H:i') }}</small>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div
                                                    class="avatar-sm bg-light rounded-circle d-flex align-items-center justify-content-center me-2">
                                                    <i class="fas fa-user text-muted"></i>
                                                </div>
                                                <div>
                                                    <div class="fw-bold">{{ $sale->customer->name ?? 'عميل نقدي' }}
                                                    </div>
                                                    @if ($sale->customer && $sale->customer->phone)
                                                        <small class="text-muted">{{ $sale->customer->phone }}</small>
                                                    @endif
                                                </div>
                                            </div>
                                        </td>
                                        @if (auth()->user()->canAccessAllBranches())
                                            <td>
                                                <span
                                                    class="badge bg-info">{{ $sale->branch->name ?? 'غير محدد' }}</span>
                                            </td>
                                        @endif
                                        <td>
                                            <div class="d-flex flex-column">
                                                <span>{{ $sale->created_at->format('Y-m-d') }}</span>
                                                <small
                                                    class="text-muted">{{ $sale->created_at->diffForHumans() }}</small>
                                            </div>
                                        </td>
                                        <td class="text-center">
                                            <span class="badge bg-secondary fs-6">{{ $sale->items->count() }}</span>
                                            <br><small class="text-muted">منتج</small>
                                        </td>
                                        <td>
                                            <div class="d-flex flex-column">
                                                <span class="fw-bold fs-6">{{ number_format($sale->total_amount, 2) }}
                                                    ج.م</span>
                                                @if ($sale->discount_amount > 0)
                                                    <small class="text-warning">خصم:
                                                        {{ number_format($sale->discount_amount, 2) }} ج.م</small>
                                                @endif
                                            </div>
                                        </td>
                                        <td class="text-center">
                                            @if ($sale->hasReturns())
                                                <div class="d-flex flex-column align-items-center">
                                                    <span
                                                        class="badge bg-warning fs-6">{{ $sale->returns->where('status', '!=', 'cancelled')->count() }}</span>
                                                    <small class="text-muted">مرتجع</small>
                                                    <small class="text-danger fw-bold">
                                                        {{ number_format($sale->total_returns, 2) }} ج.م
                                                    </small>
                                                </div>
                                            @else
                                                <span class="text-muted">-</span>
                                            @endif
                                        </td>
                                        <td>
                                            {{-- @if ($sale->status === 'completed') --}}
                                            <div class="d-flex flex-column">
                                                <span
                                                    class="badge bg-{{ $sale->payment_status_color }} px-2 py-1 mb-1 fw-bold">
                                                    @if ($sale->payment_status === 'paid')
                                                        <i class="fas fa-check-circle me-1"></i>
                                                    @elseif ($sale->payment_status === 'partial')
                                                        <i class="fas fa-clock me-1"></i>
                                                    @else
                                                        <i class="fas fa-times-circle me-1"></i>
                                                    @endif
                                                    {{ $sale->payment_status_label }}
                                                </span>
                                                @if ($sale->payment_status !== 'paid')
                                                    <div class="small text-muted">
                                                        @if ($sale->payment_status === 'partial')
                                                            <div>مدفوع:
                                                                {{ number_format($sale->getTotalPaymentsAttribute(), 2) }}
                                                                ج.م</div>
                                                            <div class="text-danger fw-bold">
                                                                متبقي:
                                                                {{ number_format($sale->getActualRemainingAmountAttribute(), 2) }}
                                                                ج.م
                                                            </div>
                                                        @else
                                                            <div class="text-danger fw-bold">
                                                                المطلوب:
                                                                {{ number_format($sale->total_amount - $sale->discount_amount, 2) }}
                                                                ج.م
                                                            </div>
                                                        @endif
                                                    </div>
                                                @else
                                                    <div class="small text-success">
                                                        مدفوع:
                                                        {{ number_format($sale->getTotalPaymentsAttribute(), 2) }}
                                                        ج.م
                                                    </div>
                                                @endif
                                            </div>
                                            {{-- @elseif ($sale->status === 'pending')
                                                <span class="badge bg-warning px-2 py-1 fw-bold">
                                                    <i class="fas fa-clock me-1"></i>معلق
                                                </span>
                                                <div class="small text-muted mt-1">
                                                    في انتظار الإكمال
                                                </div>
                                            @else
                                                <span class="badge bg-danger px-2 py-1 fw-bold">
                                                    <i class="fas fa-times-circle me-1"></i>ملغي
                                                </span>
                                                <div class="small text-muted mt-1">
                                                    عملية ملغاة
                                                </div>
                                            @endif --}}
                                        </td>
                                        <td>
                                            @if ($sale->user)
                                                <div class="d-flex align-items-center">
                                                    <div
                                                        class="avatar-xs bg-primary rounded-circle d-flex align-items-center justify-content-center me-1">
                                                        <i class="fas fa-user text-white"
                                                            style="font-size: 0.7rem;"></i>
                                                    </div>
                                                    <small>{{ $sale->user->name }}</small>
                                                </div>
                                            @else
                                                <small class="text-muted">غير محدد</small>
                                            @endif
                                        </td>
                                        <td class="text-center">
                                            <div class="d-flex gap-1 justify-content-center flex-wrap">
                                                <!-- Quick Action Buttons -->
                                                <a href="{{ user_route('sales.show', $sale) }}"
                                                    class="btn btn-outline-info btn-sm" title="عرض التفاصيل">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                @if ($sale->status === 'completed' && $sale->canReceivePayment())
                                                    <a href="{{ user_route('customer-payments.create', ['sale_id' => $sale->id]) }}"
                                                        class="btn btn-outline-success btn-sm" title="إضافة دفعة">
                                                        <i class="fas fa-money-bill-wave"></i>
                                                    </a>
                                                @endif
                                                @if ($sale->status === 'completed' && $sale->canBeReturned())
                                                    <a href="{{ user_route('sale-returns.create', ['sale_id' => $sale->id]) }}"
                                                        class="btn btn-outline-warning btn-sm" title="إنشاء مرتجع">
                                                        <i class="fas fa-undo"></i>
                                                    </a>
                                                @endif

                                                <!-- Dropdown Menu -->
                                                <div class="btn-group btn-group-sm">
                                                    <button type="button"
                                                        class="btn btn-outline-primary dropdown-toggle"
                                                        data-bs-toggle="dropdown" aria-expanded="false">
                                                        <i class="fas fa-cog"></i>
                                                    </button>
                                                    <ul class="dropdown-menu">
                                                        <li>
                                                            <a class="dropdown-item"
                                                                href="{{ user_route('sales.show', $sale) }}">
                                                                <i class="fas fa-eye text-info"></i> عرض التفاصيل
                                                            </a>
                                                        </li>
                                                        @if ($sale->status === 'pending')
                                                            <li>
                                                                <a class="dropdown-item"
                                                                    href="{{ user_route('sales.edit', $sale) }}">
                                                                    <i class="fas fa-edit text-primary"></i> تعديل
                                                                </a>
                                                            </li>
                                                        @endif
                                                        <li>
                                                            <a class="dropdown-item"
                                                                href="{{ user_route('sales.print', $sale) }}"
                                                                target="_blank">
                                                                <i class="fas fa-print text-secondary"></i> طباعة
                                                            </a>
                                                        </li>
                                                        @if (isset($sale->receipt_url))
                                                            <li>
                                                                <a class="dropdown-item"
                                                                    href="{{ $sale->receipt_url }}" target="_blank">
                                                                    <i class="fas fa-receipt text-info"></i> إيصال
                                                                </a>
                                                            </li>
                                                        @endif
                                                        @if ($sale->status === 'completed')
                                                            @if ($sale->canReceivePayment())
                                                                <li>
                                                                    <a class="dropdown-item"
                                                                        href="{{ user_route('customer-payments.create', ['sale_id' => $sale->id]) }}">
                                                                        <i
                                                                            class="fas fa-money-bill-wave text-success"></i>
                                                                        إضافة دفعة
                                                                    </a>
                                                                </li>
                                                            @endif
                                                            @if ($sale->canBeReturned())
                                                                <li>
                                                                    <a class="dropdown-item"
                                                                        href="{{ user_route('sale-returns.create', ['sale_id' => $sale->id]) }}">
                                                                        <i class="fas fa-undo text-warning"></i> إنشاء
                                                                        مرتجع
                                                                    </a>
                                                                </li>
                                                            @endif
                                                            @if (auth()->user()->hasRole('admin'))
                                                                <li>
                                                                    <hr class="dropdown-divider">
                                                                </li>
                                                                <li>
                                                                    <form
                                                                        action="{{ user_route('sales.destroy', $sale) }}"
                                                                        method="POST" class="d-inline">
                                                                        @csrf
                                                                        @method('DELETE')
                                                                        <button type="submit"
                                                                            class="dropdown-item text-danger"
                                                                            onclick="return confirm('تحذير: حذف عملية بيع مكتملة سيؤثر على المخزون والحسابات. هل أنت متأكد؟')">
                                                                            <i class="fas fa-trash"></i> حذف (إداري)
                                                                        </button>
                                                                    </form>
                                                                </li>
                                                            @else
                                                                <li>
                                                                    <hr class="dropdown-divider">
                                                                </li>
                                                            @endif
                                                        @endif
                                                        @if ($sale->status === 'pending' && auth()->user()->hasRole('admin'))
                                                            <li>
                                                                <a class="dropdown-item"
                                                                    href="{{ user_route('sales.edit', $sale) }}">
                                                                    <i class="fas fa-edit text-warning"></i> تعديل
                                                                </a>
                                                            </li>
                                                            <li>
                                                                <form
                                                                    action="{{ user_route('sales.destroy', $sale) }}"
                                                                    method="POST" class="d-inline">
                                                                    @csrf
                                                                    @method('DELETE')
                                                                    <button type="submit"
                                                                        class="dropdown-item text-danger"
                                                                        onclick="return confirm('هل أنت متأكد من حذف هذه العملية؟')">
                                                                        <i class="fas fa-trash"></i> حذف
                                                                    </button>
                                                                </form>
                                                            </li>
                                                        @endif
                                                    </ul>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="{{ auth()->user()->canAccessAllBranches() ? '10' : '9' }}"
                                            class="text-center py-5">
                                            <div class="d-flex flex-column align-items-center">
                                                <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                                                <h5 class="text-muted">لا توجد عمليات بيع</h5>
                                                <p class="text-muted">لم يتم العثور على عمليات بيع مطابقة للمعايير
                                                    المحددة</p>
                                                <a href="{{ user_route('sales.create') }}" class="btn btn-primary">
                                                    <i class="fas fa-plus"></i> إنشاء عملية بيع جديدة
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Bulk Actions Bar -->
                    <div class="d-flex justify-content-between align-items-center mt-3" id="bulkActionsBar"
                        style="display: none !important;">
                        <div class="d-flex align-items-center">
                            <span class="me-3">
                                <span id="selectedCount">0</span> عنصر محدد
                            </span>
                            <div class="btn-group btn-group-sm">
                                @if (auth()->user()->hasRole('admin'))
                                    <button type="button" class="btn btn-outline-danger" onclick="bulkDelete()">
                                        <i class="fas fa-trash"></i> حذف المحدد
                                    </button>
                                @endif
                                <button type="button" class="btn btn-outline-primary" onclick="bulkPrint()">
                                    <i class="fas fa-print"></i> طباعة المحدد
                                </button>
                                <button type="button" class="btn btn-outline-success" onclick="bulkExport()">
                                    <i class="fas fa-download"></i> تصدير المحدد
                                </button>
                            </div>
                        </div>
                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearSelection()">
                            <i class="fas fa-times"></i> إلغاء التحديد
                        </button>
                    </div>

                    <!-- Enhanced Pagination -->
                    <div class="d-flex justify-content-between align-items-center mt-4">
                        <div class="d-flex align-items-center">
                            <span class="text-muted me-3">
                                عرض {{ $sales->firstItem() ?? 0 }} إلى {{ $sales->lastItem() ?? 0 }}
                                من أصل {{ $sales->total() }} عملية
                            </span>
                            <select class="form-select form-select-sm" style="width: auto;"
                                onchange="changePerPage(this.value)">
                                <option value="10" {{ request('per_page') == 10 ? 'selected' : '' }}>10 لكل صفحة
                                </option>
                                <option value="25" {{ request('per_page') == 25 ? 'selected' : '' }}>25 لكل صفحة
                                </option>
                                <option value="50" {{ request('per_page') == 50 ? 'selected' : '' }}>50 لكل صفحة
                                </option>
                                <option value="100" {{ request('per_page') == 100 ? 'selected' : '' }}>100 لكل صفحة
                                </option>
                            </select>
                        </div>
                        <div>
                            {{ $sales->appends(request()->query())->links() }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @push('styles')
        <style>
            .border-left-primary {
                border-left: 0.25rem solid #4e73df !important;
            }

            .border-left-success {
                border-left: 0.25rem solid #1cc88a !important;
            }

            .border-left-info {
                border-left: 0.25rem solid #36b9cc !important;
            }

            /* Table Design Improvements */
            #salesTable {
                font-size: 0.9rem;
            }

            #salesTable th {
                font-size: 0.85rem;
                font-weight: 600;
                vertical-align: middle;
                padding: 0.75rem 0.5rem;
                white-space: nowrap;
            }

            #salesTable td {
                vertical-align: middle;
                padding: 0.75rem 0.5rem;
            }

            /* Responsive table adjustments */
            @media (max-width: 1200px) {

                #salesTable th,
                #salesTable td {
                    padding: 0.5rem 0.25rem;
                    font-size: 0.8rem;
                }
            }

            .avatar-sm {
                width: 32px;
                height: 32px;
                font-size: 0.8rem;
            }

            .avatar-xs {
                width: 24px;
                height: 24px;
                font-size: 0.7rem;
            }

            .table-responsive {
                border-radius: 0.375rem;
                box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            }

            .btn-group-sm .btn {
                padding: 0.25rem 0.5rem;
                font-size: 0.75rem;
            }

            /* Responsive adjustments */
            @media (max-width: 768px) {
                #salesTable {
                    font-size: 0.8rem;
                }

                #salesTable th,
                #salesTable td {
                    padding: 0.5rem 0.25rem;
                }

                .btn-sm {
                    padding: 0.2rem 0.4rem;
                    font-size: 0.7rem;
                }
            }

            /* Badge improvements */
            .badge {
                font-size: 0.75rem;
                font-weight: 500;
            }

            /* Payment status colors */
            .bg-success {
                background-color: #28a745 !important;
            }

            .bg-warning {
                background-color: #ffc107 !important;
                color: #212529 !important;
            }

            .bg-danger {
                background-color: #dc3545 !important;
            }

            .bg-info {
                background-color: #17a2b8 !important;
            }

            .bg-secondary {
                background-color: #6c757d !important;
            }

            .border-left-warning {
                border-left: 0.25rem solid #f6c23e !important;
            }

            .avatar-sm {
                width: 2rem;
                height: 2rem;
            }

            .avatar-xs {
                width: 1.5rem;
                height: 1.5rem;
            }

            .sale-row:hover {
                background-color: #f8f9fa;
            }

            .table th {
                border-top: none;
                font-weight: 600;
                font-size: 0.875rem;
            }

            .btn-group-sm>.btn {
                padding: 0.25rem 0.5rem;
                font-size: 0.75rem;
            }
        </style>
    @endpush

    @push('scripts')
        <script>
            // Global variables
            let selectedSales = [];

            // Initialize page
            document.addEventListener('DOMContentLoaded', function() {
                initializeCheckboxes();
                initializeTooltips();
            });

            // Checkbox functionality
            function initializeCheckboxes() {
                const selectAllCheckbox = document.getElementById('selectAll');
                const saleCheckboxes = document.querySelectorAll('.sale-checkbox');

                selectAllCheckbox.addEventListener('change', function() {
                    saleCheckboxes.forEach(checkbox => {
                        checkbox.checked = this.checked;
                    });
                    updateSelectedSales();
                });

                saleCheckboxes.forEach(checkbox => {
                    checkbox.addEventListener('change', updateSelectedSales);
                });
            }

            function updateSelectedSales() {
                const checkboxes = document.querySelectorAll('.sale-checkbox:checked');
                selectedSales = Array.from(checkboxes).map(cb => cb.value);

                const selectedCount = selectedSales.length;
                document.getElementById('selectedCount').textContent = selectedCount;

                const bulkActionsBar = document.getElementById('bulkActionsBar');
                if (selectedCount > 0) {
                    bulkActionsBar.style.display = 'flex';
                } else {
                    bulkActionsBar.style.display = 'none';
                }

                // Update select all checkbox state
                const selectAllCheckbox = document.getElementById('selectAll');
                const totalCheckboxes = document.querySelectorAll('.sale-checkbox').length;

                if (selectedCount === 0) {
                    selectAllCheckbox.indeterminate = false;
                    selectAllCheckbox.checked = false;
                } else if (selectedCount === totalCheckboxes) {
                    selectAllCheckbox.indeterminate = false;
                    selectAllCheckbox.checked = true;
                } else {
                    selectAllCheckbox.indeterminate = true;
                }
            }

            function clearSelection() {
                document.querySelectorAll('.sale-checkbox').forEach(cb => cb.checked = false);
                document.getElementById('selectAll').checked = false;
                updateSelectedSales();
            }

            // Filter functions
            function setDateFilter(period) {
                const startDateInput = document.querySelector('input[name="start_date"]');
                const endDateInput = document.querySelector('input[name="end_date"]');
                const today = new Date();

                let startDate, endDate;

                switch (period) {
                    case 'today':
                        startDate = endDate = today.toISOString().split('T')[0];
                        break;
                    case 'yesterday':
                        const yesterday = new Date(today);
                        yesterday.setDate(yesterday.getDate() - 1);
                        startDate = endDate = yesterday.toISOString().split('T')[0];
                        break;
                    case 'week':
                        const weekStart = new Date(today);
                        weekStart.setDate(today.getDate() - today.getDay());
                        startDate = weekStart.toISOString().split('T')[0];
                        endDate = today.toISOString().split('T')[0];
                        break;
                    case 'month':
                        startDate = new Date(today.getFullYear(), today.getMonth(), 1).toISOString().split('T')[0];
                        endDate = today.toISOString().split('T')[0];
                        break;
                    case 'last_month':
                        const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
                        const lastMonthEnd = new Date(today.getFullYear(), today.getMonth(), 0);
                        startDate = lastMonth.toISOString().split('T')[0];
                        endDate = lastMonthEnd.toISOString().split('T')[0];
                        break;
                }

                startDateInput.value = startDate;
                endDateInput.value = endDate;
                document.getElementById('filterForm').submit();
            }

            function clearFilters() {
                const form = document.getElementById('filterForm');
                form.querySelectorAll('input, select').forEach(input => {
                    if (input.type === 'checkbox' || input.type === 'radio') {
                        input.checked = false;
                    } else {
                        input.value = '';
                    }
                });
                form.submit();
            }

            function changePerPage(perPage) {
                const url = new URL(window.location);
                url.searchParams.set('per_page', perPage);
                url.searchParams.delete('page'); // Reset to first page
                window.location.href = url.toString();
            }

            // Bulk actions
            function bulkDelete() {
                if (selectedSales.length === 0) return;

                if (confirm(`هل أنت متأكد من حذف ${selectedSales.length} عملية بيع؟`)) {
                    // Implementation for bulk delete
                    console.log('Bulk delete:', selectedSales);
                }
            }

            function bulkPrint() {
                if (selectedSales.length === 0) return;

                selectedSales.forEach(saleId => {
                    const printUrl = `{{ user_route('sales.print', ':id') }}`.replace(':id', saleId);
                    window.open(printUrl, '_blank');
                });
            }

            function bulkExport() {
                if (selectedSales.length === 0) return;

                const exportUrl = `{{ user_route('sales.index') }}?export=excel&ids=${selectedSales.join(',')}`;
                window.location.href = exportUrl;
            }

            // Export functions
            function exportSales(format) {
                const url = new URL(window.location);
                url.searchParams.set('export', format);
                window.location.href = url.toString();
            }

            // Sort functionality
            function sortTable(column) {
                const url = new URL(window.location);
                const currentSort = url.searchParams.get('sort');
                const currentDirection = url.searchParams.get('direction') || 'asc';

                if (currentSort === column) {
                    url.searchParams.set('direction', currentDirection === 'asc' ? 'desc' : 'asc');
                } else {
                    url.searchParams.set('sort', column);
                    url.searchParams.set('direction', 'asc');
                }

                window.location.href = url.toString();
            }

            // Initialize tooltips
            function initializeTooltips() {
                const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
                tooltipTriggerList.map(function(tooltipTriggerEl) {
                    return new bootstrap.Tooltip(tooltipTriggerEl);
                });
            }

            // Auto-refresh functionality (optional)
            function enableAutoRefresh(interval = 30000) {
                setInterval(() => {
                    if (selectedSales.length === 0) {
                        window.location.reload();
                    }
                }, interval);
            }
        </script>
    @endpush
</x-app-layout>
