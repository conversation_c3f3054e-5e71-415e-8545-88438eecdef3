<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class InventoryTransferItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'inventory_transfer_id',
        'product_id',
        'requested_quantity',
        'approved_quantity',
        'received_quantity',
        'unit_cost',
        'total_cost',
        'notes',
        'damage_notes',
    ];

    protected $casts = [
        'requested_quantity' => 'decimal:2',
        'approved_quantity' => 'decimal:2',
        'received_quantity' => 'decimal:2',
        'unit_cost' => 'decimal:2',
        'total_cost' => 'decimal:2',
    ];

    // Relationships
    public function inventoryTransfer(): BelongsTo
    {
        return $this->belongsTo(InventoryTransfer::class);
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    // Helper methods
    public function getVarianceQuantity(): float
    {
        if ($this->received_quantity === null || $this->approved_quantity === null) {
            return 0;
        }
        return $this->received_quantity - $this->approved_quantity;
    }

    public function hasVariance(): bool
    {
        return abs($this->getVarianceQuantity()) > 0.01; // Allow for small floating point differences
    }

    public function getVariancePercentage(): float
    {
        if ($this->approved_quantity == 0) {
            return 0;
        }
        return ($this->getVarianceQuantity() / $this->approved_quantity) * 100;
    }

    public function isShortage(): bool
    {
        return $this->getVarianceQuantity() < 0;
    }

    public function isOverage(): bool
    {
        return $this->getVarianceQuantity() > 0;
    }

    public function getStatusText(): string
    {
        if ($this->received_quantity === null) {
            return $this->approved_quantity === null ? 'في الانتظار' : 'موافق عليه';
        }

        if ($this->hasVariance()) {
            return $this->isShortage() ? 'نقص' : 'زيادة';
        }

        return 'مطابق';
    }

    public function getStatusBadgeClass(): string
    {
        if ($this->received_quantity === null) {
            return $this->approved_quantity === null ? 'bg-warning' : 'bg-info';
        }

        if ($this->hasVariance()) {
            return $this->isShortage() ? 'bg-danger' : 'bg-warning';
        }

        return 'bg-success';
    }

    // Calculate total cost when approved quantity and unit cost are set
    public function calculateTotalCost(): void
    {
        if ($this->approved_quantity && $this->unit_cost) {
            $this->total_cost = $this->approved_quantity * $this->unit_cost;
        }
    }

    // Auto-calculate total cost when saving
    protected static function booted()
    {
        static::saving(function ($item) {
            $item->calculateTotalCost();
        });
    }
}
